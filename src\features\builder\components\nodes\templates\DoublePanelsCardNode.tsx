/* eslint-disable react-hooks/rules-of-hooks */
import React, { useState, useCallback, useRef } from "react";
import { NodeRendererProps } from "../NodeFactory";
import { ImageLoader } from "@/components/image/ImageLoader";
import { cn } from "@/lib/utils";
import { compareNodeWithProperties } from '@/lib/memo-utils';
import { useNodeOperations } from "../../../context/hooks";
import { useInlineTextEditor } from "../../../hooks/useInlineTextEditor";
import { Button } from "@/components/ui/button";
import { Type, Image as ImageIcon, GripVertical } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface PanelContentType {
  type: "text" | "image" | string;
  content: string;
  title?: string;
}

const DoublePanelsCardNodeComponent: React.FC<NodeRendererProps & React.HTMLAttributes<HTMLElement>> = ({
  node,
  ...htmlProps
}) => {
  const { updateNode } = useNodeOperations();

  // Get panel data from node properties
  const leftPanel: PanelContentType = {
    type: (node.properties?.leftPanelType as string) || "text",
    content: (node.properties?.leftPanelContent as string) || "",
    title: (node.properties?.leftPanelTitle as string) || "",
  };

  const rightPanel: PanelContentType = {
    type: (node.properties?.rightPanelType as string) || "text",
    content: (node.properties?.rightPanelContent as string) || "",
    title: (node.properties?.rightPanelTitle as string) || "",
  };

  const leftWidth = (node.properties?.leftWidth as number) || 50;
  const rightWidth = 100 - leftWidth;

  const [isDragging, setIsDragging] = useState(false);
  const [showLeftDropdown, setShowLeftDropdown] = useState(false);
  const [showRightDropdown, setShowRightDropdown] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent triggering editor's drag system
    setIsDragging(true);

    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;

      const rect = containerRef.current.getBoundingClientRect();
      const newLeftWidth = Math.max(10, Math.min(90, ((e.clientX - rect.left) / rect.width) * 100));

      // Update node properties through the builder context
      updateNode(node.id, {
        properties: {
          ...node.properties,
          leftWidth: Math.round(newLeftWidth),
        },
      });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [updateNode, node.id, node.properties]);

  // Helper functions to update panel properties
  const updatePanelProperty = useCallback((panel: 'left' | 'right', property: string, value: string) => {
    const propertyKey = `${panel}Panel${property.charAt(0).toUpperCase() + property.slice(1)}`;
    updateNode(node.id, {
      properties: {
        ...node.properties,
        [propertyKey]: value,
      },
    });
  }, [updateNode, node.id, node.properties]);

  const renderPanelContent = (panel: PanelContentType, side: 'left' | 'right') => {
    const showDropdown = side === 'left' ? showLeftDropdown : showRightDropdown;
    const setShowDropdown = side === 'left' ? setShowLeftDropdown : setShowRightDropdown;

    // Inline text editor for text content
    const handleSaveContent = useCallback((newContent: string) => {
      updatePanelProperty(side, 'content', newContent);
    }, [side, updatePanelProperty]);

    const handleSaveTitle = useCallback((newTitle: string) => {
      updatePanelProperty(side, 'title', newTitle);
    }, [side, updatePanelProperty]);

    const {
      isEditing: isEditingContent,
      elementRef: contentRef,
      startEditing: startEditingContent,
      handleKeyDown: handleContentKeyDown,
      handleBlur: handleContentBlur
    } = useInlineTextEditor({
      initialContent: panel.content || "",
      onSave: handleSaveContent,
      multiline: true
    });

    const {
      isEditing: isEditingTitle,
      elementRef: titleRef,
      startEditing: startEditingTitle,
      handleKeyDown: handleTitleKeyDown,
      handleBlur: handleTitleBlur
    } = useInlineTextEditor({
      initialContent: panel.title || "",
      onSave: handleSaveTitle,
      multiline: false
    });

    return (
      <div className="relative h-full group">
        {/* Content Type Selector */}
        <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <DropdownMenu open={showDropdown} onOpenChange={setShowDropdown}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 w-8 p-0 bg-white/90 backdrop-blur-sm">
                {panel.type === 'text' ? <Type className="h-4 w-4" /> : <ImageIcon className="h-4 w-4" />}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => updatePanelProperty(side, 'type', 'text')}>
                <Type className="mr-2 h-4 w-4" />
                Text
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => updatePanelProperty(side, 'type', 'image')}>
                <ImageIcon className="mr-2 h-4 w-4" />
                Image
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Panel Content */}
        <div className="h-full">
          {panel.type === "image" ? (
            <div
              className="w-full h-full cursor-pointer"
              onClick={() => updatePanelProperty(side, 'type', 'text')}
              title="Click to switch to text mode"
            >
              <ImageLoader
                src={panel.content}
                alt={panel.title}
                className="w-full h-full object-cover rounded-md"
                fallbackText={panel.title || "Click to add image"}
              />
            </div>
          ) : (
            <div className="h-full p-6 flex items-center justify-center">
              <div className="text-center space-y-4">
                {panel.title && (
                  <div
                    ref={titleRef}
                    className={cn(
                      "text-[64px] font-medium whitespace-nowrap text-[#0177BD] text-center cursor-pointer outline-none",
                      isEditingTitle && "ring-2 ring-blue-500 ring-opacity-50"
                    )}
                    contentEditable={isEditingTitle}
                    suppressContentEditableWarning={true}
                    onDoubleClick={startEditingTitle}
                    onKeyDown={handleTitleKeyDown}
                    onBlur={handleTitleBlur}
                    style={{
                      minHeight: '1em',
                      whiteSpace: 'pre-wrap'
                    }}
                  >
                    {panel.title || '\u00A0'}
                  </div>
                )}
                <div
                  ref={contentRef}
                  className={cn(
                    "text-gray-700 text-sm leading-relaxed whitespace-pre-line cursor-pointer outline-none",
                    isEditingContent && "ring-2 ring-blue-500 ring-opacity-50"
                  )}
                  contentEditable={isEditingContent}
                  suppressContentEditableWarning={true}
                  onDoubleClick={startEditingContent}
                  onKeyDown={handleContentKeyDown}
                  onBlur={handleContentBlur}
                  style={{
                    minHeight: '1em',
                    whiteSpace: 'pre-wrap'
                  }}
                >
                  {panel.content || "Click to add text"}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "overflow-hidden transition-all duration-300 bg-transparent min-h-64 h-max relative",
        node.style?.className
      )}
      style={node.style?.css}
      {...htmlProps}
    >
      <div className="flex h-full items-center relative">
        {/* Left Panel */}
        <div
          className="relative h-full"
          style={{ width: `${leftWidth}%` }}
        >
          {renderPanelContent(leftPanel, 'left')}
        </div>

        {/* Resize Divider */}
        <div
          className={cn(
            "w-1 h-full bg-gray-300 cursor-col-resize hover:bg-blue-500 transition-colors relative group",
            isDragging && "bg-blue-500"
          )}
          onMouseDown={handleMouseDown}
          onClick={(e) => e.stopPropagation()} // Prevent click propagation
        >
          <div className="absolute inset-y-0 -left-1 -right-1 flex items-center justify-center">
            <GripVertical className="h-4 w-4 text-gray-500 group-hover:text-blue-500 transition-colors" />
          </div>
        </div>

        {/* Right Panel */}
        <div
          className="relative h-full"
          style={{ width: `${rightWidth}%` }}
        >
          {renderPanelContent(rightPanel, 'right')}
        </div>
      </div>
    </div>
  );
};

// Memoized template node to prevent re-renders
export const DoublePanelsCardNode = React.memo(
  DoublePanelsCardNodeComponent,
  compareNodeWithProperties
);
