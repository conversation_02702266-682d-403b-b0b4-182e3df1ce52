import React, { useMemo, useCallback } from 'react';
import { TreeNode } from '../../../../context/types';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import { safeMatch, combineClassNames } from '../../utils/classNameUtils';

interface DisplaySectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const DISPLAY_OPTIONS = [
  { value: 'block', label: 'Block' },
  { value: 'inline-block', label: 'Inline Block' },
  { value: 'inline', label: 'Inline' },
  { value: 'flex', label: 'Flex' },
  { value: 'inline-flex', label: 'Inline Flex' },
  { value: 'grid', label: 'Grid' },
  { value: 'inline-grid', label: 'Inline Grid' },
  { value: 'contents', label: 'Contents' },
  { value: 'list-item', label: 'List Item' },
  { value: 'hidden', label: 'Hidden' },
  { value: 'table', label: 'Table' },
  { value: 'table-caption', label: 'Table Caption' },
  { value: 'table-cell', label: 'Table Cell' },
  { value: 'table-column', label: 'Table Column' },
  { value: 'table-column-group', label: 'Table Column Group' },
  { value: 'table-footer-group', label: 'Table Footer Group' },
  { value: 'table-header-group', label: 'Table Header Group' },
  { value: 'table-row-group', label: 'Table Row Group' },
  { value: 'table-row', label: 'Table Row' }
];

const OVERFLOW_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'overflow-auto', label: 'Auto' },
  { value: 'overflow-hidden', label: 'Hidden' },
  { value: 'overflow-clip', label: 'Clip' },
  { value: 'overflow-visible', label: 'Visible' },
  { value: 'overflow-scroll', label: 'Scroll' }
];

const OVERFLOW_X_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'overflow-x-auto', label: 'Auto' },
  { value: 'overflow-x-hidden', label: 'Hidden' },
  { value: 'overflow-x-clip', label: 'Clip' },
  { value: 'overflow-x-visible', label: 'Visible' },
  { value: 'overflow-x-scroll', label: 'Scroll' }
];

const OVERFLOW_Y_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'overflow-y-auto', label: 'Auto' },
  { value: 'overflow-y-hidden', label: 'Hidden' },
  { value: 'overflow-y-clip', label: 'Clip' },
  { value: 'overflow-y-visible', label: 'Visible' },
  { value: 'overflow-y-scroll', label: 'Scroll' }
];

const VISIBILITY_OPTIONS = [
  { value: 'visible', label: 'Hiển thị' },
  { value: 'invisible', label: 'Ẩn (giữ không gian)' },
  { value: 'collapse', label: 'Thu gọn' }
];

const removeDisplayClasses = (className: string): string => {
  return className
    .split(' ')
    .filter(cls => 
      !cls.startsWith('block') &&
      !cls.startsWith('inline') &&
      !cls.startsWith('flex') &&
      !cls.startsWith('grid') &&
      !cls.startsWith('hidden') &&
      !cls.startsWith('table') &&
      !cls.startsWith('contents') &&
      !cls.startsWith('list-item') &&
      !cls.startsWith('overflow') &&
      !cls.startsWith('invisible') &&
      !cls.startsWith('collapse')
    )
    .join(' ')
    .trim();
};

const DisplaySectionComponent: React.FC<DisplaySectionProps> = ({ node, onChange, disabled }) => {
  const currentClassName = node.style?.className || '';
  
  const currentState = useMemo(() => {
    // Detect display type
    let display = 'block'; // default
    
    if (currentClassName.includes('hidden')) display = 'hidden';
    else if (currentClassName.includes('inline-flex')) display = 'inline-flex';
    else if (currentClassName.includes('inline-block')) display = 'inline-block';
    else if (currentClassName.includes('inline-grid')) display = 'inline-grid';
    else if (currentClassName.includes('inline')) display = 'inline';
    else if (currentClassName.includes('flex')) display = 'flex';
    else if (currentClassName.includes('grid')) display = 'grid';
    else if (currentClassName.includes('contents')) display = 'contents';
    else if (currentClassName.includes('list-item')) display = 'list-item';
    else if (currentClassName.includes('table-caption')) display = 'table-caption';
    else if (currentClassName.includes('table-cell')) display = 'table-cell';
    else if (currentClassName.includes('table-column-group')) display = 'table-column-group';
    else if (currentClassName.includes('table-footer-group')) display = 'table-footer-group';
    else if (currentClassName.includes('table-header-group')) display = 'table-header-group';
    else if (currentClassName.includes('table-row-group')) display = 'table-row-group';
    else if (currentClassName.includes('table-column')) display = 'table-column';
    else if (currentClassName.includes('table-row')) display = 'table-row';
    else if (currentClassName.includes('table')) display = 'table';

    const overflow = safeMatch(currentClassName, /\boverflow-(auto|hidden|clip|visible|scroll)\b/, 'default');
    const overflowX = safeMatch(currentClassName, /\boverflow-x-(auto|hidden|clip|visible|scroll)\b/, 'default');
    const overflowY = safeMatch(currentClassName, /\boverflow-y-(auto|hidden|clip|visible|scroll)\b/, 'default');
    
    const visibility = currentClassName.includes('invisible') ? 'invisible' :
                      currentClassName.includes('collapse') ? 'collapse' : 'visible';

    return { display, overflow, overflowX, overflowY, visibility };
  }, [currentClassName]);

  const cleanClassName = useMemo(() => {
    return removeDisplayClasses(currentClassName);
  }, [currentClassName]);

  const updateClassName = (newClassName: string) => {
    onChange({
      style: {
        ...node.style,
        className: newClassName
      }
    });
  };

  const handleChange = useCallback((type: string, value: string) => {
    const newState = { ...currentState, [type]: value };
    
    const classes = [
      cleanClassName,
      newState.display !== 'block' ? newState.display : '',
      newState.overflow !== 'default' ? newState.overflow : '',
      newState.overflowX !== 'default' ? newState.overflowX : '',
      newState.overflowY !== 'default' ? newState.overflowY : '',
      newState.visibility !== 'visible' ? newState.visibility : ''
    ].filter(Boolean);
    
    updateClassName(combineClassNames(...classes));
  }, [cleanClassName, currentState]);

  return (
    <div className="space-y-3">
      {/* Display Type */}
      <Select 
        value={currentState.display} 
        onValueChange={(value) => handleChange('display', value)}
        disabled={disabled}
      >
        <SelectTrigger>
          <SelectValue placeholder="Kiểu hiển thị" />
        </SelectTrigger>
        <SelectContent>
          {DISPLAY_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Overflow Controls */}
      <div className="flex gap-2">
        <Select 
          value={currentState.overflow} 
          onValueChange={(value) => handleChange('overflow', value)}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Overflow" />
          </SelectTrigger>
          <SelectContent>
            {OVERFLOW_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select 
          value={currentState.visibility} 
          onValueChange={(value) => handleChange('visibility', value)}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Hiển thị" />
          </SelectTrigger>
          <SelectContent>
            {VISIBILITY_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Overflow X & Y */}
      <div className="flex gap-2">
        <Select 
          value={currentState.overflowX} 
          onValueChange={(value) => handleChange('overflowX', value)}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Overflow X" />
          </SelectTrigger>
          <SelectContent>
            {OVERFLOW_X_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select 
          value={currentState.overflowY} 
          onValueChange={(value) => handleChange('overflowY', value)}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Overflow Y" />
          </SelectTrigger>
          <SelectContent>
            {OVERFLOW_Y_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export const DisplaySection = React.memo(DisplaySectionComponent);
