{"permissions": {"allow": ["Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(node:*)", "Bash(find:*)", "Bash(rg:*)", "WebFetch(domain:help.figma.com)", "Bash(/home/<USER>/.nvm/versions/node/v24.1.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"TODO|FIXME|XXX|HACK\" /mnt/d/KHA/FE/kha-demo/src/features/builder/pages/WebBuilder.tsx)", "Bash(git add:*)", "Bash(/home/<USER>/.nvm/versions/node/v24.1.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"selectedNodeId.*=.*null\" src/features/builder --type tsx --type ts)", "Bash(/home/<USER>/.nvm/versions/node/v24.1.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"selectedNodeId.*=.*null\" src/features/builder)", "<PERSON><PERSON>(chmod:*)", "Bash(npx typescript:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(touch:*)", "Bash(tsc --noEmit)", "Bash(./node_modules/.bin/tsc:*)", "Bash(tree:*)", "<PERSON><PERSON>(tsx test:*)", "Bash(ts-node:*)", "Bash(NODE_OPTIONS=\"--max-old-space-size=4096\" yarn build)", "Bash(npx tsx:*)", "Bash(yarn typecheck)", "Bash(yarn build)", "Bash(yarn tsc:*)", "Bash(yarn build)", "<PERSON><PERSON>(tsc:*)", "WebFetch(domain:tailwindcss.com)", "Bash(yarn tsc:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "Bash(npm run test:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(npm install)", "Bash(cp:*)", "<PERSON><PERSON>(sed:*)", "Bash(for file in /mnt/d/KHA/FE/kha-demo/src/features/builder/forms/sections/*.tsx)", "Bash(do)", "Bash(done)", "Bash(yarn dev:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(diff:*)"], "deny": []}}