// Border helper functions - Pure logic without UI

import type { BorderState, BorderMode, BorderStyle } from "./types";
import { BORDER_PREFIX_MAP } from "./types";
import { memoize, borderParseCache } from "../../../../../utils/cache";

/**
 * Check if a string is a valid Tailwind color className segment
 * Examples: "red-500", "gray-200", "blue-600", "slate-950"
 */
function isColorClassName(segment: string): boolean {
  // Common Tailwind color names
  const colorNames = [
    "slate",
    "gray",
    "zinc",
    "neutral",
    "stone",
    "red",
    "orange",
    "amber",
    "yellow",
    "lime",
    "green",
    "emerald",
    "teal",
    "cyan",
    "sky",
    "blue",
    "indigo",
    "violet",
    "purple",
    "fuchsia",
    "pink",
    "rose",
    "black",
    "white",
    "transparent",
    "current",
  ];

  // Check for color-number pattern (e.g., "red-500")
  const colorNumberPattern = new RegExp(
    `^(${colorNames.join("|")})-(\\d{2,3})$`
  );
  if (colorNumberPattern.test(segment)) return true;

  // Check for single color names (black, white, transparent, current)
  if (["black", "white", "transparent", "current"].includes(segment))
    return true;

  // Check for arbitrary color values [#hex] or [rgb(...)]
  if (segment.startsWith("[") && segment.endsWith("]")) return true;

  return false;
}

/**
 * Check if a string is a valid border size className segment
 * Examples: "0", "1", "2", "4", "8", "[3px]"
 */
function isSizeClassName(segment: string): boolean {
  // Valid Tailwind border size values
  const validSizes = ["0", "1", "2", "4", "8"];
  if (validSizes.includes(segment)) return true;

  // Check for arbitrary size values [3px], [0.5rem], etc.
  if (segment.startsWith("[") && segment.endsWith("]")) {
    // Could be either size or color, so check if it contains size units
    const sizeUnits = ["px", "rem", "em", "%", "vw", "vh"];
    return sizeUnits.some((unit) => segment.includes(unit));
  }

  return false;
}

// removeBorderClasses function moved to utils/classNameUtils.ts to avoid duplicate exports

/**
 * Parse current border state from className
 * Returns 'custom' mode if border classes exist but don't match expected patterns
 */
export const parseBorderState = memoize(
  (className: string): BorderState => {
    const classes = className.trim().split(/\s+/);

    // Default state
    const state: BorderState = {
      enabled: false,
      mode: "none",
      style: "solid",
      options: [],
    };

    // Check if border is enabled
    state.enabled = classes.some((c) => c.startsWith("border"));
    if (!state.enabled) return state;

    // Step 1: Detect mode based on className patterns
    if (
      classes.some(
        (c) =>
          c === "border-t" ||
          c === "border-r" ||
          c === "border-b" ||
          c === "border-l" ||
          c.startsWith("border-t-") ||
          c.startsWith("border-r-") ||
          c.startsWith("border-b-") ||
          c.startsWith("border-l-")
      )
    ) {
      state.mode = "individual";
    } else if (
      classes.some((c) => c === "border-y" || c.startsWith("border-y-"))
    ) {
      state.mode = "vertical";
    } else if (
      classes.some((c) => c === "border-x" || c.startsWith("border-x-"))
    ) {
      state.mode = "horizontal";
    } else {
      state.mode = "all";
    }

    // Step 2: Detect style
    const styles = ["solid", "dashed", "dotted", "double"];
    for (const style of styles) {
      if (classes.includes(`border-${style}`)) {
        state.style = style as BorderStyle;
        break;
      }
    }

    // Step 3: Extract values based on mode prefixes
    const borderPrefixes = BORDER_PREFIX_MAP[state.mode];

    state.options = borderPrefixes.map((borderPrefix) => {
      let size = "";
      let color = "";

      // Find all classes that start with this border prefix
      const prefixClasses = classes.filter((c) => c.startsWith(borderPrefix));

      for (const className of prefixClasses) {
        if (className === borderPrefix) {
          // Just "border", "border-t", etc. without suffix means size 1
          size = "1";
        } else if (className.startsWith(`${borderPrefix}-`)) {
          // Extract the suffix after the prefix
          const suffix = className.substring(borderPrefix.length + 1);

          // In Tailwind CSS, valid patterns are:
          // border-{size} -> border-2, border-4
          // border-{color} -> border-red-500, border-blue-600
          // NOT border-2-red-500 (this is invalid in Tailwind)

          // Check if it's a size
          if (isSizeClassName(suffix)) {
            size = suffix;
          }
          // Check if it's a color
          else if (isColorClassName(suffix)) {
            color = suffix;
          }
        }
      }

      return { size, color };
    });

    // Step 4: Validate if we have enough information for the detected mode
    if (
      state.enabled &&
      (state.mode as string) !== "custom" &&
      (state.mode as string) !== "none"
    ) {
      let hasEnoughInfo = false;

      switch (state.mode) {
        case "all":
          // For "all" mode, we need at least one complete option with both size and color
          hasEnoughInfo =
            state.options.length > 0 &&
            state.options.some((opt) => opt.size && opt.color);
          break;

        case "vertical":
        case "horizontal":
          // For directional modes, we need complete info
          hasEnoughInfo =
            state.options.length > 0 &&
            state.options.every((opt) => opt.size && opt.color);
          break;

        case "individual":
          // For individual mode, we should have 4 options or at least some complete ones
          hasEnoughInfo =
            state.options.length > 0 &&
            state.options.some((opt) => opt.size && opt.color);
          break;

        default:
          hasEnoughInfo = true;
      }

      // If we don't have enough information, mark as custom mode
      if (!hasEnoughInfo) {
        state.mode = "custom";
      }
    }

    // Check for patterns we don't support
    const hasUnsupportedPatterns = classes.some((c) => {
      // Check for border-{side}-{style} patterns (e.g., border-t-dotted)
      if (/^border-[trbl]-(solid|dashed|dotted|double)$/.test(c)) return true;
      // Check for complex arbitrary values we can't parse
      if (c.includes("[") && c.includes("]") && c.startsWith("border"))
        return true;
      // Check for border-collapse, border-separate, border-spacing
      if (/^border-(collapse|separate|spacing)/.test(c)) return true;
      return false;
    });

    if (hasUnsupportedPatterns) {
      state.mode = "custom";
    }

    return state;
  },
  borderParseCache,
  (className) => `border:${className}`
);

/**
 * Convert BorderState to Tailwind CSS classes
 */
export function borderStateToClassName(state: BorderState): string {
  if (!state.enabled || state.mode === "none") return "";

  // For custom mode, we don't generate any classes
  // The UI should preserve existing border classes
  if (state.mode === "custom") {
    return ""; // Return empty, let UI handle preserving existing classes
  }

  const classNames: string[] = [];

  // Border style: chỉ có 1 class, luôn áp dụng cho toàn bộ
  if (state.style && state.style !== "solid") {
    classNames.push(`border-${state.style}`);
  }

  // Lấy prefix theo mode
  const borderPrefixes = BORDER_PREFIX_MAP[state.mode];

  // Duyệt từng option (option i tương ứng prefix i)
  state.options.forEach((opt, idx) => {
    // Border size
    if (opt.size) {
      const prefix = borderPrefixes[idx] || borderPrefixes[0]; // tránh undefined
      classNames.push(opt.size === "1" ? prefix : `${prefix}-${opt.size}`);
    }
    // Border color
    if (opt.color) {
      const prefix = borderPrefixes[idx] || borderPrefixes[0];
      classNames.push(`${prefix}-${opt.color}`);
    }
  });

  return classNames.join(" ");
}

/**
 * Create default BorderState for specific mode
 */
function createDefaultBorderStateForMode(mode: BorderMode): BorderState {
  if (mode === "none") {
    return {
      enabled: false,
      mode: "none",
      style: "solid",
      options: [],
    };
  }

  const optionCount = mode === "individual" ? 4 : 1;

  return {
    enabled: true,
    mode,
    style: "solid",
    options: Array(optionCount)
      .fill(null)
      .map(() => ({
        size: "1",
        color: "gray-300",
      })),
  };
}

/**
 * Switch border mode and copy values from first option when possible
 */
export function switchBorderMode(
  oldState: BorderState,
  newMode: BorderMode
): BorderState {
  // Don't allow switching to custom mode - it's only a fallback state
  if (newMode === "custom") {
    return oldState;
  }

  // If switching to none mode, return disabled state
  if (newMode === "none") {
    return createDefaultBorderStateForMode("none");
  }

  // If switching from none or custom mode, create default state for new mode
  if (oldState.mode === "none" || oldState.mode === "custom") {
    return createDefaultBorderStateForMode(newMode);
  }

  // For other modes, preserve values from first option when possible
  const newState = createDefaultBorderStateForMode(newMode);
  newState.style = oldState.style; // Preserve style

  // Copy values from first option if available
  const firstOption = oldState.options[0];
  if (firstOption && firstOption.size && firstOption.color) {
    newState.options = newState.options.map(() => ({
      size: firstOption.size,
      color: firstOption.color,
    }));
  }

  return newState;
}
