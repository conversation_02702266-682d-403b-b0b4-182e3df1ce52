import React, { useEffect, useState, useCallback, useRef } from 'react';
import { ObserverManager } from '../utils/ObserverManager';
import { ElementFinder } from '../utils/ElementFinder';
import { Plus } from 'lucide-react';

interface DropTargetOverlayProps {
  nodeId: string;
  getNodeElement: (nodeId: string) => HTMLElement | undefined;
  containerElement: HTMLElement;
}

export const DropTargetOverlay: React.FC<DropTargetOverlayProps> = ({
  nodeId,
  getNodeElement,
  containerElement,
}) => {
  const [rect, setRect] = useState<DOMRect | null>(null);
  const observerManager = useRef(ObserverManager.getInstance());
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedUpdateRect = useCallback(() => {
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
    
    updateTimeoutRef.current = setTimeout(() => {
      try {
        const wrapperElement = getNodeElement(nodeId);
        if (!wrapperElement || !containerElement) {
          setRect(null);
          return;
        }

        const targetElement = ElementFinder.findActualElement(wrapperElement, nodeId);
        if (!targetElement) {
          setRect(null);
          return;
        }

        const relativeRect = ElementFinder.calculateRelativePosition(
          targetElement, 
          containerElement
        );
        
        setRect(relativeRect);
      } catch {
        setRect(null);
      }
    }, 16);
  }, [nodeId, getNodeElement, containerElement]);

  useEffect(() => {
    const wrapperElement = getNodeElement(nodeId);
    if (!wrapperElement) return;

    debouncedUpdateRect();

    const targetElement = ElementFinder.findActualElement(wrapperElement);
    if (targetElement) {
      observerManager.current.observe(nodeId, targetElement, debouncedUpdateRect);
    }

    const containerObserver = new ResizeObserver(debouncedUpdateRect);
    containerObserver.observe(containerElement);

    const handleScrollResize = () => debouncedUpdateRect();
    containerElement.addEventListener('scroll', handleScrollResize, { passive: true });
    window.addEventListener('resize', handleScrollResize, { passive: true });

    let contentChangeTimeout: NodeJS.Timeout;
    const handleContentChange = () => {
      if (contentChangeTimeout) clearTimeout(contentChangeTimeout);
      contentChangeTimeout = setTimeout(debouncedUpdateRect, 100);
    };
    window.addEventListener('builder-content-changed', handleContentChange);

    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      if (contentChangeTimeout) {
        clearTimeout(contentChangeTimeout);
      }
      
      if (targetElement) {
        observerManager.current.unobserve(nodeId, targetElement);
      }
      containerObserver.disconnect();
      containerElement.removeEventListener('scroll', handleScrollResize);
      window.removeEventListener('resize', handleScrollResize);
      window.removeEventListener('builder-content-changed', handleContentChange);
    };
  }, [nodeId, getNodeElement, containerElement, debouncedUpdateRect]);

  if (!rect || rect.width <= 0 || rect.height <= 0) {
    return null;
  }

  return (
    <div
      className="absolute bg-green-500/10 pointer-events-none flex items-center justify-center"
      style={{
        left: `${Math.round(rect.left)}px`,
        top: `${Math.round(rect.top)}px`,
        width: `${Math.round(rect.width)}px`,
        height: `${Math.round(rect.height)}px`,
      }}
    >
      <Plus className="w-5 h-5 text-green-600" />
    </div>
  );
};