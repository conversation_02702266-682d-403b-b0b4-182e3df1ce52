import React, { use<PERSON>emo, useCallback } from 'react';
import { TreeNode } from '../../../../context/types';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import { safeMatch, combineClassNames } from '../../utils/classNameUtils';

interface ImageSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const OBJECT_FIT_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'object-contain', label: 'Vừa khít' },
  { value: 'object-cover', label: 'Che phủ' },
  { value: 'object-fill', label: 'Kéo giãn' },
  { value: 'object-none', label: '<PERSON>ích thước gốc' },
  { value: 'object-scale-down', label: 'Thu nhỏ' }
];

const OBJECT_POSITION_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'object-bottom', label: 'Dưới' },
  { value: 'object-center', label: 'Giữa' },
  { value: 'object-left', label: 'Trái' },
  { value: 'object-left-bottom', label: 'Trái dưới' },
  { value: 'object-left-top', label: 'Trái trên' },
  { value: 'object-right', label: 'Phải' },
  { value: 'object-right-bottom', label: 'Phải dưới' },
  { value: 'object-right-top', label: 'Phải trên' },
  { value: 'object-top', label: 'Trên' }
];

const ASPECT_RATIO_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'aspect-square', label: 'Vuông (1:1)' },
  { value: 'aspect-video', label: 'Video (16:9)' },
  { value: 'aspect-auto', label: 'Tự động' },
  { value: 'aspect-[4/3]', label: '4:3' },
  { value: 'aspect-[3/2]', label: '3:2' },
  { value: 'aspect-[5/4]', label: '5:4' },
  { value: 'aspect-[2/1]', label: '2:1' },
  { value: 'aspect-[3/1]', label: '3:1' }
];

// Helper functions following border pattern
const removeImageClasses = (className: string): string => {
  return className
    .split(/\s+/)
    .filter(cls => 
      !cls.startsWith('object-') &&
      !cls.startsWith('aspect-')
    )
    .join(' ')
    .trim();
};

const ImageSectionComponent: React.FC<ImageSectionProps> = ({ node, onChange, disabled }) => {
  const currentClassName = node.style?.className || '';
  
  const currentState = useMemo(() => {
    const objectFit = safeMatch(currentClassName, /\bobject-(contain|cover|fill|none|scale-down)\b/, 'default');
    const objectPosition = safeMatch(currentClassName, /\bobject-(bottom|center|left|left-bottom|left-top|right|right-bottom|right-top|top)\b/, 'default');
    const aspectRatio = safeMatch(currentClassName, /\baspect-\S+\b/, 'default');

    return { objectFit, objectPosition, aspectRatio };
  }, [currentClassName]);

  const cleanClassName = useMemo(() => {
    return removeImageClasses(currentClassName);
  }, [currentClassName]);

  const updateClassName = (newClassName: string) => {
    onChange({
      style: {
        ...node.style,
        className: newClassName
      }
    });
  };

  const handleChange = useCallback((type: string, value: string) => {
    const newState = { ...currentState, [type]: value };
    
    const classes = [
      cleanClassName,
      newState.objectFit !== 'default' ? newState.objectFit : '',
      newState.objectPosition !== 'default' ? newState.objectPosition : '',
      newState.aspectRatio !== 'default' ? newState.aspectRatio : ''
    ].filter(Boolean);
    
    updateClassName(combineClassNames(...classes));
  }, [cleanClassName, currentState]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        {/* Object Fit Column */}
        <div className="space-y-3">
          <div className="space-y-2">
            <Label htmlFor="object-fit-select" className="text-xs">Hiển thị</Label>
            <Select 
              value={currentState.objectFit} 
              onValueChange={(value) => handleChange('objectFit', value)}
              disabled={disabled}
            >
              <SelectTrigger id="object-fit-select">
                <SelectValue placeholder="Che phủ" />
              </SelectTrigger>
              <SelectContent>
                {OBJECT_FIT_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="aspect-ratio-select" className="text-xs">Tỷ lệ</Label>
            <Select 
              value={currentState.aspectRatio} 
              onValueChange={(value) => handleChange('aspectRatio', value)}
              disabled={disabled}
            >
              <SelectTrigger id="aspect-ratio-select">
                <SelectValue placeholder="Tự động" />
              </SelectTrigger>
              <SelectContent>
                {ASPECT_RATIO_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Object Position Column */}
        <div className="space-y-3">
          <div className="space-y-2">
            <Label htmlFor="object-position-select" className="text-xs">Vị trí</Label>
            <Select 
              value={currentState.objectPosition} 
              onValueChange={(value) => handleChange('objectPosition', value)}
              disabled={disabled}
            >
              <SelectTrigger id="object-position-select">
                <SelectValue placeholder="Giữa" />
              </SelectTrigger>
              <SelectContent>
                {OBJECT_POSITION_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ImageSection = React.memo(ImageSectionComponent);
