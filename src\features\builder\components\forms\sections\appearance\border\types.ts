// 1. Type và Mapping
export type BorderMode = "none" | "all" | "vertical" | "horizontal" | "individual" | "custom";
export type BorderStyle = "solid" | "dashed" | "dotted" | "double";

export type BorderPrefix =
  | "border"
  | "border-x"
  | "border-y"
  | "border-t"
  | "border-b"
  | "border-l"
  | "border-r";

export const BORDER_PREFIX_MAP = {
  all:        ['border'],
  vertical:   ['border-y'],
  horizontal: ['border-x'],
  individual: ['border-t', 'border-r', 'border-b', 'border-l'],
} as const;

export interface BorderState {
  enabled: boolean;
  mode: BorderMode;
  style: BorderStyle;
  options: Array<{
    size: string; // ví dụ: "2", "4"
    color: string; // ví dụ: "red-500"
  }>;
}
