import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ToggleLeft, ToggleRight } from "lucide-react";
import { useUI } from "../../context/UIContext";
import { TreeNode } from "../../context/types";
import { renderDisplayForm } from "../forms/registry/DisplayFormFactory";
import { AdvancedDisplayForm } from "../forms/layouts/AdvancedDisplayForm";

interface DisplayTabContentProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled: boolean;
}

/**
 * DisplayTabContent handles the content of the "Hiển thị" (Display) tab
 * Includes toggle between Basic/Advanced modes
 */
export const DisplayTabContent: React.FC<DisplayTabContentProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const { uiState, setPropertiesPanelAdvanced } = useUI();
  const showAdvanced = uiState?.temporary?.propertiesPanelAdvanced ?? false;

  return (
    <div className="p-3 pb-8">
      {/* Toggle button at the top right */}
      <div className="mb-3 flex justify-end">
        <Button
          onClick={() => setPropertiesPanelAdvanced(!showAdvanced)}
          variant="ghost"
          size="sm"
          className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50"
        >
          <div className="flex items-center gap-1">
            {showAdvanced ? (
              <ToggleRight className="h-3 w-3" />
            ) : (
              <ToggleLeft className="h-3 w-3" />
            )}
            <span className="w-12 text-left">
              {showAdvanced ? "Cơ bản" : "Nâng cao"}
            </span>
          </div>
        </Button>
      </div>

      {/* Form content */}
      {showAdvanced ? (
        <AdvancedDisplayForm
          node={node}
          onChange={onChange}
          disabled={disabled}
        />
      ) : (
        renderDisplayForm({ node, onChange, disabled })
      )}
    </div>
  );
};
