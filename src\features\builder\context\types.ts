import { Post as BasePost, Excerpt } from "../states/posts";

// Core TreeNode types
export type TreeNodeType =
  | "frame"
  | "text"
  | "image"
  | "video"
  | "imageWithCaption"
  | "dateTimeCard"
  | "doublePanelsCard"
  | "libraryDisplay"
  | "newsCustomGridDisplay"
  | "newsGridDisplay";
export type TreeNodeGroup = "basic" | "template" | "widget";

export type TreeNodeProperty =
  | boolean
  | boolean[]
  | number
  | number[]
  | string
  | string[]
  | null;

export interface TreeNode {
  id: string;
  label: string;
  type: TreeNodeType;
  group: TreeNodeGroup;
  style?: {
    className?: string;
    css?: React.CSSProperties;
    // Flexible className styling - each node decides its own keys
    variants?: Record<string, string>;
  };
  properties?: Record<string, TreeNodeProperty>;
  children: TreeNode[];
}

// Extend Post to include TreeNode content and required fields
export interface Post
  extends Omit<BasePost, "content" | "authorId" | "excerpt"> {
  content: TreeNode;
  author: {
    id: string;
    name: string;
    email: string;
  };
  excerpt?: Excerpt; // Make excerpt optional
}

// UI State types
export type ViewMode = "mobile" | "tablet" | "desktop";
export type EditorMode = "edit" | "preview" | "move";

// UI state that should persist across browser refreshes
export interface PersistentUIState {
  selectedNodeId: string | null;
  expandedNodeIds: Set<string>;
  viewMode: ViewMode;
  showTreePanel: boolean;
  showPropertiesPanel: boolean;
}

// UI state that only exists for current session
export interface SessionUIState {
  editorMode: EditorMode; // Always reset to 'edit' on load
  isDirty: boolean;
  lastSaved: number;
  copiedNode: TreeNode | null;
  lastCreatedNodeId: string | null; // Track newly created nodes
}

// Temporary UI state for editing workflows (can be tracked for undo/redo)
export interface TemporaryUIState {
  // Properties Panel States
  propertiesPanelMainTab?: "post" | "block"; // Thông tin vs Chi tiết
  propertiesPanelSubTab?: "display" | "content"; // Hiển thị vs Nội dung
  propertiesPanelAdvanced?: boolean; // Cơ bản vs Nâng cao
  propertiesPanelDisplaySection?: string; // Kích thước vs Bố cục vs ...

  // Legacy support
  propertiesPanelTab?: "basic" | "advanced";
  isEditingCSS?: boolean;
  focusedInputId?: string | null;
  selectedPropertyGroup?: string | null;
  isAdvancedMode?: boolean;
}

// Complete UI state (composite)
export interface BuilderUIState extends PersistentUIState, SessionUIState {
  temporary?: TemporaryUIState;
}

// Builder State
export interface BuilderState {
  post: Post | null;
  uiState: BuilderUIState;
}

// History types
export interface HistoryAction {
  type: string;
  payload: unknown;
  timestamp: number;
  description?: string;
}

// UI-specific history action
export interface UIHistoryAction extends HistoryAction {
  type: "UI_CHANGE";
  payload: {
    category: "persistent" | "temporary";
    changes: Partial<PersistentUIState> | Partial<TemporaryUIState>;
  };
}

export interface HistoryState {
  baseState: BuilderState;
  actions: HistoryAction[];
  currentIndex: number;
  snapshots: Map<number, BuilderState>;
}

// Storage types - only serialize persistent UI state
export interface SerializedUIState {
  selectedNodeId: string | null;
  expandedNodeIds: string[];
  viewMode: ViewMode;
  showTreePanel: boolean;
  showPropertiesPanel: boolean;
}

// Enhanced storage for temporary UI state tracking
export interface SerializedTemporaryUIState {
  // Properties Panel States
  propertiesPanelMainTab?: "post" | "block";
  propertiesPanelSubTab?: "display" | "content";
  propertiesPanelAdvanced?: boolean;
  propertiesPanelDisplaySection?: string;

  // Legacy support
  propertiesPanelTab?: "basic" | "advanced";
  isAdvancedMode?: boolean;
}

export interface StorageSchema {
  version: number;
  data: {
    post: Post;
    uiState: SerializedUIState;
    temporaryUIState?: SerializedTemporaryUIState;
    history?: {
      baseState: BuilderState;
      actions: HistoryAction[];
      currentIndex: number;
    };
  };
  metadata: {
    lastSaved: number;
    createdAt: number;
  };
}

// Storage error types
export type StorageErrorType =
  | "CORRUPT_DATA"
  | "VERSION_MISMATCH"
  | "PARSE_ERROR";

export interface StorageError {
  type: StorageErrorType;
  message: string;
  data?: unknown;
}

// Recovery options
export interface RecoveryOptions {
  clearAndNew: () => void;
  keepAndIgnore: () => void;
  tryRecover: () => void;
}
