import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { ProcessingTicket } from "../data/mockData";

/**
 * Props interface for ProcessingTicketDisplay component
 */
export interface ProcessingTicketDisplayProps {
  /** Array of tickets to display */
  tickets: ProcessingTicket[];
  /** Additional CSS classes */
  className?: string;
  /** Click handler for individual tickets */
  onTicketClick?: (ticket: ProcessingTicket) => void;
}

/**
 * ProcessingTicketDisplay component displays tickets in a 4x1 grid layout
 * 
 * @param props - Component props
 * @returns JSX element for processing ticket display
 */
export const ProcessingTicketDisplay: React.FC<ProcessingTicketDisplayProps> = ({
  tickets,
  className,
  onTicketClick,
}) => {
  /**
   * Get status configuration including color, icon, and label
   */
  const getStatusConfig = (status: ProcessingTicket["status"]) => {
    switch (status) {
      case "pending":
        return {
          color: "bg-yellow-100 text-yellow-800 border-yellow-200",
          icon: Clock,
          label: "Chờ xử lý",
        };
      case "processing":
        return {
          color: "bg-blue-100 text-blue-800 border-blue-200",
          icon: AlertCircle,
          label: "Đang xử lý",
        };
      case "completed":
        return {
          color: "bg-green-100 text-green-800 border-green-200",
          icon: CheckCircle,
          label: "Hoàn thành",
        };
      case "rejected":
        return {
          color: "bg-red-100 text-red-800 border-red-200",
          icon: XCircle,
          label: "Từ chối",
        };
      default:
        return {
          color: "bg-gray-100 text-gray-800 border-gray-200",
          icon: Clock,
          label: "Không xác định",
        };
    }
  };

  /**
   * Format date string to Vietnamese format
   */
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN');
    } catch {
      return dateString;
    }
  };

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4", className)}>
      {tickets.map((ticket) => {
        const statusConfig = getStatusConfig(ticket.status);
        const StatusIcon = statusConfig.icon;

        return (
          <Card
            key={ticket.id}
            className="hover:shadow-lg transition-all duration-300 cursor-pointer bg-transparent border-gray-200"
            onClick={() => onTicketClick?.(ticket)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-gray-900 line-clamp-1">
                  {ticket.ticketName}
                </CardTitle>
                <StatusIcon className="w-4 h-4 text-gray-400" />
              </div>
            </CardHeader>
            
            <CardContent className="pt-0 space-y-3">
              {/* Ticket Code */}
              <div className="space-y-1">
                <p className="text-xs text-gray-500">Mã phiếu</p>
                <p className="text-sm font-mono font-medium text-gray-900">
                  {ticket.ticketCode}
                </p>
              </div>

              {/* Created Date */}
              <div className="space-y-1">
                <p className="text-xs text-gray-500">Ngày tạo</p>
                <p className="text-sm text-gray-700">
                  {formatDate(ticket.createdDate)}
                </p>
              </div>

              {/* Status Badge */}
              <div className="pt-2">
                <Badge
                  variant="outline"
                  className={cn(
                    "text-xs font-medium",
                    statusConfig.color
                  )}
                >
                  <StatusIcon className="w-3 h-3 mr-1" />
                  {statusConfig.label}
                </Badge>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
