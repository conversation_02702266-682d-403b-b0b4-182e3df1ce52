import React, { useState } from "react";
import {
  Popover,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { BorderGroup } from "../utils/borderGroups";

interface BorderPopoverProps {
  value: string;
  onValueChange: (value: string) => void;
  groups: BorderGroup[];
  placeholder: string;
  disabled?: boolean;
  className?: string;
  renderValue?: (value: string) => React.ReactNode;
}

export const BorderPopover: React.FC<BorderPopoverProps> = ({
  value,
  onValueChange,
  groups,
  placeholder,
  disabled = false,
  className,
  renderValue,
}) => {
  const [open, setOpen] = useState(false);

  // Find current option to display
  const getCurrentOption = () => {
    for (const group of groups) {
      const option = group.options.find((opt) => opt.value === value);
      if (option) return option;
    }
    return null;
  };

  const currentOption = getCurrentOption();

  const handleSelect = (selectedValue: string) => {
    onValueChange(selectedValue);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "h-7 px-2 font-normal",
            !currentOption && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          {renderValue
            ? renderValue(value)
            : currentOption?.label || placeholder}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        {groups.length > 1 ? (
          <Tabs defaultValue={groups[0]?.id} className="w-full">
            <TabsList
              className="grid w-full h-8"
              style={{ gridTemplateColumns: `repeat(${groups.length}, 1fr)` }}
            >
              {groups.map((group) => (
                <TabsTrigger
                  key={group.id}
                  value={group.id}
                  className="text-xs px-1"
                >
                  {group.label}
                </TabsTrigger>
              ))}
            </TabsList>

            {groups.map((group) => (
              <TabsContent
                key={group.id}
                value={group.id}
                className="mt-0 max-h-64 overflow-y-auto"
              >
                <div className="p-2">
                  <div className="grid grid-cols-3 gap-1">
                    {group.options.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => handleSelect(option.value)}
                        className={cn(
                          "flex items-center justify-center px-2 py-2 text-xs rounded hover:bg-gray-100 transition-colors",
                          value === option.value &&
                            "bg-blue-100 text-blue-900 ring-1 ring-blue-500"
                        )}
                        title={option.description}
                      >
                        <span className="font-medium">{option.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        ) : (
          // Single group - no tabs
          <div className="p-2 max-h-64 overflow-y-auto">
            <div className="grid grid-cols-3 gap-1">
              {groups[0]?.options.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleSelect(option.value)}
                  className={cn(
                    "flex items-center justify-center px-2 py-2 text-xs rounded hover:bg-gray-100 transition-colors",
                    value === option.value &&
                      "bg-blue-100 text-blue-900 ring-1 ring-blue-500"
                  )}
                  title={option.description}
                >
                  <span className="font-medium">{option.label}</span>
                </button>
              ))}
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};
