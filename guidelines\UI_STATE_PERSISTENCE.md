# UI State Persistence Implementation Guide

## 🎯 Overview

This guide documents the UI State Persistence system implemented to maintain user interface state across browser refreshes and enable undo/redo functionality for UI changes.

## 📐 Architecture

### State Categories

The UI state is organized into three distinct categories:

#### 1. **Persistent UI State** - Survives F5 refresh
```typescript
interface PersistentUIState {
  selectedNodeId: string | null;      // Currently selected node
  expandedNodeIds: Set<string>;       // Tree nodes that are expanded
  viewMode: ViewMode;                 // Mobile/tablet/desktop view
  showTreePanel: boolean;             // Tree panel visibility
  showPropertiesPanel: boolean;       // Properties panel visibility
}
```

#### 2. **Session UI State** - Reset on page load
```typescript
interface SessionUIState {
  editorMode: EditorMode;            // 'view' | 'edit' | 'move' - Always reset to 'edit'
  isDirty: boolean;                  // Unsaved changes flag
  lastSaved: number;                 // Timestamp of last save
  copiedNode: TreeNode | null;       // Clipboard state
  lastCreatedNodeId: string | null;  // Track newly created nodes
}
```

#### 3. **Temporary UI State** - Editing workflow state
```typescript
interface TemporaryUIState {
  propertiesPanelMainTab?: 'post' | 'block';
  propertiesPanelSubTab?: 'display' | 'content';
  propertiesPanelAdvanced?: boolean;
  propertiesPanelDisplaySection?: string;  // Nested sections like 'size', 'layout'
  isEditingCSS?: boolean;
  focusedInputId?: string | null;
  selectedPropertyGroup?: string | null;
}
```

### Composite UI State
```typescript
interface BuilderUIState extends PersistentUIState, SessionUIState {
  temporary?: TemporaryUIState;
}
```

## 🔄 Persistence Strategy

### Save Triggers

Different UI state types have different save strategies:

```typescript
const UI_PERSISTENCE_CONFIG = {
  IMMEDIATE_SAVE: ['selectedNodeId', 'viewMode'],           // Save immediately
  DEBOUNCED_SAVE: ['expandedNodeIds'],                      // Save after 500ms delay
  ACTION_SAVE: ['showTreePanel', 'showPropertiesPanel'],   // Save on user action
  SESSION_SAVE: ['propertiesPanelTab', 'isAdvancedMode'],  // Save for session restoration
  
  TRACKABLE_UI_CHANGES: [                                  // Tracked in history
    'selectedNodeId', 
    'viewMode', 
    'showTreePanel',
    'propertiesPanelTab'
  ]
};
```

### Storage Implementation

```typescript
// Save persistent state only
BuilderStorage.savePersistentUIState(uiState);

// Save temporary state separately  
BuilderStorage.saveTemporaryUIState(uiState);

// Load with proper defaults
const { state } = BuilderStorage.loadCompleteState();
// Returns persistent state + session defaults + temporary state
```

## 📝 Usage Examples

### 1. **F5 Refresh Scenario**

```typescript
// Before F5: User is editing node "text-123" in mobile view
const beforeRefresh = {
  selectedNodeId: "text-123",
  viewMode: "mobile", 
  showTreePanel: false,
  temporary: {
    propertiesPanelTab: "advanced",
    isEditingCSS: true
  }
};

// After F5: State is restored with proper context
const afterRefresh = {
  selectedNodeId: "text-123",     // ✅ Restored - user continues editing
  viewMode: "mobile",             // ✅ Restored - user's choice preserved
  showTreePanel: false,           // ✅ Restored - layout preserved
  editorMode: "edit",            // ✅ Reset - always start in edit mode
  isDirty: false,                // ✅ Reset - no unsaved changes initially
  temporary: {
    propertiesPanelTab: "advanced" // ✅ Restored - editing context preserved
    // isEditingCSS omitted - runtime state not restored
  }
};
```

### 2. **Context Integration**

```typescript
const { setPersistentUIState, setTemporaryUIState } = useBuilder();

// Auto-saves to localStorage
setPersistentUIState({ selectedNodeId: "new-node" });

// Saves for session restoration  
setTemporaryUIState({ propertiesPanelTab: "basic" });
```

### 3. **History Integration**

```typescript
// Only important UI changes are tracked in undo/redo
const trackableChanges = ['selectedNodeId', 'viewMode', 'showTreePanel'];

// Automatic UI history tracking
historyManager.pushUIChange('persistent', { selectedNodeId: "node-1" }, newState);
```

## 🔧 Implementation Details

### Type Safety

Strong typing prevents mixing UI state concerns:

```typescript
// ✅ Correct usage
const persistent: PersistentUIState = { selectedNodeId: "test", ... };
const temporary: TemporaryUIState = { propertiesPanelTab: "basic" };

// ❌ TypeScript prevents this
// persistent.isDirty = true;        // Error: isDirty not in PersistentUIState
// temporary.viewMode = "mobile";    // Error: viewMode not in TemporaryUIState
```

### Storage Schema

Enhanced storage schema supports both old and new formats:

```typescript
interface StorageSchema {
  version: number;
  data: {
    post: Post;
    uiState: SerializedUIState;           // Persistent state only
    temporaryUIState?: SerializedTemporaryUIState;  // Optional temporary state
    history?: HistoryState;                // Optional history
  };
  metadata: {
    lastSaved: number;
    createdAt: number;
  };
}
```

### Migration Strategy

```typescript
// V1 → V2: Backward compatibility maintained
// Legacy format automatically migrated
// New features gracefully degrade if not supported
```

## 🚀 Benefits

### 1. **Enhanced User Experience**
- No lost work on accidental refresh
- Consistent editing context restoration
- Smooth workflow continuation

### 2. **Robust State Management**
- Clear separation of concerns
- Type-safe state updates
- Predictable behavior

### 3. **Performance Optimized**
- Selective persistence (only save what matters)
- Debounced saves for frequent changes
- Efficient storage utilization

### 4. **Developer Friendly**
- Strong TypeScript typing
- Clear API boundaries
- Extensible architecture

## 📋 Usage Checklist

### When implementing new UI features:

- [ ] **Categorize state**: Persistent vs Session vs Temporary
- [ ] **Choose save strategy**: Immediate, Debounced, or Action-based
- [ ] **Consider history tracking**: Should this be undoable?
- [ ] **Add to type definitions**: Update appropriate interfaces
- [ ] **Test F5 scenario**: Verify state restoration works correctly

### Common patterns:

```typescript
// ✅ Panel visibility (persistent, action-based save)
setPersistentUIState({ showTreePanel: !showTreePanel });

// ✅ Editing mode (temporary, session-based save)  
setTemporaryUIState({ isEditingCSS: true });

// ✅ Current selection (persistent, immediate save + history)
setPersistentUIState({ selectedNodeId: nodeId });
```

## 🔍 Testing

Comprehensive test suite covers:

- Persistent state save/load cycles
- Session state reset behavior  
- Temporary state restoration
- F5 refresh scenarios
- Backward compatibility
- Type safety validation

## 🎯 Current Implementation Status

### ✅ **Completed Features**
1. **Multi-layer Tab Persistence**: Main tabs, sub-tabs, và nested sections
2. **Smart Tab Behavior**: Auto-switch for new nodes, preserve for existing
3. **Editor Mode Management**: View/Edit/Move modes với proper state handling
4. **Node Creation Counter**: Intelligent naming system (frame-1, frame-2)
5. **History Integration**: UI changes tracked in undo/redo system
6. **Performance Optimizations**: Memoization và debounced saves

### 🔄 **Recent Improvements**
1. **Overlay System Integration**: State synced với figma-like overlays
2. **Inline Editing Support**: Text editing states properly managed
3. **Advanced Form Sections**: Shadow, Filter, Appearance section states
4. **Node Factory System**: Template và Widget node support
5. **Drag & Drop State**: Canvas drag operations với visual indicators

### 🚧 **Current Focus Areas**
1. **Canvas Drag & Drop**: Move mode implementation với position indicators
2. **Node Property Management**: Enhanced TreeNodeProperty system
3. **Form Section Persistence**: Individual section open/closed states
4. **Node Naming System**: Improved counter algorithms

## 🎯 Future Enhancements

### Planned Features:
1. **Cloud Sync**: Sync UI state across devices
2. **Profile-based Settings**: Different UI preferences per user
3. **Project-specific State**: UI state per project/post
4. **Advanced Analytics**: Track UI usage patterns
5. **Customizable Persistence**: User-configurable save strategies
6. **Collaborative State**: Multi-user editing state management

### 🔧 **Technical Improvements**
1. **State Migration System**: Version upgrades cho UI schema
2. **Performance Monitoring**: UI state change tracking
3. **Memory Optimization**: Large project state management
4. **Error Recovery**: Corrupted state fallback strategies

---

**Key Principle**: The UI should restore users to their exact working context after any interruption, making the editing experience seamless and reliable.