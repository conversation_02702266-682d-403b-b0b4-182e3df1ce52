// =============================================================================
// KHA BUILDER PACKAGE - PUBLIC API
// =============================================================================

// Core Types & Constants (Single source of truth)
export type {
  TreeNode,
  TreeNodeType,
  BuilderState,
  Post,
  BasePost,
  ViewMode,
  EditorMode,
} from "./core";

// Main Builder Component
export { BuilderLayout as Builder } from "./components";

// Essential Context & Hooks
export {
  BuilderProvider,
  useBuilder,
  useSelectedNode,
  useNodeOperations,
} from "./context";

// Core Utilities
export {
  cloneNode,
  findNodeById,
  createNodeCopy,
  addNodeToTree,
} from "./utils/tree";

// Configuration
export * from "./config/constants";
