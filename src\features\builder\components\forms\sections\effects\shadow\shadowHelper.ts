import {
  ShadowState,
  <PERSON>T<PERSON>,
  ShadowSize,
  DEFAULT_SHADOW_OPACITY,
  DEFAULT_SHADOW_COLOR,
} from "./types";
export const parseShadowState = (className: string = ""): ShadowState => {
  const classes = className.split(" ").filter(Boolean);
  const hasBoxShadow = classes.some(
    (cls) =>
      cls === "shadow" ||
      (cls.startsWith("shadow-") && !cls.startsWith("shadow-["))
  );
  const hasDropShadow = classes.some(
    (cls) => cls === "drop-shadow" || cls.startsWith("drop-shadow-")
  );

  let type: ShadowType = "none";
  let size: ShadowSize = "md";
  let color = "";
  let opacity = DEFAULT_SHADOW_OPACITY;

  if (hasDropShadow) {
    type = "drop";
    const dropShadowClass = classes.find((cls) =>
      cls.startsWith("drop-shadow")
    );
    if (dropShadowClass) {
      if (dropShadowClass === "drop-shadow") {
        size = "md";
      } else {
        const sizeMatch = dropShadowClass.match(/drop-shadow-(.+)/);
        if (sizeMatch) {
          size = sizeMatch[1] as ShadowSize;
        }
      }
    }
  } else if (hasBoxShadow) {
    type = "box";
    const shadowClass = classes.find(
      (cls) =>
        cls === "shadow" ||
        (cls.startsWith("shadow-") && !cls.includes("/") && !cls.includes("["))
    );
    if (shadowClass) {
      if (shadowClass === "shadow") {
        size = "md";
      } else {
        const sizeMatch = shadowClass.match(/shadow-(.+)/);
        if (sizeMatch) {
          size = sizeMatch[1] as ShadowSize;
        }
      }
    }
  }

  if (type === "box") {
    const colorClass = classes.find(
      (cls) => cls.includes("shadow-") && cls.includes("/")
    );
    if (colorClass) {
      const match = colorClass.match(/shadow-(.+?)\/(.+)/);
      if (match) {
        color = match[1];
        opacity = match[2];
      }
    }
  } else if (type === "drop") {
    const colorClass = classes.find(
      (cls) => cls.includes("drop-shadow-") && cls.includes("/")
    );
    if (colorClass) {
      const match = colorClass.match(/drop-shadow-(.+?)\/(.+)/);
      if (match) {
        color = match[1];
        opacity = match[2];
      }
    }
  }

  return { type, size, color, opacity };
};

export const generateShadowClasses = (shadowState: ShadowState): string => {
  if (shadowState.type === "none") {
    return "";
  }

  const classes: string[] = [];

  if (shadowState.type === "box") {
    if (shadowState.size === "md") {
      classes.push("shadow");
    } else {
      classes.push(`shadow-${shadowState.size}`);
    }
  } else if (shadowState.type === "drop") {
    if (shadowState.size === "md") {
      classes.push("drop-shadow");
    } else {
      classes.push(`drop-shadow-${shadowState.size}`);
    }

    const color = shadowState.color || DEFAULT_SHADOW_COLOR;
    const opacity = shadowState.opacity || DEFAULT_SHADOW_OPACITY;
    if (color && opacity) {
      classes.push(`drop-shadow-${color}/${opacity}`);
    }
  }

  if (shadowState.type === "box") {
    const color = shadowState.color || DEFAULT_SHADOW_COLOR;
    const opacity = shadowState.opacity || DEFAULT_SHADOW_OPACITY;
    classes.push(`shadow-${color}/${opacity}`);
  }

  return classes.join(" ");
};

// removeShadowClasses function moved to utils/classNameUtils.ts to avoid duplicate exports
const removeShadowClasses = (className: string): string => {
  return className
    .split(" ")
    .filter(
      (cls) => !cls.startsWith("shadow") && !cls.startsWith("drop-shadow")
    )
    .join(" ")
    .trim();
};

export const updateShadowClasses = (
  currentClassName: string,
  shadowState: ShadowState
): string => {
  const cleanClassName = removeShadowClasses(currentClassName);
  const stateWithDefaults = {
    ...shadowState,
    color:
      shadowState.type !== "none"
        ? shadowState.color || DEFAULT_SHADOW_COLOR
        : shadowState.color,
    opacity:
      shadowState.type !== "none"
        ? shadowState.opacity || DEFAULT_SHADOW_OPACITY
        : shadowState.opacity,
  };

  const newShadowClasses = generateShadowClasses(stateWithDefaults);
  const allClasses = [cleanClassName, newShadowClasses]
    .filter(Boolean)
    .join(" ")
    .trim();

  return allClasses;
};

export const getShadowColorDisplayName = (
  color: string,
  opacity: string
): string => {
  if (!color) return "Chưa chọn màu";

  if (color === "black") {
    return `Đen (${opacity}%)`;
  }

  if (color === "white") {
    return `Trắng (${opacity}%)`;
  }

  const colorParts = color.split("-");
  if (colorParts.length === 2) {
    const [colorName, shade] = colorParts;
    const colorNameVi = getColorNameInVietnamese(colorName);
    return `${colorNameVi} ${shade} (${opacity}%)`;
  }

  const colorNameVi = getColorNameInVietnamese(color);
  return `${colorNameVi} (${opacity}%)`;
};

const getColorNameInVietnamese = (colorName: string): string => {
  const colorMap: Record<string, string> = {
    gray: "Xám",
    slate: "Xám đá",
    zinc: "Kẽm",
    neutral: "Trung tính",
    stone: "Đá",
    red: "Đỏ",
    orange: "Cam",
    amber: "Hổ phách",
    yellow: "Vàng",
    lime: "Vàng chanh",
    green: "Xanh lá",
    emerald: "Ngọc lục",
    teal: "Xanh mòng két",
    cyan: "Xanh lam",
    sky: "Xanh trời",
    blue: "Xanh dương",
    indigo: "Chàm",
    violet: "Tím",
    purple: "Tím",
    fuchsia: "Hồng tím",
    pink: "Hồng",
    rose: "Hồng hoa",
  };

  return (
    colorMap[colorName] ||
    colorName.charAt(0).toUpperCase() + colorName.slice(1)
  );
};
