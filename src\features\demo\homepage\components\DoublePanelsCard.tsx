import React from "react";
import { ImageLoader } from "@/components/image/ImageLoader";
import { cn } from "@/lib/utils";
import { TAILWIND_SIZES } from "@/constants/constants";
import { PanelContent } from "../states/type";

const defaultPanelContent: PanelContent = {
  type: "image",
  content: "",
  title: "",
  size: "1/2" as TAILWIND_SIZES,
};

export interface DoublePanelsCardProps {
  leftPanel?: PanelContent;
  rightPanel?: PanelContent;
  className?: string;
  onClick?: () => void;
}

export const DoublePanelsCard: React.FC<DoublePanelsCardProps> = ({
  leftPanel = defaultPanelContent,
  rightPanel = defaultPanelContent,
  className,
}) => {
  const renderPanelContent = (panel: PanelContent) => {
    return (
      <div className={cn("relative h-max flex", `w-${panel?.size || "1/2"}`)}>
        <div className="h-full">
          {panel.type === "image" ? (
            <ImageLoader
              src={panel.content}
              alt={panel.title}
              className="w-full h-full object-cover rounded-md"
              fallbackText={panel.title}
            />
          ) : (
            <div className="h-full p-6 flex items-center justify-center">
              <div className="text-center space-y-4">
                {panel.title && (
                  <h1 className="text-[64px] font-medium whitespace-nowrap text-[#0177BD] text-center" >{panel.title}</h1>
                )}
                <div className="text-gray-700 text-sm leading-relaxed whitespace-pre-line"
                  dangerouslySetInnerHTML={{ __html: panel.content }}>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div
      className={cn(
        "overflow-hidden transition-all duration-300 bg-transparent min-h-64 h-max",
        className
      )}
    >
      <div className="flex h-full items-center">
        {renderPanelContent(leftPanel)}
        <div className="w-px" />
        {renderPanelContent(rightPanel)}
      </div>
    </div>
  );
};
