/**
 * Layout configuration constants for the Builder
 */

// Panel sizing configuration
export const PANEL_CONFIG = {
  leftPanel: {
    minWidth: 200,
    maxWidth: 500,
    defaultWidth: 264,
  },
  rightPanel: {
    minWidth: 250,
    maxWidth: 600,
    defaultWidth: 320,
  },
  storageKey: 'builder-panel-widths',
} as const;

// Layout dimensions
export const LAYOUT_DIMENSIONS = {
  headerHeight: 56, // 14 * 4 = 56px (h-14 in Tailwind)
  resizeHandleWidth: 4,
  zIndex: {
    canvas: 1,
    panels: 10,
    resizeHandles: 20,
  },
} as const;

// CSS custom properties for consistent styling
export const LAYOUT_STYLES = {
  panel: {
    shadow: 'shadow-lg',
    background: 'bg-white',
    border: {
      left: 'border-r',
      right: 'border-l',
    },
  },
  resizeHandle: {
    background: 'bg-gray-300',
    backgroundHover: 'hover:bg-blue-400',
    cursor: 'cursor-col-resize',
  },
} as const;