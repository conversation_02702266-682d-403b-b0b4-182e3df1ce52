import React, { useCallback } from "react";
import { createPortal } from "react-dom";
import { TreeNode } from "../../context/types";
import { useBuilder } from "../../context";
import { useCanvasDragDrop } from "../../hooks/useCanvasDragDrop";
import { useCanvasMove } from "../../hooks/useCanvasMove";
import { useNodeRegistration } from "../../overlay/hooks/useNodeRegistration";
import { renderNode } from "../nodes";
import { DragPreview } from "./DragPreview";
import { DropIndicator } from "./DropIndicator";
import { useOverlay } from "../../overlay";

interface DraggableNodeRendererProps {
  node: TreeNode;
  isEditing?: boolean;
}

const DraggableNodeRendererComponent: React.FC<DraggableNodeRendererProps> = ({
  node,
  isEditing = true,
}) => {
  const { uiState } = useBuilder();
  const {
    isDragging,
    draggedNodeId,
    dragPreview,
    dropIndicator,
    handleDragStart,
  } = useCanvasDragDrop();
  const { moveIndicator } = useCanvasMove();
  const overlayNodeRef = useNodeRegistration(node.id);
  const { registerNode, unregisterNode } = useOverlay();
  const nodeRef = React.useRef<HTMLElement>(null);

  const editorMode = uiState?.editorMode ?? "edit";
  const isEditMode = editorMode === "edit";
  const isBeingDragged = isDragging && draggedNodeId === node.id;

  const renderContent = () => {
    return renderNode({
      node,
    });
  };

  // Combine refs function
  const combineRefs = useCallback(
    (element: HTMLElement | null) => {
      // Set both refs
      if (nodeRef.current !== element) {
        nodeRef.current = element;
      }
      if (overlayNodeRef && 'current' in overlayNodeRef) {
        overlayNodeRef.current = element;
      }
      
      // Register with overlay system
      if (element && node.id) {
        registerNode(node.id, element);
      } else if (!element && node.id) {
        unregisterNode(node.id);
      }
    },
    [node.id, registerNode, unregisterNode, overlayNodeRef]
  );

  // Pass props to node components for overlay integration
  const content = renderContent();
  
  if (!content) {
    return null;
  }

  // For ImageNode, we need to wrap it in a div to ensure proper ref handling
  const isImageNode = node.type === 'image';
  
  if (isImageNode) {
    // Wrap ImageNode in a div for proper overlay registration
    return (
      <>
        <div
          ref={combineRefs}
          data-node-id={node.id}
          onMouseDown={isEditMode && isEditing ? (e: React.MouseEvent) => handleDragStart(e, node.id) : undefined}
          style={{
            opacity: isBeingDragged ? 0.3 : undefined,
            display: 'inline-block' // Preserve inline behavior
          }}
        >
          {content}
        </div>

        {/* Render drag preview and drop indicator globally */}
        {createPortal(
          <>
            {dragPreview.visible && dragPreview.node && (
              <DragPreview
                node={dragPreview.node}
                position={dragPreview.position}
              />
            )}
            {dropIndicator && (
              <DropIndicator
                targetId={dropIndicator.targetId}
                position={dropIndicator.position}
                visualPosition={dropIndicator.visualPosition}
              />
            )}
            {/* Move mode indicators */}
            {moveIndicator && (
              <DropIndicator
                targetId={moveIndicator.targetId}
                position={moveIndicator.position}
                visualPosition={moveIndicator.visualPosition}
              />
            )}
          </>,
          document.body
        )}
      </>
    );
  }

  const enhancedContent = (
    <div
      ref={combineRefs}
      data-node-id={node.id}
      onMouseDown={isEditMode && isEditing ? (e: React.MouseEvent) => handleDragStart(e, node.id) : undefined}
      style={{
        opacity: isBeingDragged ? 0.3 : undefined
      }}
    >
      {content}
    </div>
  );

  return (
    <>
      {/* Direct render - events handled by canvas delegation */}
      {enhancedContent}

      {/* Render drag preview and drop indicator globally */}
      {createPortal(
        <>
          {dragPreview.visible && dragPreview.node && (
            <DragPreview
              node={dragPreview.node}
              position={dragPreview.position}
            />
          )}
          {dropIndicator && (
            <DropIndicator
              targetId={dropIndicator.targetId}
              position={dropIndicator.position}
              visualPosition={dropIndicator.visualPosition}
            />
          )}
          {/* Move mode indicators */}
          {moveIndicator && (
            <DropIndicator
              targetId={moveIndicator.targetId}
              position={moveIndicator.position}
              visualPosition={moveIndicator.visualPosition}
            />
          )}
        </>,
        document.body
      )}
    </>
  );
};

export const DraggableNodeRenderer = DraggableNodeRendererComponent;

