import React from "react";
import { ImageLoader } from "@/components/image/ImageLoader";
import { cn } from "@/lib/utils";
import { LibraryItem } from "../data/mockData";
import { Button } from "@/components/ui/button";

export interface LibraryPanelProps {
  type: "image" | "video";
  items: LibraryItem[];
  title: string;
  description?: string;
  className?: string;
  onViewMoreClick?: () => void;
}

export const LibraryPanel: React.FC<LibraryPanelProps> = ({
  type,
  items,
  title,
  description,
  className,
  onViewMoreClick,
}) => {
  const filteredItems = items.filter((item) => item.type === type);
  const flexDirection = type === "image" ? "flex-row" : "flex-row-reverse";

  const LibraryPanelItem: React.FC<{
    item: LibraryItem;
    direction: "up" | "down";
  }> = ({ item, direction }) => {
    return (
      <div
        key={item.id}
        className={cn(
          "flex h-min",
          direction === "up" ? "flex-col-reverse" : "flex-col"
        )}
      >
        <div className="flex aspect-square rounded-lg overflow-hidden bg-gray-100 h-2/3">
          <ImageLoader
            src={item.thumbnail}
            alt={item.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 aspect-square"
            fallbackText={item.title}
          />
        </div>
        <div className="flex flex-1 aspect-square h-1/3 w-1/3"></div>
      </div>
    );
  };

  return (
    <div
      className={cn(
        "flex gap-8 bg-transparent border-gray-200 p-4 items-center",
        className,
        flexDirection
      )}
    >
      <div className="flex flex-col space-y-4 w-1/3 h-full">
        <div className="space-y-2">
          <h2 className="text-[28px] font-semibold text-gray-900 flex items-center gap-2">
            {title}
          </h2>
          <p className="text-[15px]">{description}</p>
        </div>
        <div>
          <Button
            variant="outline"
            className="rounded-full border-2 border-primary text-primary bg-primary-foreground hover:bg-primary/10"
            onClick={onViewMoreClick}
          >
            Xem thêm
          </Button>
        </div>
      </div>

      <div className="w-2/3">
        <div className="grid grid-cols-3 gap-10">
          {filteredItems[0] && (
            <LibraryPanelItem item={filteredItems[0]} direction="up" />
          )}
          {filteredItems[1] && (
            <LibraryPanelItem item={filteredItems[1]} direction="down" />
          )}
          {filteredItems[2] && (
            <LibraryPanelItem item={filteredItems[2]} direction="up" />
          )}
        </div>
      </div>
    </div>
  );
};
