import React, { useMemo, useCallback } from 'react';
import { TreeNode } from '../../../../context/types';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import { safeMatch, combineClassNames } from '../../utils/classNameUtils';

interface OverflowSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const OVERFLOW_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'overflow-visible', label: 'Hiện tất cả' },
  { value: 'overflow-hidden', label: 'Ẩn phần tràn' },
  { value: 'overflow-scroll', label: 'Cuộn khi cần' },
  { value: 'overflow-auto', label: 'Tự động' }
];

const OVERFLOW_X_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'overflow-x-visible', label: 'Hiện tất cả' },
  { value: 'overflow-x-hidden', label: 'Ẩn phần tràn' },
  { value: 'overflow-x-scroll', label: 'Cuộn khi cần' },
  { value: 'overflow-x-auto', label: 'Tự động' }
];

const OVERFLOW_Y_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'overflow-y-visible', label: 'Hiện tất cả' },
  { value: 'overflow-y-hidden', label: 'Ẩn phần tràn' },
  { value: 'overflow-y-scroll', label: 'Cuộn khi cần' },
  { value: 'overflow-y-auto', label: 'Tự động' }
];

// Helper functions following border pattern
const removeOverflowClasses = (className: string): string => {
  return className
    .split(/\s+/)
    .filter(cls => !cls.startsWith('overflow'))
    .join(' ')
    .trim();
};

const OverflowSectionComponent: React.FC<OverflowSectionProps> = ({ node, onChange, disabled }) => {
  const currentClassName = node.style?.className || '';
  
  const currentState = useMemo(() => {
    // Check for specific overflow-x or overflow-y first
    const overflowX = safeMatch(currentClassName, /\boverflow-x-(visible|hidden|scroll|auto)\b/, 'default');
    const overflowY = safeMatch(currentClassName, /\boverflow-y-(visible|hidden|scroll|auto)\b/, 'default');
    
    // Check for general overflow (affects both x and y)
    const overflow = safeMatch(currentClassName, /\boverflow-(visible|hidden|scroll|auto)\b/, 'default');

    return { overflow, overflowX, overflowY };
  }, [currentClassName]);

  const cleanClassName = useMemo(() => {
    return removeOverflowClasses(currentClassName);
  }, [currentClassName]);

  const updateClassName = (newClassName: string) => {
    onChange({
      style: {
        ...node.style,
        className: newClassName
      }
    });
  };

  const handleChange = useCallback((type: string, value: string) => {
    const newState = { ...currentState, [type]: value };
    
    const classes = [
      cleanClassName,
      newState.overflow !== 'default' ? newState.overflow : '',
      newState.overflowX !== 'default' ? newState.overflowX : '',
      newState.overflowY !== 'default' ? newState.overflowY : ''
    ].filter(Boolean);
    
    updateClassName(combineClassNames(...classes));
  }, [cleanClassName, currentState]);

  return (
    <div className="space-y-4">
      {/* General Overflow - Full Width */}
      <div className="space-y-2">
        <Label htmlFor="overflow-select" className="text-xs">Tràn chung</Label>
        <Select 
          value={currentState.overflow} 
          onValueChange={(value) => handleChange('overflow', value)}
          disabled={disabled}
        >
          <SelectTrigger id="overflow-select">
            <SelectValue placeholder="Mặc định" />
          </SelectTrigger>
          <SelectContent>
            {OVERFLOW_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Overflow X & Y - Same Row */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="overflow-x-select" className="text-xs">Tràn ngang</Label>
          <Select 
            value={currentState.overflowX} 
            onValueChange={(value) => handleChange('overflowX', value)}
            disabled={disabled}
          >
            <SelectTrigger id="overflow-x-select">
              <SelectValue placeholder="Mặc định" />
            </SelectTrigger>
            <SelectContent>
              {OVERFLOW_X_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="overflow-y-select" className="text-xs">Tràn dọc</Label>
          <Select 
            value={currentState.overflowY} 
            onValueChange={(value) => handleChange('overflowY', value)}
            disabled={disabled}
          >
            <SelectTrigger id="overflow-y-select">
              <SelectValue placeholder="Mặc định" />
            </SelectTrigger>
            <SelectContent>
              {OVERFLOW_Y_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export const OverflowSection = React.memo(OverflowSectionComponent);
