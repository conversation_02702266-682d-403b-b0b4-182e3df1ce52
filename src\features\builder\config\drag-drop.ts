/**
 * Unified configuration for drag-drop and move operations
 */
export const DRAG_DROP_CONFIG = {
  // Drag behavior
  DRAG_THRESHOLD: 5, // pixels before drag starts
  THROTTLE_DELAY: 50, // ms between position updates
  
  // Drop zone calculation
  CENTER_THRESHOLD: 0.3, // 30% from edges for "inside" detection
  EDGE_THRESHOLD: 0.25, // 25% from edge for before/after split
  MAX_EDGE_THRESHOLD: 20, // max pixels for edge detection
  
  // Visual indicators
  INDICATOR_SIZE: 4, // px width/height of indicator line
  INDICATOR_OFFSET: 2, // px offset from target element
  
  // Z-index layers
  Z_INDEX_INDICATOR: 9998,
  Z_INDEX_PREVIEW: 9999,
} as const;