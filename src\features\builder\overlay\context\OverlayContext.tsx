import React, { createContext, useContext, useState, useCallback, useRef } from 'react';
import { OverlayState, OverlayContextValue } from '../types';
import { EditorMode } from '../../context/types';

const initialState: OverlayState = {
  selectedNodeId: null,
  hoveredNodeId: null,
  dropTargetNodeId: null,
  editorMode: 'edit',
};

const OverlayContext = createContext<OverlayContextValue | null>(null);

export const useOverlay = () => {
  const context = useContext(OverlayContext);
  if (!context) {
    throw new Error('useOverlay must be used within OverlayProvider');
  }
  return context;
};

interface OverlayProviderProps {
  children: React.ReactNode;
}

export const OverlayProvider: React.FC<OverlayProviderProps> = ({ children }) => {
  const [state, setState] = useState<OverlayState>(initialState);
  const nodeRegistry = useRef<Map<string, HTMLElement>>(new Map());

  const updateSelection = useCallback((nodeId: string | null) => {
    setState(prev => ({ ...prev, selectedNodeId: nodeId }));
  }, []);

  const updateHover = useCallback((nodeId: string | null) => {
    setState(prev => ({ ...prev, hoveredNodeId: nodeId }));
  }, []);

  const updateDropTarget = useCallback((nodeId: string | null) => {
    setState(prev => ({ ...prev, dropTargetNodeId: nodeId }));
  }, []);

  const updateEditorMode = useCallback((mode: EditorMode) => {
    setState(prev => ({ ...prev, editorMode: mode }));
  }, []);

  const registerNode = useCallback((nodeId: string, element: HTMLElement) => {
    nodeRegistry.current.set(nodeId, element);
  }, []);

  const unregisterNode = useCallback((nodeId: string) => {
    nodeRegistry.current.delete(nodeId);
  }, []);

  const getNodeElement = useCallback((nodeId: string) => {
    // First try registry
    const registeredElement = nodeRegistry.current.get(nodeId);
    if (registeredElement) {
      return registeredElement;
    }
    
    // Fallback to DOM query for elements that might not be registered (like direct img elements)
    const element = document.querySelector(`[data-node-id="${nodeId}"]`);
    return element as HTMLElement | undefined;
  }, []);

  const value: OverlayContextValue = {
    state,
    updateSelection,
    updateHover,
    updateDropTarget,
    updateEditorMode,
    registerNode,
    unregisterNode,
    getNodeElement,
  };

  return <OverlayContext.Provider value={value}>{children}</OverlayContext.Provider>;
};