import { useEffect, useRef } from 'react';
import { useOverlay } from '../context/OverlayContext';

export const useNodeRegistration = (nodeId: string) => {
  const { registerNode, unregisterNode } = useOverlay();
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (elementRef.current && nodeId) {
      registerNode(nodeId, elementRef.current);

      return () => {
        unregisterNode(nodeId);
      };
    }
  }, [nodeId, registerNode, unregisterNode]);

  return elementRef;
};