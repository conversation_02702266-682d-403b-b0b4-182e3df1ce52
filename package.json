{"name": "kha-builder-package", "private": true, "version": "1.0.0", "description": "Builder component package for KHA projects", "type": "module", "main": "dist/builder.js", "module": "dist/builder.es.js", "types": "dist/builder.d.ts", "files": ["dist", "README.md"], "keywords": ["builder", "react", "component", "kha"], "license": "UNLICENSED", "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0", "tailwindcss": "^3.0.0 || ^4.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^3.0.0", "lucide-react": "^0.400.0", "@radix-ui/react-slot": "^1.0.0"}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:builder": "vite build --config vite.config.builder.ts", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "prepublishOnly": "yarn build:builder", "pack:builder": "yarn build:builder && yarn pack", "clean:dist": "rmdir /s /q dist 2>nul || rm -rf dist", "prepare:package": "yarn clean:dist && yarn build:builder && yarn pack && move *.tgz dist\\"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.7.0", "@tailwindcss/vite": "^4.1.3", "@tanstack/query-sync-storage-persister": "^5.74.4", "@tanstack/react-form": "^1.6.3", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-persist-client": "^5.74.4", "@tanstack/react-table": "^8.21.3", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "i18next": "^25.0.1", "immer": "^10.1.1", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "lucide-react": "^0.488.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-leaflet": "^5.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.17", "@types/lodash.clonedeep": "^4.5.9", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-basic-ssl": "^2.0.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jsdom": "^26.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vitest": "^3.1.4"}}