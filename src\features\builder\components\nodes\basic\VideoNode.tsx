import React from 'react';
import { cn } from '@/lib/utils';
import { NodeRendererProps } from '../NodeFactory';
import { compareNodeWithProperties } from '@/lib/memo-utils';

const VideoNodeComponent: React.FC<NodeRendererProps & React.HTMLAttributes<HTMLDivElement>> = ({ 
  node,
  ...htmlProps
}) => {
  const videoSrc = node.properties?.src as string;
  const userClassName = node.style?.className || '';

  // Default size nếu user không specify
  const defaultVideoClass = "w-64 h-48"; 

  return (
    <div 
      className={cn(
        // Default size, user có thể override
        !userClassName.match(/w-|h-/) && defaultVideoClass,
        "inline-block align-top", // Fit content exactly + remove baseline gap, no overflow-hidden for drop-shadow
        node.style?.className
      )}
      style={node.style?.css}
      {...htmlProps}
    >
      {videoSrc ? (
        <video
          src={videoSrc}
          controls
          className="w-full h-full object-cover block"
          style={{ maxWidth: '100%', maxHeight: '100%' }}
        />
      ) : (
        <div className="w-full h-full bg-gray-100 flex items-center justify-center text-gray-400">
          <span className="text-sm">No video</span>
        </div>
      )}
    </div>
  );
};

// Memoized video node để prevent re-renders
export const VideoNode = React.memo(
  VideoNodeComponent,
  compareNodeWithProperties
);

