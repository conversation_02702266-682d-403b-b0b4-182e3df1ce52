import React from "react";
import { Phone, Mail } from "lucide-react";
import { cn } from "@/lib/utils";

/**
 * Hotline item interface
 */
export interface HotlineItem {
  name: string;
  phone: string;
  email?: string;
}

/**
 * Props for HotlineInfoDisplay component
 */
export interface HotlineInfoDisplayProps {
  /** Array of hotline items to display */
  hotlines: HotlineItem[];
  /** Whether to show icons */
  showIcons?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Title for the section */
  title?: string;
}

/**
 * Clean, reusable hotline information display component
 * Suitable for both preview and public page usage
 */
export const HotlineInfoDisplay: React.FC<HotlineInfoDisplayProps> = ({
  hotlines,
  showIcons = true,
  className,
  title = "Thông tin đường dây nóng",
}) => {
  if (!hotlines || hotlines.length === 0) {
    return null;
  }

  return (
    <div className={cn("w-full space-y-6", className)}>
      {/* Section Title */}
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {title}
        </h3>
      )}

      {/* Hotlines Container - 2-column grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {hotlines.map((hotline, index) => (
          <div key={`hotline-${index}-${hotline.name}`} className="space-y-2">
            {/* Hotline Name */}
            <h4 className="font-semibold text-base text-gray-900">
              {hotline.name}
            </h4>

            {/* Contact Details */}
            <div className="space-y-1">
              {/* Phone */}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                {showIcons && <Phone className="h-4 w-4 flex-shrink-0 text-gray-500" />}
                <a
                  href={`tel:${hotline.phone}`}
                  className="hover:text-blue-600 transition-colors"
                >
                  {hotline.phone}
                </a>
              </div>

              {/* Email */}
              {hotline.email && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  {showIcons && <Mail className="h-4 w-4 flex-shrink-0 text-gray-500" />}
                  <a
                    href={`mailto:${hotline.email}`}
                    className="hover:text-blue-600 transition-colors break-all"
                  >
                    {hotline.email}
                  </a>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
