import React from "react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TreeNode } from "../../../../context/types";
import { PaddingPopover } from "../../inputs/PaddingPopover";
import {
  PADDING_GROUPS,
  PADDING_X_GROUPS,
  PADDING_Y_GROUPS,
  PADDING_T_GROUPS,
  PADDING_R_GROUPS,
  PADDING_B_GROUPS,
  PADDING_L_GROUPS,
  PADDING_S_GROUPS,
  PADDING_E_GROUPS,
} from "../../utils/paddingGroups";

interface PaddingSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

type PaddingMode =
  | "default"
  | "all"
  | "vertical"
  | "horizontal"
  | "individual"
  | "start"
  | "end";

export const PaddingSection: React.FC<PaddingSectionProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  // Detect current padding mode from className
  const getPaddingMode = (): PaddingMode => {
    const hasP = /\bp-[^\s]+/.test(currentClassName);
    const hasPx = /\bpx-[^\s]+/.test(currentClassName);
    const hasPy = /\bpy-[^\s]+/.test(currentClassName);
    const hasPt = /\bpt-[^\s]+/.test(currentClassName);
    const hasPr = /\bpr-[^\s]+/.test(currentClassName);
    const hasPb = /\bpb-[^\s]+/.test(currentClassName);
    const hasPl = /\bpl-[^\s]+/.test(currentClassName);
    const hasPs = /\bps-[^\s]+/.test(currentClassName);
    const hasPe = /\bpe-[^\s]+/.test(currentClassName);

    // Check for start mode
    if (hasPs && !hasPe) return "start";

    // Check for end mode
    if (hasPe && !hasPs) return "end";

    // If both ps and pe exist, prioritize start
    if (hasPs && hasPe) return "start";

    // Check for individual mode (any combination of pt/pr/pb/pl)
    if (hasPt || hasPr || hasPb || hasPl) return "individual";

    // Check for axis modes
    if (hasPx || hasPy) {
      return hasPx && hasPy ? "individual" : hasPx ? "horizontal" : "vertical";
    }

    // Check for all mode
    if (hasP) return "all";

    return "default";
  };

  const currentMode = getPaddingMode();

  // Extract current padding values
  const extractPaddingValue = (
    pattern: RegExp,
    defaultValue: string = "none"
  ): string => {
    const match = currentClassName.match(pattern);
    return match ? match[0] : defaultValue;
  };

  const currentP = extractPaddingValue(/\bp-[^\s]+/);
  const currentPx = extractPaddingValue(/\bpx-[^\s]+/);
  const currentPy = extractPaddingValue(/\bpy-[^\s]+/);
  const currentPt = extractPaddingValue(/\bpt-[^\s]+/);
  const currentPr = extractPaddingValue(/\bpr-[^\s]+/);
  const currentPb = extractPaddingValue(/\bpb-[^\s]+/);
  const currentPl = extractPaddingValue(/\bpl-[^\s]+/);
  const currentPs = extractPaddingValue(/\bps-[^\s]+/);
  const currentPe = extractPaddingValue(/\bpe-[^\s]+/);

  // Handle mode change - clean all padding classes and switch mode
  const handleModeChange = (mode: PaddingMode) => {
    let updatedClassName = currentClassName;

    // Remove all existing padding classes
    updatedClassName = updatedClassName
      .replace(/\bp-[^\s]+/g, "")
      .replace(/\bpx-[^\s]+/g, "")
      .replace(/\bpy-[^\s]+/g, "")
      .replace(/\bpt-[^\s]+/g, "")
      .replace(/\bpr-[^\s]+/g, "")
      .replace(/\bpb-[^\s]+/g, "")
      .replace(/\bpl-[^\s]+/g, "")
      .replace(/\bps-[^\s]+/g, "")
      .replace(/\bpe-[^\s]+/g, "")
      .trim();

    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\s+/g, " ").trim();

    // Add default padding for the new mode (except 'default' mode)
    if (mode !== "default") {
      switch (mode) {
        case "all":
          updatedClassName = `${updatedClassName} p-4`.trim();
          break;
        case "vertical":
          updatedClassName = `${updatedClassName} py-4`.trim();
          break;
        case "horizontal":
          updatedClassName = `${updatedClassName} px-4`.trim();
          break;
        case "individual":
          updatedClassName = `${updatedClassName} pt-4 pr-4 pb-4 pl-4`.trim();
          break;
        case "start":
          updatedClassName = `${updatedClassName} ps-4`.trim();
          break;
        case "end":
          updatedClassName = `${updatedClassName} pe-4`.trim();
          break;
      }
    }

    onChange({
      style: {
        ...node.style,
        className: updatedClassName,
      },
    });
  };

  // Update specific padding value
  const updatePadding = (newValue: string, paddingType: string) => {
    let updatedClassName = currentClassName;

    // Define pattern to remove based on padding type
    let removePattern: RegExp;
    switch (paddingType) {
      case "p":
        removePattern = /\bp-[^\s]+/g;
        break;
      case "px":
        removePattern = /\bpx-[^\s]+/g;
        break;
      case "py":
        removePattern = /\bpy-[^\s]+/g;
        break;
      case "pt":
        removePattern = /\bpt-[^\s]+/g;
        break;
      case "pr":
        removePattern = /\bpr-[^\s]+/g;
        break;
      case "pb":
        removePattern = /\bpb-[^\s]+/g;
        break;
      case "pl":
        removePattern = /\bpl-[^\s]+/g;
        break;
      case "ps":
        removePattern = /\bps-[^\s]+/g;
        break;
      case "pe":
        removePattern = /\bpe-[^\s]+/g;
        break;
      default:
        return;
    }

    // Remove existing class
    updatedClassName = updatedClassName.replace(removePattern, "").trim();

    // Add new class if not 'none'
    if (newValue && newValue !== "none") {
      updatedClassName = `${updatedClassName} ${newValue}`.trim();
    }

    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\s+/g, " ").trim();

    onChange({
      style: {
        ...node.style,
        className: updatedClassName,
      },
    });
  };

  return (
    <div className="space-y-4">
      {/* Mode Selector */}
      <div className="space-y-3">
        <Select
          value={currentMode}
          onValueChange={handleModeChange}
          disabled={disabled}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Chọn loại lề trong" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="default">Mặc định (không có)</SelectItem>
            <SelectItem value="all">Toàn bộ (4 cạnh)</SelectItem>
            <SelectItem value="vertical">Trên dưới (2 cạnh)</SelectItem>
            <SelectItem value="horizontal">Trái phải (2 cạnh)</SelectItem>
            <SelectItem value="individual">Độc lập (từng cạnh)</SelectItem>
            <SelectItem value="start">Lề bắt đầu</SelectItem>
            <SelectItem value="end">Lề kết thúc</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Padding Controls based on mode */}
      {currentMode === "all" && (
        <div className="space-y-2">
          <Label htmlFor="padding-all" className="text-xs text-gray-600">
            Toàn bộ
          </Label>
          <PaddingPopover
            id="padding-all"
            value={currentP}
            onValueChange={(value) => updatePadding(value, "p")}
            placeholder="Chọn lề trong"
            disabled={disabled}
            groups={PADDING_GROUPS}
          />
        </div>
      )}

      {currentMode === "vertical" && (
        <div className="space-y-2">
          <Label htmlFor="padding-vertical" className="text-xs text-gray-600">
            Trên dưới
          </Label>
          <PaddingPopover
            id="padding-vertical"
            value={currentPy}
            onValueChange={(value) => updatePadding(value, "py")}
            placeholder="Chọn lề trong trên dưới"
            disabled={disabled}
            groups={PADDING_Y_GROUPS}
          />
        </div>
      )}

      {currentMode === "horizontal" && (
        <div className="space-y-2">
          <Label htmlFor="padding-horizontal" className="text-xs text-gray-600">
            Trái phải
          </Label>
          <PaddingPopover
            id="padding-horizontal"
            value={currentPx}
            onValueChange={(value) => updatePadding(value, "px")}
            placeholder="Chọn lề trong trái phải"
            disabled={disabled}
            groups={PADDING_X_GROUPS}
          />
        </div>
      )}

      {currentMode === "individual" && (
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2">
            <Label htmlFor="padding-top" className="text-xs text-gray-600">
              Trên
            </Label>
            <PaddingPopover
              id="padding-top"
              value={currentPt}
              onValueChange={(value) => updatePadding(value, "pt")}
              placeholder="Lề trên"
              disabled={disabled}
              groups={PADDING_T_GROUPS}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="padding-right" className="text-xs text-gray-600">
              Phải
            </Label>
            <PaddingPopover
              id="padding-right"
              value={currentPr}
              onValueChange={(value) => updatePadding(value, "pr")}
              placeholder="Lề phải"
              disabled={disabled}
              groups={PADDING_R_GROUPS}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="padding-bottom" className="text-xs text-gray-600">
              Dưới
            </Label>
            <PaddingPopover
              id="padding-bottom"
              value={currentPb}
              onValueChange={(value) => updatePadding(value, "pb")}
              placeholder="Lề dưới"
              disabled={disabled}
              groups={PADDING_B_GROUPS}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="padding-left" className="text-xs text-gray-600">
              Trái
            </Label>
            <PaddingPopover
              id="padding-left"
              value={currentPl}
              onValueChange={(value) => updatePadding(value, "pl")}
              placeholder="Lề trái"
              disabled={disabled}
              groups={PADDING_L_GROUPS}
            />
          </div>
        </div>
      )}

      {currentMode === "start" && (
        <div className="space-y-2">
          <Label htmlFor="padding-start" className="text-xs text-gray-600">
            Lề bắt đầu
          </Label>
          <PaddingPopover
            id="padding-start"
            value={currentPs}
            onValueChange={(value) => updatePadding(value, "ps")}
            placeholder="Chọn lề bắt đầu"
            disabled={disabled}
            groups={PADDING_S_GROUPS}
          />
          <p className="text-xs text-gray-500">Trái (LTR) / Phải (RTL)</p>
        </div>
      )}

      {currentMode === "end" && (
        <div className="space-y-2">
          <Label htmlFor="padding-end" className="text-xs text-gray-600">
            Lề kết thúc
          </Label>
          <PaddingPopover
            id="padding-end"
            value={currentPe}
            onValueChange={(value) => updatePadding(value, "pe")}
            placeholder="Chọn lề kết thúc"
            disabled={disabled}
            groups={PADDING_E_GROUPS}
          />
          <p className="text-xs text-gray-500">Phải (LTR) / Trái (RTL)</p>
        </div>
      )}
    </div>
  );
};
