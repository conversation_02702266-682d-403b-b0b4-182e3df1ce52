/**
 * Base Post types used throughout the application
 * The content field will be typed properly in the builder context
 */

export interface Excerpt {
  image: string;
  description: string;
  metadata: Record<string, unknown>;
}

export interface Post {
  id: number;
  createdAt: number;
  updatedAt: number;
  publishedAt: number;

  title: string;
  slug: string;
  content: unknown; // Will be properly typed when extended in builder context
  excerpt: Excerpt;

  postType: PostType;
  status: PostStatus;
  authorId: string;
  parentId: number | null;
}

export type PostStatus =
  | "TRASH"
  | "PUBLISHED"
  | "UNPUBLISHED"
  | "DRAFT"
  | "REVIEW"
  | "REJECTED";
export type PostType = "ARTICLE" | "PAGE";
