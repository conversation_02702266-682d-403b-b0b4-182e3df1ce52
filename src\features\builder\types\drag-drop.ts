/**
 * Drag and Drop Types
 * Clean interface for tree drag & drop operations
 */

export type DropPosition = 'before' | 'after' | 'inside';

export interface DropTarget {
  nodeId: string;
  position: DropPosition;
}

export interface MoveOperation {
  sourceNodeId: string;
  targetNodeId: string;
  position: DropPosition;
}

export interface CalculatedMove {
  sourceNodeId: string;
  targetParentId: string;
  targetIndex: number;
}

/**
 * Convert visual drop position to actual tree operation
 */
export function calculateMoveOperation(
  sourceNodeId: string,
  targetNodeId: string,
  position: DropPosition
): MoveOperation {
  return {
    sourceNodeId,
    targetNodeId,
    position
  };
}