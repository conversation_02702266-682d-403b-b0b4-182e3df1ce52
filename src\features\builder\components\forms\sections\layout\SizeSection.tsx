import React from "react";
import { Label } from "@/components/ui/label";
import { TreeNode } from "../../../../context/types";
import { SizePopover } from "../../inputs/SizePopover";
import {
  WIDTH_GROUPS,
  HEIGHT_GROUPS,
  MIN_WIDTH_GROUPS,
  MAX_WIDTH_GROUPS,
  MIN_HEIGHT_GROUPS,
  MAX_HEIGHT_GROUPS,
} from "../../utils/sizeGroups";

interface SizeSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

export const SizeSection: React.FC<SizeSectionProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  // Extract current values from className with more specific patterns
  const extractCurrentValue = (
    classPattern: RegExp,
    defaultValue: string = "none"
  ) => {
    const match = currentClassName.match(classPattern);
    return match ? match[0] : defaultValue;
  };

  // More specific patterns to avoid matching min-w-* or max-w-* when looking for w-*
  const currentWidth = extractCurrentValue(/\b(?<!min-|max-)w-[^\s]+/, "auto");
  const currentHeight = extractCurrentValue(/\b(?<!min-|max-)h-[^\s]+/, "auto");
  const currentMinWidth = extractCurrentValue(/\bmin-w-[^\s]+/, "none");
  const currentMaxWidth = extractCurrentValue(/\bmax-w-[^\s]+/, "none");
  const currentMinHeight = extractCurrentValue(/\bmin-h-[^\s]+/, "none");
  const currentMaxHeight = extractCurrentValue(/\bmax-h-[^\s]+/, "none");

  // Update className by replacing specific class type
  const updateClassName = (
    newClass: string,
    classType: "w" | "h" | "min-w" | "max-w" | "min-h" | "max-h"
  ) => {
    let updatedClassName = currentClassName;

    // Define pattern based on class type to remove only related classes
    let removePattern: RegExp;
    switch (classType) {
      case "w":
        removePattern = /\b(?<!min-|max-)w-[^\s]+/g;
        break;
      case "h":
        removePattern = /\b(?<!min-|max-)h-[^\s]+/g;
        break;
      case "min-w":
        removePattern = /\bmin-w-[^\s]+/g;
        break;
      case "max-w":
        removePattern = /\bmax-w-[^\s]+/g;
        break;
      case "min-h":
        removePattern = /\bmin-h-[^\s]+/g;
        break;
      case "max-h":
        removePattern = /\bmax-h-[^\s]+/g;
        break;
    }

    // Remove existing class of the same type
    updatedClassName = updatedClassName.replace(removePattern, "").trim();

    // Add new class if provided and it's not 'auto' or 'none'
    if (newClass && newClass !== "auto" && newClass !== "none") {
      updatedClassName = `${updatedClassName} ${newClass}`.trim();
    }

    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\s+/g, " ").trim();

    onChange({
      style: {
        ...node.style,
        className: updatedClassName,
      },
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <div className="grid grid-cols-2 gap-4">
          {/* Width Column */}
          <div className="space-y-3">
            <div className="space-y-2">
              <Label htmlFor="size-width" className="text-xs">
                Chiều rộng
              </Label>
              <SizePopover
                id="size-width"
                value={currentWidth}
                onValueChange={(value) => updateClassName(value, "w")}
                groups={WIDTH_GROUPS}
                placeholder="Chọn chiều rộng"
                disabled={disabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="size-min-width" className="text-xs">
                Tối thiểu
              </Label>
              <SizePopover
                id="size-min-width"
                value={currentMinWidth}
                onValueChange={(value) => updateClassName(value, "min-w")}
                groups={MIN_WIDTH_GROUPS}
                placeholder="Chọn tối thiểu"
                disabled={disabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="size-max-width" className="text-xs">
                Tối đa
              </Label>
              <SizePopover
                id="size-max-width"
                value={currentMaxWidth}
                onValueChange={(value) => updateClassName(value, "max-w")}
                groups={MAX_WIDTH_GROUPS}
                placeholder="Chọn tối đa"
                disabled={disabled}
              />
            </div>
          </div>

          {/* Height Column */}
          <div className="space-y-3">
            <div className="space-y-2">
              <Label htmlFor="size-height" className="text-xs">
                Chiều cao
              </Label>
              <SizePopover
                id="size-height"
                value={currentHeight}
                onValueChange={(value) => updateClassName(value, "h")}
                groups={HEIGHT_GROUPS}
                placeholder="Chọn chiều cao"
                disabled={disabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="size-min-height" className="text-xs">
                Tối thiểu
              </Label>
              <SizePopover
                id="size-min-height"
                value={currentMinHeight}
                onValueChange={(value) => updateClassName(value, "min-h")}
                groups={MIN_HEIGHT_GROUPS}
                placeholder="Chọn tối thiểu"
                disabled={disabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="size-max-height" className="text-xs">
                Tối đa
              </Label>
              <SizePopover
                id="size-max-height"
                value={currentMaxHeight}
                onValueChange={(value) => updateClassName(value, "max-h")}
                groups={MAX_HEIGHT_GROUPS}
                placeholder="Chọn tối đa"
                disabled={disabled}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
