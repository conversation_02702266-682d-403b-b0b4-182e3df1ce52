import React, { use<PERSON>emo, useCallback } from "react";
import { TreeNode } from "../../../../context/types";
import { ColorPicker } from "../../inputs/ColorPicker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { safeMatch, combineClassNames } from "../../utils/classNameUtils";

interface TypographySectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const FONT_SIZE_OPTIONS = [
  { value: "text-xs", label: "12px (Rất nhỏ)", display: "12px" },
  { value: "text-sm", label: "14px (Nhỏ)", display: "14px" },
  { value: "text-base", label: "16px (<PERSON><PERSON> bản)", display: "16px" },
  { value: "text-lg", label: "18px (Lớn)", display: "18px" },
  { value: "text-xl", label: "20px (Rất lớn)", display: "20px" },
  { value: "text-2xl", label: "24px (Tiêu đề nhỏ)", display: "24px" },
  { value: "text-3xl", label: "30px (Tiêu đề)", display: "30px" },
  { value: "text-4xl", label: "36px (Tiêu đề lớn)", display: "36px" },
  { value: "text-5xl", label: "48px (Tiêu đề rất lớn)", display: "48px" },
  { value: "text-6xl", label: "60px (Tiêu đề khổng lồ)", display: "60px" },
];

const FONT_WEIGHT_OPTIONS = [
  { value: "font-thin", label: "100 (Mỏng)", display: "100" },
  { value: "font-extralight", label: "200 (Rất nhẹ)", display: "200" },
  { value: "font-light", label: "300 (Nhẹ)", display: "300" },
  { value: "font-normal", label: "400 (Bình thường)", display: "400" },
  { value: "font-medium", label: "500 (Trung bình)", display: "500" },
  { value: "font-semibold", label: "600 (Hơi đậm)", display: "600" },
  { value: "font-bold", label: "700 (Đậm)", display: "700" },
  { value: "font-extrabold", label: "800 (Rất đậm)", display: "800" },
  { value: "font-black", label: "900 (Đậm nhất)", display: "900" },
];

const TEXT_ALIGN_OPTIONS = [
  { value: "text-left", label: "Trái", display: "Trái" },
  { value: "text-center", label: "Giữa", display: "Giữa" },
  { value: "text-right", label: "Phải", display: "Phải" },
  { value: "text-justify", label: "Căn đều", display: "Căn đều" },
];

const LINE_HEIGHT_OPTIONS = [
  { value: "leading-3", label: "0.75 (Rất sát)", display: "0.75" },
  { value: "leading-4", label: "1 (Sát)", display: "1" },
  { value: "leading-5", label: "1.25 (Hơi sát)", display: "1.25" },
  { value: "leading-6", label: "1.5 (Bình thường)", display: "1.5" },
  { value: "leading-7", label: "1.75 (Hơi rộng)", display: "1.75" },
  { value: "leading-8", label: "2 (Rộng)", display: "2" },
  { value: "leading-9", label: "2.25 (Rất rộng)", display: "2.25" },
  { value: "leading-10", label: "2.5 (Cực rộng)", display: "2.5" },
  { value: "leading-none", label: "1 (Line-height none)", display: "1" },
  { value: "leading-tight", label: "1.25 (Chặt)", display: "1.25" },
  { value: "leading-snug", label: "1.375 (Vừa)", display: "1.375" },
  { value: "leading-normal", label: "1.5 (Normal)", display: "1.5" },
  { value: "leading-relaxed", label: "1.625 (Thoải mái)", display: "1.625" },
  { value: "leading-loose", label: "2 (Lỏng)", display: "2" },
];

const TEXT_DECORATION_OPTIONS = [
  { value: "none", label: "Không", display: "Không" },
  { value: "underline", label: "Gạch dưới", display: "Gạch dưới" },
  { value: "overline", label: "Gạch trên", display: "Gạch trên" },
  { value: "line-through", label: "Gạch ngang", display: "Gạch ngang" },
];

const removeTypographyClasses = (className: string): string => {
  return className
    .split(/\s+/)
    .filter((cls) => {
      // Remove text size classes
      if (/^text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl)$/.test(cls))
        return false;
      // Remove font weight classes
      if (
        /^font-(thin|extralight|light|normal|medium|semibold|bold|extrabold|black)$/.test(
          cls
        )
      )
        return false;
      // Remove text alignment classes
      if (/^text-(left|center|right|justify)$/.test(cls)) return false;
      // Remove line height classes
      if (/^leading-\S+$/.test(cls)) return false;
      // Remove text color classes (but keep text-align and text-size)
      if (
        /^text-\w+(-\d+)?$/.test(cls) &&
        !/^text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|left|center|right|justify)$/.test(
          cls
        )
      )
        return false;
      // Remove text decoration classes
      if (cls === "underline" || cls === "overline" || cls === "line-through")
        return false;
      return true;
    })
    .join(" ")
    .trim();
};

const TypographySectionComponent: React.FC<TypographySectionProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  const currentState = useMemo(() => {
    const classes = currentClassName.split(/\s+/);

    // Check if typography is enabled (has any typography classes)
    const enabled = classes.some(
      (cls) =>
        /^text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|left|center|right|justify)$/.test(
          cls
        ) ||
        /^font-(thin|extralight|light|normal|medium|semibold|bold|extrabold|black)$/.test(
          cls
        ) ||
        /^leading-\S+$/.test(cls) ||
        cls === "underline" ||
        cls === "overline" ||
        cls === "line-through" ||
        (cls.startsWith("text-") &&
          !/^text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|left|center|right|justify)$/.test(
            cls
          ))
    );

    // Parse font size
    const fontSize = safeMatch(
      currentClassName,
      /\btext-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl)\b/,
      "text-base"
    );

    // Parse font weight
    const fontWeight = safeMatch(
      currentClassName,
      /\bfont-(thin|extralight|light|normal|medium|semibold|bold|extrabold|black)\b/,
      "font-normal"
    );

    // Parse text alignment
    const textAlign = safeMatch(
      currentClassName,
      /\btext-(left|center|right|justify)\b/,
      "text-left"
    );

    // Parse line height
    const lineHeight = safeMatch(
      currentClassName,
      /\bleading-\S+\b/,
      "leading-normal"
    );

    // Parse text color (exclude size and alignment classes)
    let textColor = "";
    for (const cls of classes) {
      if (
        cls.startsWith("text-") &&
        !/^text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|left|center|right|justify)$/.test(
          cls
        )
      ) {
        textColor = cls.replace("text-", "");
        break;
      }
    }

    // Parse text decoration
    const textDecoration = classes.includes("underline")
      ? "underline"
      : classes.includes("overline")
      ? "overline"
      : classes.includes("line-through")
      ? "line-through"
      : "none";

    return {
      enabled,
      fontSize,
      fontWeight,
      textAlign,
      lineHeight,
      textColor,
      textDecoration,
    };
  }, [currentClassName]);

  const cleanClassName = useMemo(() => {
    return removeTypographyClasses(currentClassName);
  }, [currentClassName]);

  const updateClassName = (newClassName: string) => {
    onChange({
      style: {
        ...node.style,
        className: newClassName,
      },
    });
  };

  const handleToggle = useCallback(
    (enabled: boolean) => {
      if (!enabled) {
        // Disable typography - remove all typography classes
        updateClassName(cleanClassName);
      } else {
        // Enable typography - add default values
        const classes = [
          cleanClassName,
          "text-base", // Default font size
          "font-normal", // Default font weight
          "text-left", // Default alignment
          "leading-normal", // Default line height
        ];
        updateClassName(combineClassNames(...classes));
      }
    },
    [cleanClassName]
  );

  const handleChange = useCallback(
    (type: string, value: string) => {
      const newState = { ...currentState, [type]: value };

      const classes = [
        cleanClassName,
        // Font size (default is text-base, only add if different)
        newState.fontSize && newState.fontSize !== "text-base"
          ? newState.fontSize
          : "",
        // Font weight (default is font-normal, only add if different)
        newState.fontWeight && newState.fontWeight !== "font-normal"
          ? newState.fontWeight
          : "",
        // Text alignment (default is text-left, only add if different)
        newState.textAlign && newState.textAlign !== "text-left"
          ? newState.textAlign
          : "",
        // Line height (default is leading-normal, only add if different)
        newState.lineHeight && newState.lineHeight !== "leading-normal"
          ? newState.lineHeight
          : "",
        // Text color (add prefix)
        newState.textColor ? `text-${newState.textColor}` : "",
        // Text decoration (none means no class)
        newState.textDecoration && newState.textDecoration !== "none"
          ? newState.textDecoration
          : "",
      ].filter(Boolean);

      updateClassName(combineClassNames(...classes));
    },
    [cleanClassName, currentState]
  );

  return (
    <div className="space-y-3">
      {/* Typography Toggle */}
      <div className="flex items-center justify-between">
        <Label htmlFor="typography-toggle" className="text-sm font-medium">
          Áp dụng kiểu chữ
        </Label>
        <Switch
          id="typography-toggle"
          checked={currentState.enabled}
          onCheckedChange={handleToggle}
          disabled={disabled}
        />
      </div>

      {!currentState.enabled && (
        <div className="text-sm text-gray-500 italic">
          Bật switch để tùy chỉnh kiểu chữ
        </div>
      )}

      {currentState.enabled && (
        <>
          <div className="flex gap-2">
            <Select
              value={currentState.fontSize}
              onValueChange={(value) => handleChange("fontSize", value)}
              disabled={disabled}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="16px">
                  {FONT_SIZE_OPTIONS.find(
                    (opt) => opt.value === currentState.fontSize
                  )?.display || "16px"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {FONT_SIZE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={currentState.fontWeight}
              onValueChange={(value) => handleChange("fontWeight", value)}
              disabled={disabled}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="400">
                  {FONT_WEIGHT_OPTIONS.find(
                    (opt) => opt.value === currentState.fontWeight
                  )?.display || "400"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {FONT_WEIGHT_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <Select
              value={currentState.textAlign}
              onValueChange={(value) => handleChange("textAlign", value)}
              disabled={disabled}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Trái" />
              </SelectTrigger>
              <SelectContent>
                {TEXT_ALIGN_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={currentState.lineHeight}
              onValueChange={(value) => handleChange("lineHeight", value)}
              disabled={disabled}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="1.5">
                  {LINE_HEIGHT_OPTIONS.find(
                    (opt) => opt.value === currentState.lineHeight
                  )?.display || "1.5"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {LINE_HEIGHT_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <ColorPicker
              value={currentState.textColor}
              onChange={(value) => handleChange("textColor", value)}
              disabled={disabled}
              className="h-9 w-12 p-0 flex items-center justify-center"
              renderValue={(color) => (
                <div
                  className={`w-5 h-5 rounded-full border border-gray-300 ${
                    color ? `bg-${color}` : "bg-gray-200"
                  }`}
                  style={
                    color === "black"
                      ? { backgroundColor: "black" }
                      : color === "white"
                      ? { backgroundColor: "white" }
                      : undefined
                  }
                />
              )}
            />

            <Select
              value={currentState.textDecoration}
              onValueChange={(value) => handleChange("textDecoration", value)}
              disabled={disabled}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Không" />
              </SelectTrigger>
              <SelectContent>
                {TEXT_DECORATION_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </>
      )}
    </div>
  );
};

export const TypographySection = React.memo(TypographySectionComponent);
