import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ColorPicker } from './ColorPicker';
import { cn } from '@/lib/utils';

interface BorderState {
  enabled: boolean;
  style: 'solid' | 'dashed' | 'dotted' | 'double';
  sides: {
    all: { width: string; color: string; radius: string };
    top: { width: string; color: string; radius: string };
    right: { width: string; color: string; radius: string };
    bottom: { width: string; color: string; radius: string };
    left: { width: string; color: string; radius: string };
  };
}

interface BorderControlsProps {
  value: BorderState;
  onChange: (value: BorderState) => void;
  disabled?: boolean;
}

const BORDER_STYLES = [
  { value: 'solid', label: '<PERSON>ền (solid)' },
  { value: 'dashed', label: '<PERSON><PERSON><PERSON> ngang (dashed)' },
  { value: 'dotted', label: 'Chấm (dotted)' },
  { value: 'double', label: 'Đôi (double)' },
];

const BORDER_WIDTHS = [
  { value: '0', label: '0px' },
  { value: '1', label: '1px' },
  { value: '2', label: '2px' },
  { value: '4', label: '4px' },
  { value: '8', label: '8px' },
];

const BORDER_RADIUS = [
  { value: 'none', label: 'Không' },
  { value: 'sm', label: 'Nhỏ (4px)' },
  { value: 'md', label: 'Vừa (6px)' },
  { value: 'lg', label: 'Lớn (8px)' },
  { value: 'xl', label: 'Rất lớn (12px)' },
  { value: '2xl', label: 'Cực lớn (16px)' },
  { value: 'full', label: 'Tròn (50%)' },
];

const SIDE_TABS = [
  { value: 'all', label: 'Chung', icon: '⚡' },
  { value: 'top', label: 'Trên', icon: '⬆️' },
  { value: 'right', label: 'Phải', icon: '➡️' },
  { value: 'bottom', label: 'Dưới', icon: '⬇️' },
  { value: 'left', label: 'Trái', icon: '⬅️' },
];

export const BorderControls: React.FC<BorderControlsProps> = ({
  value,
  onChange,
  disabled = false
}) => {
  const [activeTab, setActiveTab] = useState<keyof BorderState['sides']>('all');

  const updateBorder = (updates: Partial<BorderState>) => {
    onChange({ ...value, ...updates });
  };

  const updateSide = (side: keyof BorderState['sides'], updates: Partial<BorderState['sides']['all']>) => {
    onChange({
      ...value,
      sides: {
        ...value.sides,
        [side]: {
          ...value.sides[side],
          ...updates
        }
      }
    });
  };

  const currentSide = value.sides[activeTab];

  return (
    <div className="space-y-4">
      {/* Enable/Disable Border */}
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Bật viền</Label>
        <Switch
          checked={value.enabled}
          onCheckedChange={(enabled) => updateBorder({ enabled })}
          disabled={disabled}
        />
      </div>

      {/* Border Settings (only show when enabled) */}
      {value.enabled && (
        <>
          {/* Border Style (Global) */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Kiểu viền chung</Label>
            <Select
              value={value.style}
              onValueChange={(style) => updateBorder({ style: style as BorderState['style'] })}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn kiểu viền" />
              </SelectTrigger>
              <SelectContent>
                {BORDER_STYLES.map((style) => (
                  <SelectItem key={style.value} value={style.value}>
                    {style.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Side-specific settings */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Thiết lập cho từng cạnh</Label>
            
            <Tabs value={activeTab} onValueChange={(tab) => setActiveTab(tab as keyof BorderState['sides'])}>
              <TabsList className="grid w-full grid-cols-5">
                {SIDE_TABS.map((tab) => (
                  <TabsTrigger
                    key={tab.value}
                    value={tab.value}
                    className="text-xs"
                    disabled={disabled}
                  >
                    <span className="mr-1">{tab.icon}</span>
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>

              {SIDE_TABS.map((tab) => (
                <TabsContent key={tab.value} value={tab.value} className="mt-4 space-y-4">
                  {/* Width */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Độ dày</Label>
                    <Select
                      value={currentSide.width}
                      onValueChange={(width) => updateSide(activeTab, { width })}
                      disabled={disabled}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn độ dày" />
                      </SelectTrigger>
                      <SelectContent>
                        {BORDER_WIDTHS.map((width) => (
                          <SelectItem key={width.value} value={width.value}>
                            {width.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Color */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Màu sắc</Label>
                    <ColorPicker
                      value={currentSide.color}
                      onChange={(color) => updateSide(activeTab, { color })}
                      disabled={disabled}
                      placeholder="Chọn màu viền"
                    />
                  </div>

                  {/* Radius (only for corners that make sense) */}
                  {(activeTab === 'all' || activeTab === 'top' || activeTab === 'bottom') && (
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">
                        {activeTab === 'all' ? 'Bo góc' : `Bo góc ${tab.label.toLowerCase()}`}
                      </Label>
                      <Select
                        value={currentSide.radius}
                        onValueChange={(radius) => updateSide(activeTab, { radius })}
                        disabled={disabled}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn bo góc" />
                        </SelectTrigger>
                        <SelectContent>
                          {BORDER_RADIUS.map((radius) => (
                            <SelectItem key={radius.value} value={radius.value}>
                              {radius.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </TabsContent>
              ))}
            </Tabs>
          </div>

          {/* Preview */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Xem trước</Label>
            <BorderPreview borderState={value} />
          </div>
        </>
      )}
    </div>
  );
};

// Border Preview Component
interface BorderPreviewProps {
  borderState: BorderState;
}

const BorderPreview: React.FC<BorderPreviewProps> = ({ borderState }) => {
  if (!borderState.enabled) {
    return (
      <div className="w-full h-16 bg-gray-100 rounded flex items-center justify-center text-gray-400 text-sm">
        Không có viền
      </div>
    );
  }

  // Generate preview classes based on border state
  const generatePreviewClasses = () => {
    const classes: string[] = ['w-full', 'h-16', 'bg-gray-50'];
    
    const { sides, style } = borderState;
    
    // Add border style (applies to all sides)
    if (style !== 'solid') {
      classes.push(`border-${style}`);
    }
    
    // Add border classes for each side
    Object.entries(sides).forEach(([side, config]) => {
      if (side === 'all') {
        // Apply to all sides
        if (config.width && config.width !== '0') {
          classes.push(`border-${config.width}`);
        }
        if (config.color) {
          classes.push(`border-${config.color}`);
        }
        if (config.radius && config.radius !== 'none') {
          classes.push(config.radius === 'full' ? 'rounded-full' : `rounded-${config.radius}`);
        }
      } else {
        // Apply to specific side
        const sidePrefix = side === 'top' ? 't' : side === 'right' ? 'r' : side === 'bottom' ? 'b' : 'l';
        
        if (config.width && config.width !== '0') {
          classes.push(`border-${sidePrefix}-${config.width}`);
        }
        if (config.color) {
          classes.push(`border-${sidePrefix}-${config.color}`);
        }
        if (config.radius && config.radius !== 'none' && (side === 'top' || side === 'bottom')) {
          const radiusPrefix = side === 'top' ? ['tl', 'tr'] : ['bl', 'br'];
          radiusPrefix.forEach(corner => {
            classes.push(config.radius === 'full' ? `rounded-${corner}-full` : `rounded-${corner}-${config.radius}`);
          });
        }
      }
    });
    
    return classes.join(' ');
  };

  return (
    <div className="p-4 border rounded-lg bg-white">
      <div className={cn(generatePreviewClasses())}>
        <div className="w-full h-full flex items-center justify-center text-gray-600 text-sm">
          Preview
        </div>
      </div>
    </div>
  );
};
