import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { ChevronDown, Check } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ZIndexGroup } from "../utils/zIndexGroups";

interface ZIndexPopoverProps {
  id?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  groups: ZIndexGroup[];
}

export const ZIndexPopover: React.FC<ZIndexPopoverProps> = ({
  value = "none",
  onValueChange,
  placeholder = "Chọn thứ tự lớp",
  disabled = false,
  groups,
}) => {
  const [open, setOpen] = useState(false);

  // Find current option to display
  const getCurrentOption = () => {
    if (!value || value === "none") return null;

    for (const group of groups) {
      const option = group.options.find((opt) => opt.value === value);
      if (option) return option;
    }
    return null;
  };

  const currentOption = getCurrentOption();

  const handleValueSelect = (newValue: string) => {
    onValueChange?.(newValue);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-8 w-full justify-between text-xs",
            !currentOption && "text-muted-foreground",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
        >
          {currentOption ? (
            <span className="truncate">
              {currentOption.label}
              {currentOption.description && (
                <span className="text-gray-500 ml-1">
                  ({currentOption.description})
                </span>
              )}
            </span>
          ) : (
            placeholder
          )}
          <ChevronDown className="ml-2 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        {groups.length > 1 ? (
          <Tabs defaultValue={groups[0]?.id} className="w-full">
            <TabsList
              className="grid w-full h-8"
              style={{ gridTemplateColumns: `repeat(${groups.length}, 1fr)` }}
            >
              {groups.map((group) => (
                <TabsTrigger
                  key={group.id}
                  value={group.id}
                  className="text-xs px-1"
                >
                  {group.label}
                </TabsTrigger>
              ))}
            </TabsList>

            {groups.map((group) => (
              <TabsContent
                key={group.id}
                value={group.id}
                className="mt-0 max-h-64 overflow-y-auto"
              >
                <div className="p-2">
                  <div className="grid grid-cols-3 gap-1">
                    {group.options.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => handleValueSelect(option.value)}
                        className={cn(
                          "flex flex-col items-center px-2 py-2 text-xs rounded hover:bg-gray-100 transition-colors relative",
                          value === option.value &&
                            "bg-blue-100 text-blue-900 ring-1 ring-blue-500"
                        )}
                        title={option.description}
                      >
                        <span className="font-medium text-center leading-tight">
                          {option.label}
                        </span>
                        {option.description && (
                          <span className="text-gray-500 text-xs mt-0.5 text-center leading-tight">
                            {option.description}
                          </span>
                        )}
                        {value === option.value && (
                          <Check className="h-3 w-3 absolute top-0.5 right-0.5" />
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        ) : (
          // Single group - no tabs, just grid
          <div className="max-h-64 overflow-y-auto">
            <div className="p-2">
              <div className="grid grid-cols-3 gap-1">
                {groups[0]?.options.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleValueSelect(option.value)}
                    className={cn(
                      "flex flex-col items-center px-2 py-2 text-xs rounded hover:bg-gray-100 transition-colors relative",
                      value === option.value &&
                        "bg-blue-100 text-blue-900 ring-1 ring-blue-500"
                    )}
                    title={option.description}
                  >
                    <span className="font-medium text-center leading-tight">
                      {option.label}
                    </span>
                    {option.description && (
                      <span className="text-gray-500 text-xs mt-0.5 text-center leading-tight">
                        {option.description}
                      </span>
                    )}
                    {value === option.value && (
                      <Check className="h-3 w-3 absolute top-0.5 right-0.5" />
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};
