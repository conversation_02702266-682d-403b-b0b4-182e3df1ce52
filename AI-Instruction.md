# Instruction 01:

<PERSON>úng ta đang xây dựng Web UI builder
Chúng ta đã hoàn thành:
- Context quản lý trạng thái của editor
- <PERSON><PERSON><PERSON> thả các node ở Tree
- Resize panel ở layout 


Bạn cần review lại code base cũ để hiểu rõ kiến trúc và cách tổ chức code cũng như phương pháp tiếp cận xử lý các vấn đề.
Bạn chắc chắn mọi code chúng ta thêm vào đều tuân thủ quy tắc về lean code, best practices với khả năng thực hiện chức 
năng ổn định và mở rộng trong tương lai.
Chúng ta không cần thực hiện nhiều nhiệm vụ ở một thời điểm, thay vào đó là tập trung vào một nhiệm vụ cụ thể ở một thời 
điểm và hoàn thành nó cẩn thận đồng thời được kiểm thử và sửa lỗi kĩ lưỡng trước khi tiến hành các nhiệm vụ mới.

Nhiệm vụ hiện tại là xây dựng chức năng kéo thả các node ở Canvas.
Trước tiên bạn cần đề xuất hướng xử lý, trao đổi chi tiết về UX để biết rõ chúng ta cần làm gì trước khi bắt đầu cài đặt.
Khi có bất kì nội dung nào bạn chưa hiểu rõ bạn hãy trao đổi cụ thể với tôi.

UX dự kiến của tôi như sau:
Khi chúng ta bắt đầu kéo một node trên cavas, đầu tiên nó đó sẽ được làm nổi bật
Khi chúng ta di chuyển node được kéo sẽ được overlay theo chuột, khi đến vị trí có thể chèn vào được, node đó sẽ
chèn vào vị trí đó. 
Nếu người dùng dừng lại, quá trình di chuyển hoàn tất.
Nếu người dùng tiếp tục di chuột đến vị trí mới thì node lại cập nhật theo.
Nếu người dùng di chuột qua vị trí không thể chèn node vào thì node sẽ trở về trạng thái đầu tiên khi bắt đầu kéo.

Bạn hãy phân tích UX trên và đề xuất thuật toán xử lý state và giải pháp.
Nếu UX có phần nào chưa rõ hãy hỏi lại tôi để không hiểu nhầm.

Node không phải được overlay theo chuột, mà một phiên bản overlay của node sẽ di chuyển cùng chuột khi người dùng
kéo thả. Khi tìm được vị trí đề chèn vào, overlay sẽ biến mất để node được chọn chèn vào vị trí cũ. Khi người dùng 
di chuột mà chưa tìm được vị trí chèn vào thì node lại trở về vị trí xuất phát và overlay lại hiện trở lại.

# Instruction 02:

Vị trí có thể chèn xác định ý hệt như khi chúng ta kéo thả node cây, có 03 trường hợp:
1. Chèn trước node chúng ta hover qua
2. Chèn sau node chúng ta hover qua
3. Trở thành con của của node chúng ta hover qua (Nếu đó là Frame và đang trống. Nếu frame không trống thì sẽ rơi vào trường hợp 1 và 2 vì Frame trên canvas không thể collapsed như trên Tree)
Chúng ta cần tính toán để cập nhật trạng thái cây liên tục để render kết quả theo quá trình chuột di chuyển.

Node sẽ di chuyển thật sự, over lay sẽ biến mất chứ không chỉ là placeholder, khi thả chuột ra thì quá trình di chuyển 
hoàn thành và dừng ở trạng thái hiện tại luôn. Các trạng thái về quá trình kéo thả sẽ được reset (không còn node nào được chọn).

Khi di chuyển qua vùng cấm thì không cần indicator gì cả, đơn giản là đưa node trả về vị trí xuất phát và hiển thị overlay.
Trong quá trình di chuyển này chúng ta không dùng indicator mà hoàn toàn là cập nhật vị trí node thật sự. Có điều ở giữa quá trình
di chuyển thì node được chọn sẽ được làm nổi bật, nhưng sau khi thả chuột thì sẽ không có node nào được chọn nữa.

Instruction 03:

Bạn hãy bắt đầu cài đặt đi. Hãy nhớ dự án này dùng strict typescript (không có any) và dùng yarn.
Tôi đã chạy server yarn dev trước đó, khi nào bạn cài đặt xong chúng ta sẽ cùng kiểm tra lại.

Instruction 04:
Hiện tại có một số vị trí nhạy cảm vị trí node được cập nhật liên tục dẫn đền màn hình bị giật.
Hãy tăng khoảng cách di chuyển tối thiểu để tính toán lại vị trí.
Hãy bỏ hết các khung không liên quan đến quá trình di chuyển đi (khung bọc, nhãn chỉ loại node ....)
để UX chỉ tập trung vào hiển thị nguyên bản. 
Node được chọn hiện tại vẫn chưa đủ nổi bật lắm, hãy làm nổi bật hơn nữa. Có thể là đổi nền màu vàng và bọc thêm khung như bên Tree.

Instruction 05:
UX vẫn chưa rõ ràng lắm, bạn làm nổi bật overlay nhưng lại chưa làm nổi bật chính node đang được chọn.
Những khung và nhãn bọc node cũ vẫn còn và vẫn hiện ra trong quá trình di chuyển gây khó quá sát.
Trong qúa trình duy chuyển đối tượng nên được quan tâm chú ý là đối tượng di chuyển chứ không phải các 
đối tượng khác. Do đó các đối tượng khác thì giữ nguyên bản, riêng overlay và đối tượng được chọn thì phải được làm nổi bật.

Instruction 06:
Tôi đã dọn dẹp ổ cứng, hãy tiếp tục công việc. Nếu không nhớ điều gì hãy hỏi lại tôi.

Instruction 07:
Tôi thấy cứ mỗi lần hover chuột trên cavans các node sẽ hiện khụng đen và có nhãn báo đó node loại gì.
Hãy loại bỏ việc này hoàn toàn vì nó không cần thiết. Chúng ta đã có properties panal, Tree node, 
và nội dung của chính nó quá đủ để xác định nó là node gì. Còn thể hiện phạm vi node chỉ nên hiển thị khi node đó
đang được chọn còn không thì không cần thiết.

Instruction 08:
Mọi thứ đã dễ nhìn hơn, tuy nhiên tôi chưa hiểu event nào sẽ dừng việc drag and drop khi mà tôi chưa thả chuột ra thì đã 
kết thúc. Kể cả khi đã tìm được vị trí mới thì tôi chưa thả chuột thì việc drag and drop vẫn chưa dừng lại.

Instruction 09:

UX đang bị loạn vì selection, có cách nào tắt hoàn toàn sự kiện selection để tránh nhiễu.
Khi chọn một node nó đang select luôn cả node cha bên ngoài và chính nó, thực tế chỉ cho phép chọn 1 node để kéo thả ở một thời điểm 
không thể chọn 02 node 1 lần được.

Instruction 10:
Việc kéo thả vẫn dừng ngay cả khi tôi chưa rời tay khỏi chuột.
Border nên hiện bên trong thay vì bên ngoài vì nó đang che cả node khác.

Instruction 11:
Giờ việc kéo thả đã không thực hiện được nữa, mọi việc đã thật sự rối loạn.
Theo tôi nguyên nhân chính là ngay từ đầu chúng ta đã không tách biệt các nhóm trạng thái với nhau 
nên sự kiện chuột bị chồng chéo lẫn nhau.

Trước hết chúng ta cần phân rõ 03 trường hợp:
Xem: Chuột không thể tương tác hay làm bất kì việc gì
Sửa: Chuột chỉ có thể click chọn 01 node ở một thời điểm và sẽ hiện thị 
tương ứng ở Tree và Properties Panel.
Di chuyển: Chuột có thể chọn 01 node ở một thời điểm nhưng highlight khác. Khi kéo thả sẽ xử lý 
logic kéo thả và chỉ kết thúc khi thả chuột ra.

Việc bắt chuyển đổi giữa sự kiện Sửa và Di chuyển cũng như ngược lại rất quan trọng.
Khi ở trạng thái sửa, khi người dùng bắt đầu dí chuột không thả và di một đoạn (cấu hình được)
thì sẽ rơi vào trạng thái Di chuyển, khi đó node liên quan nhất sẽ được chọn. Không còn node được 
chọn ở mode Sửa nữa dù trước đó nó có là bất kì node nào. Sau đó chúng ta chỉ xử lý sự kiện chuột 
trong mode di chuyển thôi chứ không bị nhập nhằng với mode sửa. Vốn chỉ quan tâm sự kiện onclick.
Khi nào người dùng thả chuột ra thì mode di chuyển cũng tự kết thúc và chuyển sang mode sửa.

Flow tôi mô tả vậy bạn đã rõ chưa? Nếu chưa rõ hay thấy không logic ở đâu hãy thảo luận thêm với tôi.


Insstructor 12:
Move không có node để chuyển trạng thái mà nó sẽ một mode bên trong mode sửa.
Khi ở mode di chuyển viền có màu vàng, còn khi ở mode sửa viện có màu cam. 
Bản chất là mode sửa chỉ nhận sự kiện onclick lên 1 node và chọn node đó. 
Còn mode di chuyển được đánh dấu khi người dùng bắt đầu di chuột không thả (drag)
một khoảng nào đó. Đó đúng là Threshold mà bạn nhắc đến.


Instruction 13:
Tôi thấy bạn cài đặt chuyển đổi giữa các trạng thái vẫn không đúng. Để dễ debug và theo dõi hơn 
bạn hãy bổ sung một tab Di chuyển cùng với tab Sửa và Xem hiện tại.
Khi click vào tab đó sẽ là mode di chuyển. Khi đó khi người dùng click vào một node sẽ sẽ 
trở thành chọn node đó. Không còn bắt selection bằng bắt di chuột nữa dễ nhầm. Logic hãy dừng ở đó 
để tôi kiểm tra lại tính đúng đắn đã rồi hãy tiếp tục. Vì cài đặt nhiều mà không đúng sửa đi sửa 
lại mất thời gian mà vẫn không đạt được kết quả. Hãy xử lý logic theo từng step nhỏ.

Instruction 14:
Tiếc là logic hiện tại đang sai từ căn cơ nhất. Ở mode View / Edit / Move
hiển thị hoàn toàn khác nhau. Logic thực tế không phải như thế. Dù ở mode nào chúng 
cũng hiển thị như nhau, thì khác là có viền khi được chọn hay không thôi. Còn các hiển thị khác 
là y hệt nhau. Ví dụ 1 frame trống bên mode edit chiếm 48 px chiêu vào thì bên mode view cũng 
phải y hệt vậy chứ không phải vì nó trống mà bỏ hiển thị của nó đi. Như vậy thì làm sao 
mà edit được.
Bạn cần xử lý vấn đề cốt lõi trong hiển thị node đó trước khi bắt đầu tính đến những việc sau.

Sau khi giải quyết được hiển thì chúng ta mới tính tiếp đến việc thể hiện nó khi nó ở trạng thái
được chọn. Khi được chọn tức là nó đang nhận focus, nó sẽ có viền bọc xung quanh để nổi bật hơn. 
Những viền không được chọn hoặc không ở trạng thái liên quan thì không cần quan tâm và đầu cần hiện 
thị viền hay bất kì indicator nào cả.

Tôi sẽ mô tả lại UX của figma để bạn học hỏi cách xử lý:
Ở mode sửa:
Một node ở hiển thị trên màn hình nếu không ai can thiệp sẽ luôn ở trạng thái nguyên bản của nó.
Khi có chuột click vào thì nó ở trạng thái chọn và có viền mảnh màu xanh dương. Ở mỗi thời điểm chỉ có thể chọn 01
node do đó khi click vào node khác thì node cũ sẽ mất chọn.
Ở mode di chuyển:
Một node ở hiển thị trên màn hình nếu không ai can thiệp sẽ luôn ở trạng thái nguyên bản của nó.
Khi có chuột click vào thì nó ở trạng thái chọn và có viền mảnh màu cam. Ở mỗi thời điểm chỉ có thể chọn 01
node do đó khi click vào node khác thì node cũ sẽ mất chọn.
Ở mode xem:
Không ai có thể click chuột nhưng vẫn hiển thị nguyên bản của nó.



Instruction 15:
Tất cả các viền thể hiện trạng thái của một node luôn ở bên trong không ở bên ngoài để tránh hiện tượng 
khi 2 node ở gần nhau sẽ che mất nội dung của nhau.
Việc hiện thị đang tốt hơn, tuy nhiên tôi muốn có một số điều chỉnh sau:
Đối với Frame hãy hiển thị nó bằng viền mảnh dạng dashed màu xám để thể hiện khu vực nó chiếm trên canvas.

Instruction 16:
Ở mode View thì Frame không cần hiện viền (vẫn chiếm diện tích)
Tôi thấy các Node đang mặc định bo tròn (điều này không đúng hãy để nó là hình chữ nhật nguyên bản)
Ở node hình ảnh thì viền bên trong lại không thể hiển thị vì có điều gì đó đã che mất (có thể là ảnh)
hãy sửa bugs này để dù là node nào thì cũng luôn thấy viền khi được chọn.

Instruction 17:
Tôi muốn đồng bộ lại UI như sau:
Ở các tab mobile / tablet / desktop / move / edit / view thì bỏ hết text chỉ giữ lại icon 
và kích thước bằng nhau để UI cần xứng.

Ở trên canvas không dúng tiếng Anh mà dùng tiếng Việt thể thiện rõ tên:
Sửa | Xem | di chuyển (hiện tại mode di chuyển đang là preview mode còn sai)

Khi chuyển tab màu nền đang là xanh dương, xanh là và vàng, không cần nhiều màu vậy, theo đúng 
quy tắc của tab chỉ có duy nhất tab được chọn là màu xanh dương. Style này cần đồng bộ với cả 
tab bên mobile / tablet / desktop vì tôi thấy cùng là tab nhưng style khác nhau. 
Kể cả bên properties pannel thì Thông tin và Chi tiết cũng là tab nên cũng phỉa có style đồng nhất.

Instruction 18:
Có lỗi gì đó đang xảy ra:
Unexpected Application Error!
`RovingFocusGroupItem` must be used within `RovingFocusGroup`
useContext22@https://localhost:5173/node_modules/.vite/deps/@radix-ui_react-tabs.js:56:22
RovingFocusGroupItem@https://localhost:5173/node_modules/.vite/deps/@radix-ui_react-tabs.js:406:42
react-stack-bottom-frame@https://localhost:5173/node_modules/.vite/deps/react-dom_client.js:17424:29
renderWithHooks@https://localhost:5173/node_modules/.vite/deps/react-dom_client.js:4206:42
updateForwardRef@https://localhost:5173/node_modules/.vite/deps/react-dom_client.js:6461:36
runWithFiberInDEV@https://localhost:5173/node_modules/.vite/deps/react-dom_client.js:1487:23
performUnitOfWork@https://localhost:5173/node_modules/.vite/deps/react-dom_client.js:10868:115
workLoopSync@https://localhost:5173/node_modules/.vite/deps/react-dom_client.js:10728:60
renderRootSync@https://localhost:5173/node_modules/.vite/deps/react-dom_client.js:10711:25
performWorkOnRoot@https://localhost:5173/node_modules/.vite/deps/react-dom_client.js:10359:60
performWorkOnRootViaSchedulerTask@https://localhost:5173/node_modules/.vite/deps/react-dom_client.js:11623:26
performWorkUntilDeadline@https://localhost:5173/node_modules/.vite/deps/react-dom_client.js:36:58
💿 Hey developer 👋
You can provide a way better UX than this when your app throws errors by providing your own ErrorBoundary or errorElement prop on your route.

Instruction 19:
Ở trên canvas đang hiện mode sai: Preview Mode cho và di chuyển (hãy việt hoá)
Ngoài ra ở cạnh kích thước pixcel hãy hiện thêm loại màn hình:
375px/điện thoại máy tính bảng máy tính ...

Instruction 20:
Hiện tại frame đang mặc định là p-4, hãy bỏ điều này, mặc định là p-0.

Instruction 21:
Hiện tại các style được định nghĩa trong properties form chưa có tác dụng lên node.
Hãy chỉnh node để nhận className và styles từ props truyền vào cho các node.
Đừng dùng min-h-[100px] hãy ưu tiên dùng tailwindcss ví dụ w-h-24.

Instruction 22:
Frame đang để min-h-24 chỉ khi nó trống thôi, còn bình thường nó sẽ fit theo kích 
thước của các node con bên trong. Nó không có className nào mặc định hay style nào cả 
tất cả đều trống.


Instruction 23:
Tôi thấy các node đang được định nghĩa hết trong NodeRenderer. Điều này sẽ khó khăn khi mở rộng thêm các loại node khác.
Hãy tạo node factory để thuận lợi cho mở rộng trong tương lai. Chia các loại node thành 03 nhóm: Cơ bản, Mẫu và Tiện ích.
Cơ bản sẽ gồm các loại cơ bản như Frame Text Image Video ... Còn Mẫu và Tiện ích là các loại đã được định nghĩa trước.
Khi đưa vào builder chúng vẫn có thể là con của Frame như các node thông thường.
Chúng không có con.
Mẫu và tiện ích đều được bọc trong một thẻ html và vẫn có thể nhận 
className và styles thể tác động lên frame ngoài cùng.
Nội dung bên trong của chúng sẽ định nghĩa theo properties và có thể cấu hình được trong propeties form. 
Hãy tham khảo props tryền vào cho mọi loại node ở đây:

export interface TreeNode {
  id: string;
  label: string;
  type: TreeNodeType;
  group: TreeNodeGroup;
  style?: {
    className?: string;
    css?: React.CSSProperties;
  };
  properties?: Record<string, TreeNodeProperty>;
  children: TreeNode[];
}

Nếu bạn chưa rõ về về mẫu và tiện ích bạn có thể hỏi thêm để rõ hơn. Hiện tại chưa có Node nào thuộc loại mẫu và tiện ích do đó bạn hãy tạo trước 
1 template là ImageWithCaption (bạn có thể css mặc định hiển thị cho đẹp) còn properties ban đầu sẽ là 
{
    "src":"",
    "alt":""
}

Và 1 tiện ích là DateTimeCard hiển thị ngày giờ hiện tại.

Sau tới UX thêm node sẽ phải thay đổi nhưng bạn chưa cần cài đặt ngay, hãy xử lý phần node factory cho tốt đã, rồi 
tôi sẽ bạn về UX của việc thêm 1 node trên builder diễn ra thế nào.

Instruction 24:
Trong TreeNode đã có định nghĩa về:
export interface TreeNode {
  id: string;
  label: string;
  type: TreeNodeType;
  group: TreeNodeGroup;
  style?: {
    className?: string;
    css?: React.CSSProperties;
  };
  properties?: Record<string, TreeNodeProperty>;
  children: TreeNode[];
}
Trong đó có phần style style?: {
    className?: string;
    css?: React.CSSProperties;
  }; chính là className và style bạn định nghĩa lặp lại trong NodeRendererProps.

  Bạn hãy xem và xử lý lại phần này, đừng định nghĩa lặp lại mà hãy dùng luôn trong TreeNode
  để dễ cấu hình trong properties panel.

Instruction 25:
UX thêm node sẽ như sau:
Sẽ có Icon Plus ở header bện cạnh đã lưu.
Khi người dùng click vào icon Plus sẽ hiện ra popup để chọn loại node để thêm.
Các loại  node được chia thành 3 tab: Cơ bản, mẫu và tiện ích (mặc định là ở tab cơ bản). 
Có ô tìm kiếm để tìm kiếm nhanh để tìm node ở các 3 tabs.
Khi chọn node nào nó sẽ thêm vào layout và đóng popup lại.
Node sẽ được thêm vào vị trí phụ thuộc vào node đang được chọn hiện tại.
1. Nếu là Frame (tính cả root) thì trở thành con cuối cùng
2. Nếu là Node không phải frame thì thêm vào ngay sau node đó.
3. Nếu không có node nào được chọn thì thêm vào root.

Sau khi thêm xong thì focus sẽ chọn nào node đó luôn. 
Lưu ý chỉ thêm được Node khi đang ở mode sửa. Còn các mode khác thì disable icon Plus đi.

Hãy cân đối UI cho hợp lý.


Instruction 26:
Nút + đang hơi khó nhìn thấy, hãy để cạnh tab chuyển mode (edit/view/mode) 
để dễ nhìn. (Cạnh chứ không phải trở thành tab)
Dialog to lên và không bị nhảy kích thước khi chuyển tab vì số item bị ít đi. 
Hỗ trợ tìm kiếm không dấu.
HIện tại card hiện thị các node trong Dialog đang bị lỗi tràn text hướng dẫn
ví dụ node Frame.
UI của Dialog đang xấu do UI mặc định của Shadcn cho Dialog xấu, hãy tìm cách để 
nó đẹp hơn. Người dùng dễ theo dõi và tìm kiếm node hơn.

Instruction 27:
Dialog không bị nhảy nữa nhưng UI không đẹp hơn. Dialog mặc 
định của Shadcn khá xấu. 
Hiện tại có tận 2 nút close cùng lúc. Theo tôi bạn có thể 
tạo dialog riêng không dùng của Shadcn có thể sẽ có UI tốt hơn.
Vẫn chưa tìm kiếm được bằng không dấu, thậm chí có dấu cũng không tìm được nữa.
Text không bị tràn nữa nhưng lại bị che vì quá dài mà ô đại diện không đủ.

Instruction 28:
Dialog footer thì bo tròn nhưng header thì không bo. Đồng bộ lại là cùng bo.
Vẫn chưa tìm kiếm được không dấu, ví dụ tìm dong ho thì không ra gì cả.

Instruction 29:
Tìm kiếm đang hỗ trợ theo từng tab, nếu đang tìm kiếm thì bỏ tab đi và tìm ở trong cả 3 loại.

Instruction 30:
Dialog vẫn bị nhảy khi tìm kiếm do chiều cao thay đổi. Hãy cố định chiều cao. Có thể cho phép scroll khi có nhiều nội dung.

Instruction 31:
Hiện tại quá trình thêm node đang luôn là thêm vào root.
Logic không phải vậy mà nó phụ thuộc vào node hiện tại đang được chọn.
Node sẽ được thêm vào vị trí phụ thuộc vào node đang được chọn hiện tại.
1. Nếu là Frame (tính cả root) thì trở thành con cuối cùng
2. Nếu là Node không phải frame thì thêm vào ngay sau node đó.
3. Nếu không có node nào được chọn thì thêm vào root.


Instruction 31:
Vẫn sai logic thêm ngay sau node không phải là frame. Hiện vẫn đang là thêm vào cuối của cha.
Logic đúng là thêm ngay sau.

Instruction 32;
UX hiện tại đã khá ổn định. Hiện tại có một nút thêm frame vào root ở footer của Tree.
Hãy chuyển nó thành nút Thêm phần tử và xử lý logic như nút dấu + trên header.
Bạn có thể review code lại và clean những code rác, và đảm bảo code chuẩn lean 
và best practices sẵn sàng cho production và mở rộng chức năng trong tương lai.
Sau khi bạn review xong nếu không còn vấn đề gì tôi sẽ commit code để chuẩn bị bước 
sang giai đoạn tiếp theo.

Instruction 30:
Thêm nút thùng rác cạnh dấu + trên header để xoá node.
Button xoá node ở footer của panel sửa lại thành Xoá phần tử.
BỎ phần chú thích selected: 
Bỏ các button Thêm frame, thêm text ... trước đây
Trường hợp node hiện tại là root thì disable nút xoá (không cần ẩn đi)

Instruction 31:
Đồng bộ style cặp nút thêm xoá ở footer của TreePanel giống như header (chỉ hiện icon không có label)
Thêm cặp nút này ở cạnh title: Cấu trúc trang dạng ghost để người dùng dễ thao tác hơn nữa.

Instruction 32:
Sửa chữ Cấu trúc trang thành Cấu trúc để không phải xuống dòng khi có thêm node.
Bạn còn sửa gì code để đạt chuẩn best practice không?

Instruction 33:
Thêm Frame mới đang mặc định style p-4. Bỏ style này để frame nguyên bản chỉ có
min-h-24 khi không có con.
Image đang có w-full cũng bỏ luôn.
Video cũng thế.

Instruction 34:
Hiện tại tôi đã commit code lên git để đảm bảo chúng ta đã dừng ở một điểm với chức năng hoạt động ổn định.
Tuy nhiên hiện tại trong mã nguồn tôi thấy vẫn còn nhiều warning:
Ví dụ: 
React Hook useEffect has a missing dependency: 'state'. Either include it or remove the dependency array.eslintreact-hooks/exhaustive-deps

Hay sử dụng deprecated funtions:
'returnValue' is deprecated.ts(6385)
lib.dom.d.ts(3455, 8): The declaration was marked as deprecated here.
(property) BeforeUnloadEvent.returnValue: any

Hãy xem có phương án nào giải quyết không?


Instruction 35:
Khi thêm Node mới bỏ chữ New đi vì lặp đi lặp lại, chỉ cần để tên mặc định
là đủ và thêm dạng số như Figma ví dụ Frame 01, Frame 02 ... 
Cần control state để trong một docs mỗi lần thêm sẽ tạo nhãn mới.
Không đặt tên theo camel style mà có thể đặt theo kiểu Text hay 
dateTimeCard -> date-time-card

Instruction 36: 
Thuật toán của bạn đang là gì vậy? Tôi thấy bạn sửa code khá nhiều, liệu có gây ra lỗi gì tiềm ẩn không?
Bạn đang lưu biến đếm ở đâu?

Instruction 37:
Bạn làm như vậy là cứng nhắc rồi mà vẫn có thể lỗi.
Cách làm đơn giản hơn là bạn lưu một Recode<string, number> ở trong Ram.
Mỗi lần bạn load case từ storage lên bạn filter 1 lần duy nhất 
để tính ra gía trị count lớn nhất của tất cả các node trong Post hiện tại.
Làm như này bạn k cần sửa code cũ, kết quả luôn đúng, không lo khi người dùng thêm node type
mới mà không khai báo.
Hãy revert lại code cũ và sửa theo hướng tôi chỉ ra.

Instuction 38:
Chưa đúng bạn ạ, có lẽ việc extract số từ các định của label như Frame 01 .. của bạn đang gặp khó khăn. 
Nguyên nhân là cách đặt tên và node type đang chưa khớp nhau dẫn đến việc revert đang gặp khó.
Vậy thay vì đặt tên là Frame 01, hãy đơn giả là đặt frame-1 với quy tắc NodeType-counter thì bạn sẽ mapping dễ hơn nhiều.
Bạn hãy xử lý thuật toán tốt hơn.

Instruction 39

Vẫn lỗi bạn ạ, tôi thấy nó vẫn đếm lại từ frame-1 dù trong cay có frame-1 rồi

Instruction 40
Bạn dùng hàm này đã deprecated: substr
  const random = Math.random().toString(36).substr(2, 6);
'(from: number, length?: number | undefined): string' is deprecated.ts(6385)


Instruction 41:

export interface TreeNodeProperty {
  type: 'text' | 'number' | 'boolean' | 'url';
  value: string | number | boolean;
}

export interface TreeNode {
  id: string;
  label: string;
  type: TreeNodeType;
  group: TreeNodeGroup;
  style?: {
    className?: string;
    css?: React.CSSProperties;
  };
  properties?: Record<string, TreeNodeProperty>;
  children: TreeNode[];
}
Đoạn này bạn định nghĩa kiểu dữ liệu sai ý đồ của tôi rồi. 

Phải làm tổng quát hoá lên như sau:
export type TreeNodeProperty = boolean | boolean[] | number | number[] | string | string[] | null
properties?: Record<string, TreeNodeProperty>;

Lúc đó properties sẽ chứa được hầu hết các kiểu dữ liệu để truyền vào TreeNode. Lúc đọc 
ra thì string trong Record<string, TreeNodeProperty> chính là key, còn TreeNodeProperty
là giá trị ứng với key đó. 
Từ đó tổng quát hoá lên truyền đc hầu hết các loại props vào Node.

Còn làm như bạn thì việc lọc và đọc ra rất phức tạp, lúc cấu hình trong form 
cũng phức tạp, rất khó kiểm soát và không có tính tổng quát hoá.

Hãy xử lý theo cách làm của tôi, trừ khi bạn có ý kiến nào để nó tốt hơn.

Instruction 42:
Tất cả các node khởi tạo mặc định đều không nên có giá trị className và css.
Việc đó nên để người dùng điều chỉnh. Node nên hiển thị nguyên bản của chính nó.

Instruction 43:
Bạn đã xử lý đúng như chưa thật sự hiểu đúng ý của tôi.
Nguyên bản của node nghĩa là khi chúng ta tạo nên node thì trong node 
đã có sẵn style và chúng ta tôn trọng style đó. Chứ không có nghĩa 
là trong node hoàn toàn không có style.

Trường hợp basic node không mang theo style nào là hoàn toàn đúng. 
Nhưng trường hợp template và widget, thì bản thân node đã có style của chính nó rồi.
Editor chỉ có thể can thiệp vào style của thẻ ngoài cùng của node đó thôi chứ 
không thể style sâu bên trong nội dung node. Đương nhiên cái style đó lúc đầu không tồn 
tại và chỉ sinh ra khi người dùng thêm vào để điều chỉnh theo ý họ.

Bạn đã hiểu ý tôi chưa? Nên việc bản bỏ hết style trong widget và template là không đúng.
Tất nhiên template và widget này chỉ là mẫu chúng ta tạo ra để minh hoạ vai trò của nó 
còn template và widget thật thì sẽ được bổ sung trong tương lai để việc builder được 
dễ dàng hơn chứ không phải vất vả build lên từ basic.

Instruction 43:
Chúng ta đã đi được một chặng đường xa nhưng vẫn còn nhiều bài toán cần xử lý. 
Tôi đã commit code ở trạng thái ổn định hiện tại để đảm bảo nếu có sai lầm chúng ta
có thể quay lại và bắt đầu lại từ đây.
Sau đây chúng ta sẽ tập trung vào xây dựng chức năng di chuyển các node trên canvas.
UX sẽ được đơn giản hoá như sau:
- Người dùng ở mode di chuyển click vào một node để di chuyển node đó. 
Nó đó được làm nổi bật (điều này đã có)
- Người dùng bắt đầu di chuột trên canvas, từ vị trí của chuột hệ thống sẽ tính toán 
ra node đang được chọn nên di chuyển về vị trí nào và cập nhật luôn node được chọn về
vị trí đó. Với UX này chúng ta sẽ hoàn toàn bỏ qua overlay mà chỉ quan tâm đến vị trí chuột.
Khi người dùng click chuột lần tiếp theo thì việc di chuyển sẽ kết thúc node đó sẽ được dừng lại ở vị trí 
cuối cùng. Lúc đó không còn node nào được chọn nữa.
- Bạn đã hiểu hết UX chưa? Nếu chưa hãy hỏi lại tôi để xác nhận lại những nội dung bạn cần rõ để bắt đầu.

Instruction 44:
1. Khi di chuyển chúng ta cần căn cứ vào vị trí chuột để tính ra vị trí mới của node. Đương
nhiên chỉ những vị trí hợp lệ chúng ta mới cập nhật vị trí node di chuyển về đó. Còn không 
hợp lệ thì node vẫn ở nguyên vị trí ban đầu. Giả sử ban đầu node ở A, chuột di đến vị trí hợp lệ B, nhưng
sau đó lại tiếp tục di đến vị trí C không hợp lệ, thì node di chuyển lúc chuột ở C thì nó về lại vị trí 
xuất phát là A. 
2. Chúng ta đã di chuyển node thật sự nên chính nó đã là indicator rồi, không cần thêm nữa.
3. Nếu ấn esc hay click ngoài canvas hay bất kì vị trí nào không hợp lệ sẽ kết thúc di chuyển và đương
nhiên bỏ chọn luôn node đó. Người dùng muốn di chuyển tiếp sẽ phải chọn lại.


Instruction 45:
Tôi đã sai về việc cho node di chuyển luôn mà không dùng indicator. Thực tế không thể làm việc đó được vì 
khi node di chuyển sẽ làm đảo lộn vị trí chuột và làm sai lệch tính toán.
Hãy trở về với các chúng ta đã làm với Tree. Sau khi chọn node xong chúng ta di chuyển chuột, hệ thống 
sẽ tính toàn vào tạo ra indicator giống như với Tree đó là viền xanh dương khi chèn trước hoặc sau một node, 
và khung tím khi đưa node được chọn vào trong node đó.
Việc di chuyển hoàn thành khi người dùng click chuột thêm một lần nữa. Nếu có vị trí hợp lệ thì node sẽ chuyển 
đến đó, nếu không thì bỏ chọn để người dùng thao tác lại tự đầu.

Instruction 46:
Node được chọn chỉ cần đánh dấu bằng khụng da cam là đủ, đừng có là mở hay hiệu ứng như hiện tại rất đau mắt.
Hiện tại thấy node đã chọn rồi, click vào thì mới thật sự chọn. Một thời điểm chi có 1 node được chọn 
để di chuyển nên có thể tái sử dụng state của node đang chọn bên sửa mà không cần định nghĩa thêm. Tuy nhiên 
khi chuyển mode thì sẽ chưa có node nào được chọn.
Hiện tại khi di chuyển chuột thì có lúc nó hiện indicator, có lúc không, lúc hover qua frame cũng không thấy frame
hiện khung tím để drop node vào.