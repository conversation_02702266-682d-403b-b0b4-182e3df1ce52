import { useState, useRef, useCallback, useEffect } from "react";

interface UseInlineTextEditorOptions {
  initialContent: string;
  onSave: (content: string) => void;
  onCancel?: () => void;
  multiline?: boolean;
}

interface UseInlineTextEditorReturn {
  isEditing: boolean;
  elementRef: React.RefObject<HTMLDivElement | null>;
  startEditing: () => void;
  stopEditing: () => void;
  cancelEditing: () => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  handleBlur: () => void;
}

export const useInlineTextEditor = ({
  initialContent,
  onSave,
  onCancel,
  multiline = false,
}: UseInlineTextEditorOptions): UseInlineTextEditorReturn => {
  const [isEditing, setIsEditing] = useState(false);
  const [originalContent, setOriginalContent] = useState(initialContent);
  const elementRef = useRef<HTMLDivElement>(null);

  const startEditing = useCallback(() => {
    if (isEditing) return;

    setOriginalContent(initialContent);
    setIsEditing(true);

    // Focus and select all content after element becomes editable
    setTimeout(() => {
      const element = elementRef.current;
      if (element) {
        element.focus();

        // Select all text content
        const selection = window.getSelection();
        if (selection) {
          const range = document.createRange();
          range.selectNodeContents(element);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      }
    }, 0);
  }, [isEditing, initialContent]);

  const stopEditing = useCallback(() => {
    if (!isEditing) return;

    const element = elementRef.current;
    if (element) {
      const content = element.textContent || "";
      onSave(content);
    }

    setIsEditing(false);
  }, [isEditing, onSave]);

  const cancelEditing = useCallback(() => {
    if (!isEditing) return;

    const element = elementRef.current;
    if (element) {
      // Restore original content
      element.textContent = originalContent;
    }

    setIsEditing(false);
    onCancel?.();
  }, [isEditing, originalContent, onCancel]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (!isEditing) return;

      if (e.key === "Enter") {
        if (multiline && !e.shiftKey) {
          // Allow Enter for multiline, but Shift+Enter to save
          return;
        } else if (!multiline) {
          // Single line: Enter saves
          e.preventDefault();
          stopEditing();
        } else if (e.shiftKey) {
          // Multiline: Shift+Enter saves
          e.preventDefault();
          stopEditing();
        }
      }

      if (e.key === "Escape") {
        e.preventDefault();
        cancelEditing();
      }

      // Prevent some formatting shortcuts that might break layout
      if (e.key === "Tab") {
        e.preventDefault();
        stopEditing();
      }
    },
    [isEditing, multiline, stopEditing, cancelEditing]
  );

  const handleBlur = useCallback(() => {
    if (!isEditing) return;

    // Small delay to check if focus moved to a related element
    // This prevents saving when clicking on toolbar buttons (future feature)
    setTimeout(() => {
      const activeElement = document.activeElement;
      const element = elementRef.current;

      // If focus didn't move to a child or related element, save
      if (element && !element.contains(activeElement)) {
        stopEditing();
      }
    }, 100);
  }, [isEditing, stopEditing]);

  // Update content when initialContent changes externally
  useEffect(() => {
    if (!isEditing && elementRef.current) {
      elementRef.current.textContent = initialContent;
    }
  }, [initialContent, isEditing]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isEditing) {
        stopEditing();
      }
    };
  }, [isEditing, stopEditing]);

  return {
    isEditing,
    elementRef,
    startEditing,
    stopEditing,
    cancelEditing,
    handleKeyDown,
    handleBlur,
  };
};
