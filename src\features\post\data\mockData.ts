import { Post, PostType, PostStatus } from "../states/types";

// Mock posts data
export const mockPosts: Post[] = [
  {
    id: 1,
    createdAt: Date.now() - 7 * 24 * 60 * 60 * 1000, // 7 days ago
    updatedAt: Date.now() - 2 * 24 * 60 * 60 * 1000, // 2 days ago
    publishedAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
    title: "Hướng dẫn sử dụng hệ thống",
    slug: "huong-dan-su-dung-he-thong",
    content: {
      type: "root",
      children: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Nội dung hướng dẫn...",
              children: [],
            },
          ],
        },
      ],
    },
    excerpt: {
      image: "/images/guide-thumbnail.jpg",
      description: "Hướng dẫn chi tiết cách sử dụng các tính năng của hệ thống",
      files: [],
    },
    postType: "ARTICLE" as PostType,
    status: "PUBLISHED" as PostStatus,
    authorId: "user-123",
    parentId: null,
  },
  {
    id: 2,
    createdAt: Date.now() - 14 * 24 * 60 * 60 * 1000, // 14 days ago
    updatedAt: Date.now() - 5 * 24 * 60 * 60 * 1000, // 5 days ago
    publishedAt: Date.now() - 5 * 24 * 60 * 60 * 1000,
    title: "Chính sách bảo mật",
    slug: "chinh-sach-bao-mat",
    content: {
      type: "root",
      children: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Chi tiết chính sách bảo mật...",
              children: [],
            },
          ],
        },
      ],
    },
    excerpt: {
      image: "src/assets/images/privacy-thumbnail.jpg",
      description: "Thông tin về chính sách bảo mật của hệ thống",
      files: [],
    },
    postType: "PAGE" as PostType,
    status: "PUBLISHED" as PostStatus,
    authorId: "admin-1",
    parentId: null,
  },
  {
    id: 3,
    createdAt: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3 days ago
    updatedAt: Date.now() - 1 * 24 * 60 * 60 * 1000, // 1 day ago
    publishedAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
    title: "Cập nhật tính năng mới",
    slug: "cap-nhat-tinh-nang-moi",
    content: {
      type: "root",
      children: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Danh sách tính năng mới...",
              children: [],
            },
          ],
        },
      ],
    },
    excerpt: {
      image: "src/assets/images/update-thumbnail.jpg",
      description: "Thông tin về các tính năng mới được cập nhật",
      files: [],
    },
    postType: "ARTICLE" as PostType,
    status: "DRAFT" as PostStatus,
    authorId: "editor-1",
    parentId: null,
  },
  {
    id: 4,
    createdAt: Date.now() - 10 * 24 * 60 * 60 * 1000, // 10 days ago
    updatedAt: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3 days ago
    publishedAt: Date.now() - 3 * 24 * 60 * 60 * 1000,
    title: "Điều khoản sử dụng",
    slug: "dieu-khoan-su-dung",
    content: {
      type: "root",
      children: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Chi tiết điều khoản sử dụng...",
              children: [],
            },
          ],
        },
      ],
    },
    excerpt: {
      image: "src/assets/images/terms-thumbnail.jpg",
      description: "Điều khoản và điều kiện sử dụng hệ thống",
      files: [],
    },
    postType: "PAGE" as PostType,
    status: "PUBLISHED" as PostStatus,
    authorId: "admin-1",
    parentId: null,
  },
  {
    id: 5,
    createdAt: Date.now() - 1 * 24 * 60 * 60 * 1000, // 1 day ago
    updatedAt: Date.now() - 1 * 24 * 60 * 60 * 1000,
    publishedAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
    title: "Thông báo bảo trì hệ thống",
    slug: "thong-bao-bao-tri-he-thong",
    content: {
      type: "root",
      children: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Lịch bảo trì hệ thống...",
              children: [],
            },
          ],
        },
      ],
    },
    excerpt: {
      image: "/images/maintenance-thumbnail.jpg",
      description: "Thông báo về lịch bảo trì và nâng cấp hệ thống",
      files: [],
    },
    postType: "ARTICLE" as PostType,
    status: "REVIEW" as PostStatus,
    authorId: "editor-2",
    parentId: null,
  },
];
