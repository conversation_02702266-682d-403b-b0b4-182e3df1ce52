import React, { useCallback } from "react";
import { NodeRendererProps } from "../NodeFactory";
import { ImageLoader } from "@/components/image/ImageLoader";
import { cn } from "@/lib/utils";
import { compareNodeWithProperties } from '@/lib/memo-utils';
import { useNodeOperations } from "../../../context/hooks";
import { useInlineTextEditor } from "../../../hooks/useInlineTextEditor";
import { Button } from "@/components/ui/button";

const LibraryDisplayNodeComponent: React.FC<NodeRendererProps & React.HTMLAttributes<HTMLElement>> = ({
  node,
  isPreview = false,
  ...htmlProps
}) => {
  const { updateNode } = useNodeOperations();

  // Get properties from node
  const title = (node.properties?.title as string) || "Thư viện nổi bật";
  const description = (node.properties?.description as string) || "<PERSON><PERSON> nh<PERSON><PERSON> thư viện nổi bật";
  const buttonUrl = (node.properties?.buttonUrl as string) || "#";
  const imagePosition = (node.properties?.imagePosition as string) || "right";
  const image1Url = (node.properties?.image1Url as string) || "";
  const image2Url = (node.properties?.image2Url as string) || "";
  const image3Url = (node.properties?.image3Url as string) || "";

  // Inline text editor for title
  const handleSaveTitle = useCallback((newTitle: string) => {
    updateNode(node.id, {
      properties: {
        ...node.properties,
        title: newTitle,
      },
    });
  }, [updateNode, node.id, node.properties]);

  // Inline text editor for description
  const handleSaveDescription = useCallback((newDescription: string) => {
    updateNode(node.id, {
      properties: {
        ...node.properties,
        description: newDescription,
      },
    });
  }, [updateNode, node.id, node.properties]);

  const {
    isEditing: isEditingTitle,
    elementRef: titleRef,
    startEditing: startEditingTitle,
    handleKeyDown: handleTitleKeyDown,
    handleBlur: handleTitleBlur
  } = useInlineTextEditor({
    initialContent: title,
    onSave: handleSaveTitle,
    multiline: false
  });

  const {
    isEditing: isEditingDescription,
    elementRef: descriptionRef,
    startEditing: startEditingDescription,
    handleKeyDown: handleDescriptionKeyDown,
    handleBlur: handleDescriptionBlur
  } = useInlineTextEditor({
    initialContent: description,
    onSave: handleSaveDescription,
    multiline: true
  });

  // Handle button click
  const handleButtonClick = () => {
    if (buttonUrl && buttonUrl !== "#") {
      window.open(buttonUrl, "_blank");
    }
  };

  // Library panel item component
  const LibraryPanelItem: React.FC<{
    imageUrl: string;
    direction: "up" | "down";
    index: number;
  }> = React.memo(({ imageUrl, direction, index }) => {
    const [imageError, setImageError] = React.useState(false);
    const [imageLoading, setImageLoading] = React.useState(!!imageUrl);

    const handleImageLoad = () => {
      setImageLoading(false);
      setImageError(false);
    };

    const handleImageError = () => {
      setImageLoading(false);
      setImageError(true);
    };

    return (
      <div
        className={cn(
          "flex h-min",
          direction === "up" ? "flex-col-reverse" : "flex-col"
        )}
      >
        <div className="flex aspect-square rounded-lg overflow-hidden bg-gray-100 h-2/3 relative">
          {imageLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          )}
          <ImageLoader
            src={imageUrl}
            alt={`Library item ${index + 1}`}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 aspect-square"
            fallbackText={
              imageError
                ? `Failed to load image ${index + 1}`
                : imageUrl
                  ? `Image ${index + 1}`
                  : "Click to add image"
            }
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </div>
        <div className="flex flex-1 aspect-square h-1/3 w-1/3"></div>
      </div>
    );
  });

  // Determine flex direction based on image position
  const flexDirection = imagePosition === "left" ? "flex-row-reverse" : "flex-row";

  return (
    <div
      className={cn(
        "flex gap-8 bg-transparent border-gray-200 p-4 items-center min-h-64",
        node.style?.className,
        flexDirection
      )}
      style={node.style?.css}
      {...htmlProps}
    >
      {/* Content Section */}
      <div className="flex flex-col space-y-4 w-1/3 h-full">
        <div className="space-y-2">
          {/* Editable Title */}
          <div
            ref={titleRef}
            className={cn(
              "text-[28px] font-semibold text-gray-900 cursor-pointer outline-none min-h-4 whitespace-pre-wrap",
              isEditingTitle && "ring-2 ring-blue-500 ring-opacity-50"
            )}
            contentEditable={isEditingTitle}
            suppressContentEditableWarning={true}
            onDoubleClick={isPreview ? undefined : startEditingTitle}
            onKeyDown={handleTitleKeyDown}
            onBlur={handleTitleBlur}
            role="heading"
            aria-level={2}
            aria-label={isEditingTitle ? "Editing title" : "Double-click to edit title"}
            tabIndex={isEditingTitle ? 0 : -1}
          >
            {title || "Click to add title"}
          </div>

          {/* Editable Description */}
          <div
            ref={descriptionRef}
            className={cn(
              "text-[15px] cursor-pointer outline-none min-h-4 whitespace-pre-wrap",
              isEditingDescription && "ring-2 ring-blue-500 ring-opacity-50",
              node.style?.className
            )}
            contentEditable={isEditingDescription}
            suppressContentEditableWarning={true}
            onDoubleClick={isPreview ? undefined : startEditingDescription}
            onKeyDown={handleDescriptionKeyDown}
            onBlur={handleDescriptionBlur}
            role="textbox"
            aria-label={isEditingDescription ? "Editing description" : "Double-click to edit description"}
            aria-multiline="true"
            tabIndex={isEditingDescription ? 0 : -1}
            style={{
              ...node.style?.css
            }}
          >
            {description || "Click to add description"}
          </div>
        </div>

        {/* Button */}
        <div>
          <Button
            variant="outline"
            className="rounded-full border-2 border-primary text-primary bg-primary-foreground hover:bg-primary/10"
            onClick={handleButtonClick}
            aria-label={`View more library content${buttonUrl && buttonUrl !== '#' ? ` - opens in new tab` : ''}`}
            disabled={!buttonUrl || buttonUrl === '#'}
          >
            Xem thêm
          </Button>
        </div>
      </div>

      {/* Images Section */}
      <div className="w-2/3">
        <div className="grid grid-cols-3 gap-10">
          {image1Url && (
            <LibraryPanelItem imageUrl={image1Url} direction="up" index={0} />
          )}
          {image2Url && (
            <LibraryPanelItem imageUrl={image2Url} direction="down" index={1} />
          )}
          {image3Url && (
            <LibraryPanelItem imageUrl={image3Url} direction="up" index={2} />
          )}

          {/* Show placeholders if no images */}
          {!image1Url && (
            <LibraryPanelItem imageUrl="" direction="up" index={0} />
          )}
          {!image2Url && (
            <LibraryPanelItem imageUrl="" direction="down" index={1} />
          )}
          {!image3Url && (
            <LibraryPanelItem imageUrl="" direction="up" index={2} />
          )}
        </div>
      </div>
    </div>
  );
};

// Memoized template node to prevent re-renders
export const LibraryDisplayNode = React.memo(
  LibraryDisplayNodeComponent,
  compareNodeWithProperties
);
