import { createContext } from 'react';
import { BuilderState } from './types';
import { BuilderAction } from './actions';

// Simplified context interface - only core state and dispatch
export interface BuilderContextValue extends BuilderState {
  // Raw dispatch for advanced use cases
  dispatch: (action: BuilderAction) => void;
  
  // History operations (kept in main context for simplicity)
  undo: () => void;
  redo: () => void;
  canUndo: () => boolean;
  canRedo: () => boolean;
  
  // UI state
  isUIReady: boolean;
}

// Create context
export const BuilderContext = createContext<BuilderContextValue | null>(null);