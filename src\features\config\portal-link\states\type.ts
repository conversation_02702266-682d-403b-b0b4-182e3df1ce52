export interface PortalLinkItem {
  title: string;
  url: string;
  image: string;
}

export interface PortalLinkConfig {
  portalLinks: PortalLinkItem[];
}

export interface PortalLinkState {
  data: PortalLinkConfig | null;
  savedData: PortalLinkConfig | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
  isDirty: boolean;
}

/**
 * Configuration field definition for form generation
 */
export interface PortalLinkField {
  /** Field identifier */
  key: string;
  /** Display label */
  label: string;
  /** Input type */
  type: "text" | "email" | "url" | "textarea" | "boolean" | "array";
  /** Whether field is required */
  required?: boolean;
  /** Placeholder text */
  placeholder?: string;
}

/**
 * Configuration metadata for form generation
 */
export interface PortalLinkMetadata {
  /** Section title */
  title: string;
  /** Section description */
  description: string;
  /** Configuration type */
  type: "array" | "object";
  /** Array key for array-type configurations */
  arrayKey?: string;
  /** Field definitions */
  fields: PortalLinkField[];
}

/**
 * Edit state for tracking changes in portal link items
 */
export interface EditablePortalLinkItem {
  /** Unique identifier */
  id: string;
  /** Whether this is a new item */
  isNew?: boolean;
  /** Item data with string values */
  data: Record<string, string>;
}

// Props for portal link table component
export interface PortalLinkTableProps {
  metadata: PortalLinkMetadata;
  data: PortalLinkConfig | null;
  loading: boolean;
  onDataChange: (newData: PortalLinkConfig) => void;
  onValidationChange?: (isValid: boolean) => void;
}

// Props for portal link page layout component
export interface PortalLinkPageLayoutProps {
  title: string;
  description: string;
  children: React.ReactNode;
  loading?: boolean;
  error?: string | null;
  onSave?: () => void;
  onRefresh?: () => void;
  onPreview?: () => void;
  showPreview?: boolean;
  saveDisabled?: boolean;
  isDirty?: boolean;
  hasValidationErrors?: boolean;
}
