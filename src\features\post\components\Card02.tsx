import { But<PERSON> } from "@/components/ui/button";
import SectionTitle from "@/features/post/components/SectionTitle";
import { ChevronLeft, ChevronRight } from "lucide-react";

export const Card02: React.FC = () => {
  return (
    <div className="p-4 flex items-center gap-4">
      <SectionTitle title="TIN TỨC MỚI CẬP NHẬT" />
      <div className="flex-grow border-t border-gray-300" />
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <button className="border border-primary rounded-full p-2 text-primary hover:bg-primary/10">
            <ChevronLeft className="w-4 h-4" />
          </button>
          <button className="border border-primary rounded-full p-2 text-primary hover:bg-primary/10">
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
        <div className="h-6 border-l border-gray-300" />
        <Button
          variant="outline"
          className="rounded-full border-2 border-primary text-primary bg-primary-foreground hover:bg-primary/10"
        >
          Xem thêm
        </Button>
      </div>
    </div>
  );
};
