# KHA Builder Package

Một React component builder có thể kéo thả để tạo giao diện web động.

## 🚀 Cài đặt

```bash
npm install kha-builder-package
# hoặc
yarn add kha-builder-package
```

## 📦 Sử dụng

### Import cơ bản

```typescript
import {
  BuilderProvider,
  useBuilder,
  BuilderLayout,
  WebBuilder,
} from "kha-builder-package";
```

### Sử dụng trong ứng dụng

```tsx
import React from "react";
import { BuilderProvider, WebBuilder } from "kha-builder-package";

function App() {
  return (
    <BuilderProvider>
      <WebBuilder />
    </BuilderProvider>
  );
}

export default App;
```

### Sử dụng với Layout tùy chỉnh

```tsx
import React from "react";
import {
  BuilderProvider,
  BuilderLayout,
  useBuilder,
} from "kha-builder-package";

function CustomBuilderApp() {
  return (
    <BuilderProvider>
      <BuilderLayout>
        {/* Nội dung tùy chỉnh */}
        <YourCustomContent />
      </BuilderLayout>
    </BuilderProvider>
  );
}
```

## 🔧 API Reference

### Components

- `BuilderProvider` - Context provider chính
- `BuilderLayout` - Layout wrapper cho builder
- `WebBuilder` - Component builder hoàn chỉnh

### Hooks

- `useBuilder` - Hook để truy cập builder context

### Types

- Tất cả types cần thiết được export từ package

## 🛡️ Bảo vệ mã nguồn

Package này được build và minify, mã nguồn gốc được bảo vệ.

## 📄 License

MIT
