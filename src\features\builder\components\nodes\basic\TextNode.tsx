import React, { forwardRef, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { NodeRendererProps } from '../NodeFactory';
import { compareNodeWithProperties } from '@/lib/memo-utils';
import { useInlineTextEditor } from '../../../hooks/useInlineTextEditor';
import { useNodeOperations } from '../../../context/hooks';

const TextNodeComponent = forwardRef<HTMLDivElement, NodeRendererProps & React.HTMLAttributes<HTMLDivElement>>(({ 
  node,
  ...htmlProps
}, ref) => {
  const { updateNode } = useNodeOperations();
  const content = (node.properties?.content as string) || '';
  
  const handleSave = useCallback((newContent: string) => {
    updateNode(node.id, {
      properties: {
        ...node.properties,
        content: newContent
      }
    });
  }, [updateNode, node.id, node.properties]);

  const {
    isEditing,
    elementRef,
    startEditing,
    handleKeyDown,
    handleBlur
  } = useInlineTextEditor({
    initialContent: content,
    onSave: handleSave,
    multiline: false
  });
  
  return (
    <div 
      ref={ref}
      className={cn(
        "inline-block", // Fit content exactly
        node.style?.className
      )}
      style={node.style?.css}
      {...htmlProps}
    >
      <div
        ref={elementRef}
        className={cn(
          "m-0 outline-none",
          isEditing && "ring-2 ring-blue-500 ring-opacity-50"
        )}
        contentEditable={isEditing}
        suppressContentEditableWarning={true}
        onDoubleClick={startEditing}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        style={{
          minHeight: '1em',
          whiteSpace: 'pre-wrap'
        }}
      >
        {content || '\u00A0'}
      </div>
    </div>
  );
});

TextNodeComponent.displayName = 'TextNodeComponent';

// Re-enable memoization for TextNode - safe for form editing
export const TextNode = React.memo(TextNodeComponent, compareNodeWithProperties);

