import React from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { TreeNode } from "../../../context/types";
import { CSSPropertiesEditor } from "../editors/CSSPropertiesEditor";

interface AdvancedDisplayFormProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled: boolean;
}

/**
 * AdvancedDisplayForm provides direct access to Tailwind classes and CSS properties
 * Used in the "Nâng cao" (Advanced) mode of the Display tab
 * Shared across all node types
 */
export const AdvancedDisplayForm: React.FC<AdvancedDisplayFormProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  const handleClassNameChange = (value: string) => {
    onChange({
      style: {
        ...node.style,
        className: value,
      },
    });
  };

  return (
    <div className="space-y-5">
      {disabled && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
          <p className="text-sm text-yellow-800">
            <PERSON><PERSON> ở chế độ xem. <PERSON><PERSON><PERSON><PERSON> sang chế độ S<PERSON>a để thay đổi.
          </p>
        </div>
      )}

      <div className="space-y-3">
        <Label htmlFor="tailwind-classes" className="text-sm font-semibold">
          Tailwind Classes
        </Label>
        <Textarea
          id="tailwind-classes"
          value={currentClassName}
          onChange={(e) => handleClassNameChange(e.target.value)}
          disabled={disabled}
          placeholder="bg-blue-500 text-white p-4 rounded-lg shadow-md..."
          className="min-h-[100px] resize-none text-sm font-mono"
        />
        <p className="text-xs text-gray-500">
          Nhập trực tiếp Tailwind classes. Mỗi class cách nhau bằng dấu cách.
        </p>
      </div>

      {/* CSS Properties Editor */}
      <CSSPropertiesEditor
        node={node}
        onChange={onChange}
        disabled={disabled}
      />
    </div>
  );
};
