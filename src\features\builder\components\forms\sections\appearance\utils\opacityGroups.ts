export interface OpacityOption {
  value: string;
  label: string;
  description?: string;
}

export interface OpacityGroup {
  id: string;
  label: string;
  options: OpacityOption[];
}

export const OPACITY_GROUPS: OpacityGroup[] = [
  {
    id: 'all',
    label: 'Opacity',
    options: [
      { value: 'opacity-100', label: '100%', description: 'Opaque' },
      { value: 'opacity-95', label: '95%' },
      { value: 'opacity-90', label: '90%' },
      { value: 'opacity-85', label: '85%' },
      { value: 'opacity-80', label: '80%' },
      { value: 'opacity-75', label: '75%' },
      { value: 'opacity-70', label: '70%' },
      { value: 'opacity-65', label: '65%' },
      { value: 'opacity-60', label: '60%' },
      { value: 'opacity-55', label: '55%' },
      { value: 'opacity-50', label: '50%', description: 'Half' },
      { value: 'opacity-45', label: '45%' },
      { value: 'opacity-40', label: '40%' },
      { value: 'opacity-35', label: '35%' },
      { value: 'opacity-30', label: '30%' },
      { value: 'opacity-25', label: '25%' },
      { value: 'opacity-20', label: '20%' },
      { value: 'opacity-15', label: '15%' },
      { value: 'opacity-10', label: '10%' },
      { value: 'opacity-5', label: '5%' },
      { value: 'opacity-0', label: '0%', description: 'Invisible' },
    ]
  }
];