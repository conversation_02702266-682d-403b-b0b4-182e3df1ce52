import { combineReducers } from "redux";
import { TypedUseSelectorHook, useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "./store";
import contactReducer from "@/features/config/contact/states/slices";
import hotlineReducer from "@/features/config/hotline/states/slices";
import portalLinkReducer from "@/features/config/portal-link/states/slices";

const rootReducer = combineReducers({
  contactState: contactReducer,
  hotlineState: hotlineReducer,
  portalLink: portalLinkReducer,
});

export type RootState = ReturnType<typeof rootReducer>;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export default rootReducer;
