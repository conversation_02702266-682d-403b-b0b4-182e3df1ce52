// Gap value groups organized by Tailwind CSS spacing scale

export interface GapOption {
  value: string;
  label: string;
  description?: string;
}

export interface GapGroup {
  id: string;
  label: string;
  options: GapOption[];
}


// Fixed gap values - using Tailwind spacing scale
const GAP_FIXED_VALUES = [
  { value: 'gap-0', label: '0', description: '0px' },
  { value: 'gap-px', label: 'px', description: '1px' },
  { value: 'gap-0.5', label: '0.5', description: '2px' },
  { value: 'gap-1', label: '1', description: '4px' },
  { value: 'gap-1.5', label: '1.5', description: '6px' },
  { value: 'gap-2', label: '2', description: '8px' },
  { value: 'gap-2.5', label: '2.5', description: '10px' },
  { value: 'gap-3', label: '3', description: '12px' },
  { value: 'gap-3.5', label: '3.5', description: '14px' },
  { value: 'gap-4', label: '4', description: '16px' },
  { value: 'gap-5', label: '5', description: '20px' },
  { value: 'gap-6', label: '6', description: '24px' },
  { value: 'gap-7', label: '7', description: '28px' },
  { value: 'gap-8', label: '8', description: '32px' },
  { value: 'gap-9', label: '9', description: '36px' },
  { value: 'gap-10', label: '10', description: '40px' },
  { value: 'gap-11', label: '11', description: '44px' },
  { value: 'gap-12', label: '12', description: '48px' },
  { value: 'gap-14', label: '14', description: '56px' },
  { value: 'gap-16', label: '16', description: '64px' },
  { value: 'gap-20', label: '20', description: '80px' },
  { value: 'gap-24', label: '24', description: '96px' },
  { value: 'gap-28', label: '28', description: '112px' },
  { value: 'gap-32', label: '32', description: '128px' },
  { value: 'gap-36', label: '36', description: '144px' },
  { value: 'gap-40', label: '40', description: '160px' },
  { value: 'gap-44', label: '44', description: '176px' },
  { value: 'gap-48', label: '48', description: '192px' },
  { value: 'gap-52', label: '52', description: '208px' },
  { value: 'gap-56', label: '56', description: '224px' },
  { value: 'gap-60', label: '60', description: '240px' },
  { value: 'gap-64', label: '64', description: '256px' },
  { value: 'gap-72', label: '72', description: '288px' },
  { value: 'gap-80', label: '80', description: '320px' },
  { value: 'gap-96', label: '96', description: '384px' },
];

// Gap-X (horizontal) values
const GAP_X_FIXED_VALUES = GAP_FIXED_VALUES.map(opt => ({
  value: opt.value.replace('gap-', 'gap-x-'),
  label: opt.label,
  description: opt.description
}));

// Gap-Y (vertical) values
const GAP_Y_FIXED_VALUES = GAP_FIXED_VALUES.map(opt => ({
  value: opt.value.replace('gap-', 'gap-y-'),
  label: opt.label,
  description: opt.description
}));

// Gap groups for unified gap - include "none" option in fixed group
const GAP_ALL_FIXED_VALUES = [
  { value: 'none', label: 'Không chọn', description: 'Không có khoảng cách' },
  ...GAP_FIXED_VALUES
];

const GAP_X_ALL_FIXED_VALUES = [
  { value: 'none', label: 'Không chọn', description: 'Không có khoảng cách' },
  ...GAP_X_FIXED_VALUES
];

const GAP_Y_ALL_FIXED_VALUES = [
  { value: 'none', label: 'Không chọn', description: 'Không có khoảng cách' },
  ...GAP_Y_FIXED_VALUES
];

// Gap groups for unified gap - single group only
export const GAP_GROUPS: GapGroup[] = [
  {
    id: 'fixed',
    label: 'Khoảng cách',
    options: GAP_ALL_FIXED_VALUES
  }
];

// Gap-X groups for horizontal spacing - single group only
export const GAP_X_GROUPS: GapGroup[] = [
  {
    id: 'fixed',
    label: 'Khoảng cách',
    options: GAP_X_ALL_FIXED_VALUES
  }
];

// Gap-Y groups for vertical spacing - single group only
export const GAP_Y_GROUPS: GapGroup[] = [
  {
    id: 'fixed',
    label: 'Khoảng cách',
    options: GAP_Y_ALL_FIXED_VALUES
  }
];