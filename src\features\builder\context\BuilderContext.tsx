import React, { useReducer, useCallback, useEffect, useRef, useState, useMemo, useContext } from 'react';
import { BuilderState, Post, RecoveryOptions, StorageError, BuilderUIState } from './types';
import { BuilderContext, BuilderContextValue } from './context';
import { builderReducer } from './reducer';
import { actions, shouldRecordInHistory, BuilderAction } from './actions';
import { createSampleContent } from '../utils/node-factory';
import { HistoryManager } from './history';
import { BuilderStorage } from './storage';
import { UI_DEFAULTS } from '../config/constants';
import { initializeCountersFromPost } from '../utils/node-naming';
import { DataProvider, useData } from './DataContext';
import { UIProvider, useUI } from './UIContext';
import { ActionsProvider, useActions } from './ActionsContext';
import { TreeNode } from './types';

// Initial state with option to provide UI state
const createInitialState = (uiState?: Partial<BuilderUIState>): BuilderState => ({
  post: null,
  uiState: {
    selectedNodeId: 'root',
    expandedNodeIds: new Set<string>(['root']),
    viewMode: UI_DEFAULTS.VIEW_MODE,
    editorMode: UI_DEFAULTS.EDITOR_MODE,
    isDirty: false,
    lastSaved: Date.now(),
    showTreePanel: true,
    showPropertiesPanel: true,
    copiedNode: null,
    lastCreatedNodeId: null,
    ...uiState
  }
});

// Provider props
export interface BuilderProviderProps {
  children: React.ReactNode;
  initialPost?: Post;
  onSave?: (post: Post) => void | Promise<void>;
  onStorageError?: (error: StorageError, options: RecoveryOptions) => void;
}


export const BuilderProvider: React.FC<BuilderProviderProps> = ({ 
  children, 
  initialPost,
  onSave,
  onStorageError
}) => {
  // Load UI state synchronously before initializing state (lazy initialization)
  const [state, baseDispatch] = useReducer(builderReducer, null, () => {
    if (initialPost) {
      return createInitialState();
    }
    
    // Try to load UI state from storage synchronously
    const { state: loadedState } = BuilderStorage.loadCompleteState();
    return createInitialState(loadedState?.uiState);
  });
  const [isInitialized, setIsInitialized] = useState(false);
  const [isUIReady, setIsUIReady] = useState(false);
  const historyManager = useRef<HistoryManager | null>(null);
  const autoSaveTimer = useRef<number | null>(null);
  const lastAction = useRef<BuilderAction | null>(null);
  
  // Enhanced dispatch with history tracking
  const dispatch = useCallback((action: BuilderAction) => {
    // Dispatch to reducer first to get new state
    baseDispatch(action);
    
    // Store action for history after state update
    lastAction.current = action;
  }, []);

  // Simple dispatch without history (for undo/redo)
  const dispatchWithoutHistory = useCallback((action: BuilderAction) => {
    baseDispatch(action);
  }, []);
  
  // Initialize history manager
  useEffect(() => {
    if (!historyManager.current && state.post) {
      historyManager.current = new HistoryManager(state);
    }
  }, [state]);
  
  // Track state changes for history
  useEffect(() => {
    if (historyManager.current && lastAction.current && shouldRecordInHistory(lastAction.current) && state.post) {
      historyManager.current.push(lastAction.current, state);
      lastAction.current = null;
    }
  }, [state]);
  
  // Initialize on mount
  useEffect(() => {
    if (!isInitialized) {
      if (initialPost) {
        dispatch(actions.setPost(initialPost));
      } else {
        // Try to load from storage
        const { state: loadedState, error } = BuilderStorage.loadCompleteState();
        
        if (error && onStorageError) {
          const recoveryOptions: RecoveryOptions = {
            clearAndNew: () => {
              BuilderStorage.clearAll();
              const newPost = createNewPost();
              initializeCountersFromPost(newPost.content);
              dispatch(actions.setPost(newPost));
            },
            keepAndIgnore: () => {
              const newPost = createNewPost();
              initializeCountersFromPost(newPost.content);
              dispatch(actions.setPost(newPost));
            },
            tryRecover: () => {
              // Attempt to recover partial data
              if (loadedState.post) {
                initializeCountersFromPost(loadedState.post.content);
                dispatch(actions.setPost(loadedState.post));
              } else {
                const newPost = createNewPost();
                initializeCountersFromPost(newPost.content);
                dispatch(actions.setPost(newPost));
              }
            }
          };
          
          onStorageError(error, recoveryOptions);
        } else if (loadedState.post) {
          // Initialize counters from loaded post content
          initializeCountersFromPost(loadedState.post.content);
          
          // UI state is already loaded in initial state, just set the post
          dispatch(actions.setPostWithPreservedUI(loadedState.post));
          
          // Restore history if available (using simpleHistory)
          // if (history && historyManager.current) {
          //   historyManager.current.import(history);
          // }
        } else {
          // Create new post
          const newPost = createNewPost();
          // Initialize counters for new post
          initializeCountersFromPost(newPost.content);
          dispatch(actions.setPost(newPost));
        }
      }
      
      setIsInitialized(true);
      // Set UI ready after a small delay to ensure layout is stable
      setTimeout(() => setIsUIReady(true), 50);
    }
  }, [isInitialized, initialPost, onStorageError, dispatch]);
  
  // Optimized auto-save effect - only trigger on meaningful changes
  useEffect(() => {
    if (state.post && state.uiState.isDirty) {
      // Clear existing timer
      if (autoSaveTimer.current) {
        clearTimeout(autoSaveTimer.current);
      }
      
      // Set new timer - longer interval and only when dirty
      autoSaveTimer.current = window.setTimeout(() => {
        try {
          // const history = historyManager.current?.export();
          BuilderStorage.saveCompleteState(state);
          
          dispatch(actions.setDirty(false));
          dispatch(actions.setLastSaved(Date.now()));
        } catch (error) {
          console.error('Auto-save failed:', error);
        }
      }, 5000); // 5 seconds instead of 2
    }
    
    return () => {
      if (autoSaveTimer.current) {
        clearTimeout(autoSaveTimer.current);
      }
    };
  }, [state.post, state.uiState.isDirty, dispatch]); // Only depend on meaningful changes
  
  // Warn before unload if dirty
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (state.uiState.isDirty) {
        // Modern way: just call preventDefault(), browser will show default message
        e.preventDefault();
        // For older browsers compatibility, set returnValue to empty string
        e.returnValue = '';
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [state.uiState.isDirty]);
  
  // Create new post helper
  const createNewPost = (): Post => ({
    id: Date.now(),
    createdAt: Date.now(),
    updatedAt: Date.now(),
    publishedAt: 0,
    title: 'Untitled Post',
    slug: 'untitled-post',
    postType: 'ARTICLE',
    status: 'DRAFT',
    content: createSampleContent(),
    author: {
      id: '1',
      name: 'Current User',
      email: '<EMAIL>'
    },
    parentId: null
  });
  
  // Simple History operations
  const undo = useCallback(() => {
    if (!historyManager.current) {
      return;
    }
    
    const previousState = historyManager.current.undo();
    if (previousState) {
      dispatchWithoutHistory(actions.restoreState(previousState));
    }
  }, [dispatchWithoutHistory]);
  
  const redo = useCallback(() => {
    if (!historyManager.current) {
      return;
    }
    
    const nextState = historyManager.current.redo();
    if (nextState) {
      dispatchWithoutHistory(actions.restoreState(nextState));
    }
  }, [dispatchWithoutHistory]);
  
  const canUndo = useCallback(() => {
    const result = historyManager.current?.canUndo() || false;
    return result;
  }, []);
  
  const canRedo = useCallback(() => {
    const result = historyManager.current?.canRedo() || false;
    return result;
  }, []);
  
  // Legacy compatibility - these will be provided by smaller contexts
  // but we keep minimal versions here for backward compatibility
  
  // Simplified context value with only core state and dispatch
  const contextValue = useMemo<BuilderContextValue>(() => ({
    ...state,
    dispatch,
    undo,
    redo,
    canUndo,
    canRedo,
    isUIReady
  }), [state, dispatch, undo, redo, canUndo, canRedo, isUIReady]);
  
  return (
    <BuilderContext.Provider value={contextValue}>
      <DataProvider post={state.post} selectedNodeId={state.uiState.selectedNodeId}>
        <UIProvider 
          uiState={state.uiState}
          isUIReady={isUIReady}
          dispatch={dispatch}
          editorMode={state.uiState.editorMode}
        >
          <ActionsProvider
            post={state.post}
            copiedNode={state.uiState.copiedNode}
            dispatch={dispatch}
            dispatchWithoutHistory={dispatchWithoutHistory}
            onSave={onSave}
            historyManager={historyManager.current}
          >
            {children}
          </ActionsProvider>
        </UIProvider>
      </DataProvider>
    </BuilderContext.Provider>
  );
};

// Custom hook to use builder context
export const useBuilder = (): BuilderContextValue => {
  const context = useContext(BuilderContext);
  if (!context) {
    throw new Error('useBuilder must be used within a BuilderProvider');
  }
  return context;
};

// Enhanced hook that combines all contexts for convenience
export const useBuilderCombined = () => {
  const core = useBuilder();
  const data = useData();
  const ui = useUI();
  const actions = useActions();
  
  // Create getSelectedNode function that uses current selectedNodeId
  const getSelectedNode = useCallback((): TreeNode | null => {
    const selectedNodeId = ui.uiState?.selectedNodeId;
    if (!selectedNodeId || !data.post) return null;
    return data.findNodeById(selectedNodeId);
  }, [ui.uiState?.selectedNodeId, data.post, data.findNodeById]);
  
  return {
    // Core state
    ...core,
    
    // Data utilities
    ...data,
    
    // UI operations
    ...ui,
    
    // Actions
    ...actions,
    
    // Combined utilities
    getSelectedNode
  };
};