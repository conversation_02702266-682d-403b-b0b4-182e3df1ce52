import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { NewsArticleCard } from "./NewsArticleCard";
import { Post } from "@/features/post/states/types";
import { GridHeader } from "@/components/grid/GridHeader";
import { GridPagination } from "@/components/grid/GridPagination";

export interface NewsGridDisplayProps {
  title?: string;
  posts: Post[];
  itemsPerPage?: number;
  className?: string;
  onArticleClick?: (post: Post) => void;
}

export const NewsGridDisplay: React.FC<NewsGridDisplayProps> = ({
  title,
  posts,
  itemsPerPage = 3,
  className,
  onArticleClick,
}) => {

  console.log("NewsGridDisplay: ", posts);

  const [currentPage, setCurrentPage] = useState(0);

  const publishedPosts = posts.filter(post => post.status.includes(""));

  if (publishedPosts.length === 0) {
    return (
      <div className={cn("text-center py-8", className)}>
        <p className="text-gray-500"><PERSON><PERSON><PERSON>ng có bài viết nào để hiển thị</p>
      </div>
    );
  }

  const totalPages = Math.ceil(publishedPosts.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPosts = publishedPosts.slice(startIndex, endIndex);

  const handlePrevious = () => {
    setCurrentPage((prev) => (prev === 0 ? totalPages - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => (prev === totalPages - 1 ? 0 : prev + 1));
  };

  return (
    <div className={cn("space-y-6", className)}>
      {title && (
        <GridHeader
          length={publishedPosts.length}
          itemsPerPage={itemsPerPage}
          title={title}
          onPrevious={handlePrevious}
          onNext={handleNext}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {currentPosts.map((post) => (
          <NewsArticleCard
            post={post}
            size="medium"
            onClick={() => onArticleClick?.(post)}
          />
        ))}
      </div>

      {totalPages > 1 && (
        <GridPagination
          length={publishedPosts.length}
          itemsPerPage={itemsPerPage}
          onChangePagination={(index) => setCurrentPage(index)}
        />
      )}
    </div>
  );
};
