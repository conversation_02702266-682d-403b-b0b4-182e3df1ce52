import { useState, useCallback, useEffect } from 'react';

/**
 * Configuration for a single panel
 */
interface PanelConfig {
  /** Minimum width in pixels */
  minWidth: number;
  /** Maximum width in pixels */
  maxWidth: number;
  /** Default width in pixels */
  defaultWidth: number;
}

/**
 * Configuration for resizable panels system
 */
interface ResizablePanelsConfig {
  /** Configuration for the left panel */
  leftPanel: PanelConfig;
  /** Configuration for the right panel */
  rightPanel: PanelConfig;
  /** Optional localStorage key for persistence */
  storageKey?: string;
}

/**
 * State and actions returned by useResizablePanels hook
 */
interface ResizablePanelsState {
  /** Current width of left panel in pixels */
  leftWidth: number;
  /** Current width of right panel in pixels */
  rightWidth: number;
  /** Function to resize left panel by delta amount */
  resizeLeftPanel: (delta: number) => void;
  /** Function to resize right panel by delta amount */
  resizeRightPanel: (delta: number) => void;
}

/**
 * Hook for managing resizable panels with localStorage persistence
 * 
 * Features:
 * - State management for panel widths
 * - localStorage persistence (optional)
 * - Width constraints enforcement
 * - Smooth resize with integer pixels
 * 
 * @param config - Configuration object for panels
 * @returns State and resize functions
 */
export const useResizablePanels = (config: ResizablePanelsConfig): ResizablePanelsState => {
  const [leftWidth, setLeftWidth] = useState(config.leftPanel.defaultWidth);
  const [rightWidth, setRightWidth] = useState(config.rightPanel.defaultWidth);

  // Load from localStorage on mount
  useEffect(() => {
    if (config.storageKey) {
      try {
        const stored = localStorage.getItem(config.storageKey);
        if (stored) {
          const { leftWidth: storedLeft, rightWidth: storedRight } = JSON.parse(stored);
          
          // Validate stored values
          if (
            storedLeft >= config.leftPanel.minWidth && 
            storedLeft <= config.leftPanel.maxWidth
          ) {
            setLeftWidth(storedLeft);
          }
          
          if (
            storedRight >= config.rightPanel.minWidth && 
            storedRight <= config.rightPanel.maxWidth
          ) {
            setRightWidth(storedRight);
          }
        }
      } catch (error) {
        console.warn('Failed to load panel widths from localStorage:', error);
      }
    }
  }, [config]);

  // Save to localStorage when widths change
  useEffect(() => {
    if (config.storageKey) {
      try {
        localStorage.setItem(config.storageKey, JSON.stringify({
          leftWidth,
          rightWidth
        }));
      } catch (error) {
        console.warn('Failed to save panel widths to localStorage:', error);
      }
    }
  }, [leftWidth, rightWidth, config.storageKey]);

  const resizeLeftPanel = useCallback((delta: number) => {
    setLeftWidth(prevWidth => {
      const newWidth = Math.round(prevWidth + delta);
      return Math.max(
        config.leftPanel.minWidth,
        Math.min(config.leftPanel.maxWidth, newWidth)
      );
    });
  }, [config.leftPanel]);

  const resizeRightPanel = useCallback((delta: number) => {
    setRightWidth(prevWidth => {
      const newWidth = Math.round(prevWidth - delta); // Right panel shrinks when dragged left
      return Math.max(
        config.rightPanel.minWidth,
        Math.min(config.rightPanel.maxWidth, newWidth)
      );
    });
  }, [config.rightPanel]);

  return {
    leftWidth,
    rightWidth,
    resizeLeftPanel,
    resizeRightPanel
  };
};