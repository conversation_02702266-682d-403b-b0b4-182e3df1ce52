import React, { use<PERSON>allback, useMemo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { TreeNode } from "../../../../context/types";
import { ColorPicker } from "../../inputs/ColorPicker";
import { BorderPopover } from "../../inputs/BorderPopover";
import {
  BORDER_WIDTH_GROUPS,
  BORDER_STYLE_OPTIONS,
} from "../../utils/borderGroups";
import {
  parseBorderState,
  borderStateToClassName,
  switchBorderMode,
} from "./border/borderHelper";
import { removeBorderClasses } from "../../utils/classNameUtils";
import type { BorderState, BorderMode, BorderStyle } from "./border/types";

interface BorderSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const BorderSectionComponent: React.FC<BorderSectionProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  // Parse current state using helper - this is the ONLY source of truth (MEMOIZED)
  const currentState = useMemo(
    () => parseBorderState(currentClassName) as BorderState,
    [currentClassName]
  );

  // Extract only border classes for custom mode textarea (MEMOIZED CORRECTLY)
  const borderClasses = useMemo(() => {
    return currentClassName
      .split(/\s+/)
      .filter((c) => c.startsWith("border"))
      .join(" ");
  }, [currentClassName]);

  // Update node className
  const updateClassName = (newClassName: string) => {
    onChange({
      style: {
        ...node.style,
        className: newClassName,
      },
    });
  };

  // Handle style change (MEMOIZED)
  const handleStyleChange = useCallback(
    (style: BorderStyle) => {
      const newState: BorderState = {
        ...currentState,
        style,
      };
      const cleanClassName = removeBorderClasses(currentClassName);
      const borderClasses = borderStateToClassName(newState);
      updateClassName(`${cleanClassName} ${borderClasses}`.trim());
    },
    [currentState, currentClassName]
  );

  // Handle mode change (MEMOIZED)
  const handleModeChange = useCallback(
    (mode: BorderMode) => {
      // Use unified switchBorderMode for all cases
      const newState = switchBorderMode(currentState, mode);

      const cleanClassName = removeBorderClasses(currentClassName);
      const borderClasses = borderStateToClassName(newState);
      updateClassName(`${cleanClassName} ${borderClasses}`.trim());
    },
    [currentState, currentClassName]
  );

  // Handle option update (MEMOIZED)
  const handleOptionUpdate = useCallback(
    (index: number, field: "size" | "color", value: string) => {
      const newOptions = [...currentState.options];
      newOptions[index] = { ...newOptions[index], [field]: value };

      const newState: BorderState = {
        ...currentState,
        options: newOptions,
      };

      const cleanClassName = removeBorderClasses(currentClassName);
      const borderClasses = borderStateToClassName(newState);
      updateClassName(`${cleanClassName} ${borderClasses}`.trim());
    },
    [currentState, currentClassName]
  );

  // Style icon helper
  const getStyleIcon = (style: string) => {
    switch (style) {
      case "solid":
        return "━";
      case "dashed":
        return "┅";
      case "dotted":
        return "⋯";
      case "double":
        return "═";
      default:
        return "━";
    }
  };

  // Render border controls based on mode
  const renderBorderControls = () => {
    if (currentState.mode === "custom") {
      return (
        <div className="space-y-2">
          <p className="text-xs text-muted-foreground">
            Các class border phức tạp không thể chỉnh sửa bằng giao diện. Chuyển
            sang tab "Nâng cao" để chỉnh sửa.
          </p>
          <Textarea
            value={borderClasses}
            placeholder={
              borderClasses ? "border-2 border-red-500" : "Không có nội dung"
            }
            className="font-mono text-xs bg-gray-50"
            rows={3}
            readOnly
            disabled
          />
        </div>
      );
    }

    const prefixLabels = {
      all: "Viền",
      horizontal: "Trái Phải",
      vertical: "Trên Dưới",
      individual: ["Trên", "Phải", "Dưới", "Trái"],
    };

    return (
      <div className="space-y-2">
        {currentState.options.map((option, idx: number) => {
          const label =
            currentState.mode === "individual"
              ? prefixLabels.individual[idx]
              : prefixLabels[currentState.mode as keyof typeof prefixLabels] ||
                "Viền";

          return (
            <div key={idx} className="flex items-center gap-2">
              <span className="text-sm w-10">{label}</span>

              <BorderPopover
                value={option.size}
                onValueChange={(value) =>
                  handleOptionUpdate(idx, "size", value)
                }
                groups={BORDER_WIDTH_GROUPS}
                placeholder="1"
                disabled={disabled}
                className="w-16 h-7 text-xs"
                renderValue={(val) => `${val}px`}
              />

              <ColorPicker
                value={option.color}
                onChange={(value) => handleOptionUpdate(idx, "color", value)}
                disabled={disabled}
                className="h-7 w-7 p-0 flex items-center justify-center"
                renderValue={(color) => (
                  <div
                    className={`w-5 h-5 rounded-full border border-gray-300 bg-${color}`}
                    style={
                      color === "transparent"
                        ? {
                            backgroundImage: `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='a' patternUnits='userSpaceOnUse' width='8' height='8'%3e%3cpath d='m0 0h4v4h-4zm4 4h4v4h-4z' fill='%23f3f4f6'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100%25' height='100%25' fill='url(%23a)'/%3e%3c/svg%3e")`,
                          }
                        : {}
                    }
                  />
                )}
              />
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="space-y-3">
      {/* Mode selector row */}
      <div className="flex items-center gap-2">
        {/* Mode selector - always show */}
        <Select
          value={currentState.mode}
          onValueChange={handleModeChange}
          disabled={disabled}
        >
          <SelectTrigger className="w-40 h-7">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">Mặc Định (không có)</SelectItem>
            <SelectItem value="all">Toàn bộ</SelectItem>
            <SelectItem value="vertical">Trên Dưới</SelectItem>
            <SelectItem value="horizontal">Trái Phải</SelectItem>
            <SelectItem value="individual">Độc lập</SelectItem>
          </SelectContent>
        </Select>

        {/* Style selector - only show for border modes (not none or custom) */}
        {currentState.mode !== "custom" && currentState.mode !== "none" && (
          <Select
            value={currentState.style}
            onValueChange={handleStyleChange}
            disabled={disabled}
          >
            <SelectTrigger className="w-20 h-7">
              <SelectValue>
                <span className="text-lg">
                  {getStyleIcon(currentState.style)}
                </span>
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {BORDER_STYLE_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-2">
                    <span className="text-lg">
                      {getStyleIcon(option.value)}
                    </span>
                    <span className="text-xs">{option.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {/* Border controls */}
      {currentState.mode !== "none" && renderBorderControls()}
    </div>
  );
};

export const BorderSection = React.memo(BorderSectionComponent);
