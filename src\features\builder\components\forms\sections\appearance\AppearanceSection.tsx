import React, { useMemo, useCallback } from "react";
import { TreeNode } from "../../../../context/types";
import { ColorPicker } from "../../inputs/ColorPicker";
import { AppearancePopover } from "../../inputs/AppearancePopover";
import { OPACITY_GROUPS } from "../../utils/opacityGroups";
import {
  removeOpacityAndBgClasses,
  combineClassNames,
  safeMatch,
} from "../../utils/classNameUtils";

interface AppearanceSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const AppearanceSectionComponent: React.FC<AppearanceSectionProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  // Parse current opacity and background color (MEMOIZED)
  const currentState = useMemo(() => {
    const opacity = safeMatch(
      currentClassName,
      /\bopacity-\d+\b/,
      "opacity-100"
    );
    const bgMatch = safeMatch(currentClassName, /\bbg-\S+\b/);
    const backgroundColor = bgMatch ? bgMatch.replace("bg-", "") : "";

    return { opacity, backgroundColor };
  }, [currentClassName]);

  // Remove opacity and background classes (MEMOIZED CORRECTLY)
  const cleanClassName = useMemo(() => {
    return removeOpacityAndBgClasses(currentClassName);
  }, [currentClassName]);

  // Update node className
  const updateClassName = (newClassName: string) => {
    onChange({
      style: {
        ...node.style,
        className: newClassName,
      },
    });
  };

  // Handle opacity change (MEMOIZED)
  const handleOpacityChange = useCallback(
    (value: string) => {
      const opacityClass = value !== "opacity-100" ? value : "";
      const bgClass = currentState.backgroundColor
        ? `bg-${currentState.backgroundColor}`
        : "";
      const newClasses = combineClassNames(
        cleanClassName,
        opacityClass,
        bgClass
      );
      updateClassName(newClasses);
    },
    [cleanClassName, currentState.backgroundColor]
  );

  // Handle background color change (MEMOIZED)
  const handleBgColorChange = useCallback(
    (value: string) => {
      const opacityClass =
        currentState.opacity !== "opacity-100" ? currentState.opacity : "";
      const bgClass = value ? `bg-${value}` : "";
      const newClasses = combineClassNames(
        cleanClassName,
        opacityClass,
        bgClass
      );
      updateClassName(newClasses);
    },
    [cleanClassName, currentState.opacity]
  );

  return (
    <div className="space-y-2">
      {/* Opacity */}
      <div className="flex items-center gap-2">
        <span className="text-sm w-20">Độ mờ</span>
        <AppearancePopover
          value={currentState.opacity}
          onValueChange={handleOpacityChange}
          groups={OPACITY_GROUPS}
          placeholder="100%"
          disabled={disabled}
          className="w-24"
        />
      </div>

      {/* Background Color */}
      <div className="flex items-center gap-2">
        <span className="text-sm w-20">Màu nền</span>
        <ColorPicker
          value={currentState.backgroundColor}
          onChange={handleBgColorChange}
          disabled={disabled}
          className="h-7 w-7 p-0 flex items-center justify-center"
          renderValue={(color) => (
            <div
              className={`w-5 h-5 rounded-full border border-gray-300 bg-${color}`}
              style={
                color === "transparent"
                  ? {
                      backgroundImage: `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='a' patternUnits='userSpaceOnUse' width='8' height='8'%3e%3cpath d='m0 0h4v4h-4zm4 4h4v4h-4z' fill='%23f3f4f6'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100%25' height='100%25' fill='url(%23a)'/%3e%3c/svg%3e")`,
                    }
                  : {}
              }
            />
          )}
        />
      </div>
    </div>
  );
};

export const AppearanceSection = React.memo(AppearanceSectionComponent);
