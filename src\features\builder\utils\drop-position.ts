import { TreeNode } from '../context/types';
import { DropPosition } from '../types/drag-drop';

/**
 * MILESTONE: Single Drop Indicator System
 * 
 * Shows only the active drop indicator where the element will be placed.
 * No confusion with multiple indicators.
 * 
 * Benefits:
 * - Clear, single indicator shows exact drop position
 * - No visual clutter with multiple indicators
 * - Clean and focused user experience
 * - Works perfectly with mixed layouts (flex wrapping, inline-blocks, etc.)
 */

export interface SmartDropPosition {
  position: DropPosition;
  visualPosition: 'horizontal' | 'vertical' | 'center';
}

/**
 * Calculate drop position based on mouse distance to edges
 * Returns which indicator type to show (horizontal/vertical/center)
 */
export const calculateSmartDropPosition = (
  targetNode: TreeNode,
  _targetParent: TreeNode | null,
  mouseX: number,
  mouseY: number,
  targetElement: HTMLElement
): SmartDropPosition | null => {
  const rect = targetElement.getBoundingClientRect();
  const relativeX = mouseX - rect.left;
  const relativeY = mouseY - rect.top;
  
  // For frame nodes that can accept children
  if (targetNode.type === 'frame') {
    const isEmpty = !targetNode.children || targetNode.children.length === 0;
    const centerThreshold = isEmpty ? 0.2 : 0.3;
    
    // Check center threshold for inside drop
    const isNearCenter = 
      relativeX > rect.width * centerThreshold &&
      relativeX < rect.width * (1 - centerThreshold) &&
      relativeY > rect.height * centerThreshold &&
      relativeY < rect.height * (1 - centerThreshold);
    
    if (isNearCenter) {
      return {
        position: 'inside',
        visualPosition: 'center'
      };
    }
  }
  
  // Calculate distances to all edges
  const distanceToLeft = relativeX;
  const distanceToRight = rect.width - relativeX;
  const distanceToTop = relativeY;
  const distanceToBottom = rect.height - relativeY;
  
  // Find minimum distance to determine which edge is closest
  const minHorizontal = Math.min(distanceToLeft, distanceToRight);
  const minVertical = Math.min(distanceToTop, distanceToBottom);
  
  // Return indicator type based on closest edge
  if (minHorizontal < minVertical) {
    // Mouse is closer to left/right edges - show vertical indicators
    return {
      position: distanceToLeft < distanceToRight ? 'before' : 'after',
      visualPosition: 'horizontal'
    };
  } else {
    // Mouse is closer to top/bottom edges - show horizontal indicators
    return {
      position: distanceToTop < distanceToBottom ? 'before' : 'after',
      visualPosition: 'vertical'
    };
  }
};

/**
 * Generate style for single active indicator
 * 
 * @param visualPosition - 'horizontal' | 'vertical' | 'center'
 * @param rect - Target element's bounding rectangle
 * @param dropPosition - 'before' | 'after' | 'inside'
 * @returns Array with single indicator style
 */
export const getDropIndicatorStyles = (
  visualPosition: SmartDropPosition['visualPosition'],
  rect: DOMRect,
  dropPosition: DropPosition
): React.CSSProperties[] => {
  const indicatorSize = 3; // Thickness of the indicator line
  const offset = 1; // Gap between indicator and element
  
  switch (visualPosition) {
    case 'horizontal': {
      // Show only the active indicator (left or right)
      const horizontalBase = {
        position: 'fixed' as const,
        width: `${indicatorSize}px`,
        height: `${rect.height}px`,
        top: `${rect.top}px`,
      };
      
      if (dropPosition === 'before') {
        // Left indicator only
        return [{
          ...horizontalBase,
          left: `${rect.left - indicatorSize - offset}px`,
        }];
      } else {
        // Right indicator only  
        return [{
          ...horizontalBase,
          left: `${rect.right + offset}px`,
        }];
      }
    }
      
    case 'vertical': {
      // Show only the active indicator (top or bottom)
      const verticalBase = {
        position: 'fixed' as const,
        width: `${rect.width}px`,
        height: `${indicatorSize}px`,
        left: `${rect.left}px`,
      };
      
      if (dropPosition === 'before') {
        // Top indicator only
        return [{
          ...verticalBase,
          top: `${rect.top - indicatorSize - offset}px`,
        }];
      } else {
        // Bottom indicator only
        return [{
          ...verticalBase,
          top: `${rect.bottom + offset}px`,
        }];
      }
    }
      
    case 'center':
      // Single center indicator for inside drop
      return [{
        position: 'fixed',
        left: `${rect.left}px`,
        top: `${rect.top}px`,
        width: `${rect.width}px`,
        height: `${rect.height}px`,
      }];
  }
};