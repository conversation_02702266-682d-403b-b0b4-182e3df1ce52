/**
 * Shadow Types
 * Type definitions for shadow functionality
 */

export type ShadowType = 'none' | 'box' | 'drop';

export type ShadowSize = 'sm' | 'md' | 'lg' | 'xl' | '2xl';

export interface ShadowState {
  type: ShadowType;
  size: ShadowSize;
  color: string;
  opacity: string;
}

// Default shadow opacity (most commonly used)
export const DEFAULT_SHADOW_OPACITY = '50';

// Default shadow color
export const DEFAULT_SHADOW_COLOR = 'gray-500';

// Shadow size options
export const SHADOW_SIZE_OPTIONS = [
  { value: 'sm', label: 'Nhỏ' },
  { value: 'md', label: 'Vừa' },
  { value: 'lg', label: 'Lớn' },
  { value: 'xl', label: 'Rất lớn' },
  { value: '2xl', label: 'Cực lớn' }
] as const;

// Shadow type options  
export const SHADOW_TYPE_OPTIONS = [
  { value: 'none', label: 'Không có' },
  { value: 'box', label: 'Box Shadow' },
  { value: 'drop', label: 'Drop Shadow' }
] as const;

// Shadow opacity options (most common values)
export const SHADOW_OPACITY_OPTIONS = [
  { value: '10', label: '10% - Rất nhạt' },
  { value: '20', label: '20% - Nhạt' },
  { value: '25', label: '25% - Nhạt vừa' },
  { value: '30', label: '30% - Vừa nhạt' },
  { value: '40', label: '40% - Vừa' },
  { value: '50', label: '50% - Chuẩn (khuyến nghị)' },
  { value: '60', label: '60% - Đậm vừa' },
  { value: '70', label: '70% - Đậm' },
  { value: '75', label: '75% - Rất đậm' },
  { value: '80', label: '80% - Cực đậm' },
  { value: '90', label: '90% - Gần đặc' },
  { value: '100', label: '100% - Đặc hoàn toàn' }
] as const;