import { restApi } from "@/api/restApi";
import type { PortalLinkConfig } from "./type";
import { AxiosResponse } from "axios";

// Base URLs for config API
const PUBLIC_CONFIG_URL = "/portal/v1/public/config";
const ADMIN_CONFIG_URL = "/portal/v1/admin/config";

// GET /portal/v1/public/config/portal_links
export async function fetchPortalLinkConfig(): Promise<
  AxiosResponse<PortalLinkConfig>
> {
  const res = await restApi.get<AxiosResponse<PortalLinkConfig>>(
    `${PUBLIC_CONFIG_URL}/portal_links`
  );
  return res as unknown as AxiosResponse<PortalLinkConfig>;
}

// POST /portal/v1/admin/config/portal_links
export async function updatePortalLinkConfig(
  data: PortalLinkConfig
): Promise<AxiosResponse<PortalLinkConfig>> {
  const res = await restApi.post<AxiosResponse<PortalLinkConfig>>(
    `${ADMIN_CONFIG_URL}/portal_links`,
    data
  );
  return res as unknown as AxiosResponse<PortalLinkConfig>;
}
