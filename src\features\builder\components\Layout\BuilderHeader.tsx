import React, { use<PERSON><PERSON>back, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useBuilderCombined } from '../../context/BuilderContext';
import { AddNodeDialog } from '../AddNodeDialog';
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  Eye, 
  Edit,
  Move,
  Save,
  Undo,
  Redo,
  PanelLeftClose,
  PanelRightClose,
  Plus,
  Trash2,
  Copy,
  Clipboard
} from 'lucide-react';
import { TreeNodeType, TreeNode } from '../../context/types';

interface BuilderHeaderProps {
  className?: string;
}

export const BuilderHeader: React.FC<BuilderHeaderProps> = ({ className }) => {
  const [showAddNodeDialog, setShowAddNodeDialog] = React.useState(false);
  
  const { 
    post, 
    uiState, 
    setViewMode, 
    setEditorMode,
    toggleTreePanel,
    togglePropertiesPanel,
    undo,
    redo,
    canUndo,
    canRedo,
    savePost,
    addNode,
    deleteNode,
    getSelectedNode,
    copyNode,
    pasteNode,
    hasCopiedNode
  } = useBuilderCombined();

  const isDirty = uiState?.isDirty ?? false;
  const viewMode = uiState?.viewMode ?? 'desktop';
  const editorMode = uiState?.editorMode ?? 'edit';
  const selectedNode = getSelectedNode();

  const handleAddNode = useCallback((type: TreeNodeType) => {
    
    if (!selectedNode) {
      // No selection - add to root
      addNode(post?.content.id || 'root', type);
      return;
    }
    
    if (selectedNode.type === 'frame' || selectedNode.id === 'root') {
      // Selected is frame (including root) - add as last child
      addNode(selectedNode.id, type);
    } else {
      // Selected is not frame - add after it
      // Need to find parent and index
      const findParentAndIndex = (node: TreeNode, targetId: string): { parentId: string; index: number } | null => {
        if (!node || !node.children) return null;
        
        for (let i = 0; i < node.children.length; i++) {
          if (node.children[i].id === targetId) {
            return { parentId: node.id, index: i + 1 };
          }
          
          // Recursively search in children
          const result = findParentAndIndex(node.children[i], targetId);
          if (result) return result;
        }
        return null;
      };

      if (post?.content) {
        const location = findParentAndIndex(post.content, selectedNode.id);
        if (location) {
          addNode(location.parentId, type, location.index);
        } else {
          // Fallback to root if parent not found
          addNode(post.content.id || 'root', type);
        }
      }
    }
  }, [selectedNode, addNode, post?.content]);

  const handleDeleteNode = useCallback(() => {
    if (selectedNode && selectedNode.id !== 'root') {
      deleteNode(selectedNode.id);
    }
  }, [selectedNode, deleteNode]);

  const canDeleteNode = useMemo(() => {
    return editorMode === 'edit' && selectedNode && selectedNode.id !== 'root';
  }, [editorMode, selectedNode]);

  const handleCopyNode = useCallback(() => {
    if (selectedNode && selectedNode.id !== 'root') {
      copyNode(selectedNode.id);
    }
  }, [selectedNode, copyNode]);

  const handlePasteNode = useCallback(() => {
    pasteNode();
  }, [pasteNode]);

  const canCopyNode = useMemo(() => {
    return editorMode === 'edit' && selectedNode && selectedNode.id !== 'root';
  }, [editorMode, selectedNode]);

  const canPasteNode = useMemo(() => {
    return editorMode === 'edit' && hasCopiedNode();
  }, [editorMode, hasCopiedNode]);

  return (
    <header className={cn("flex items-center justify-between px-4", className)}>
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        <h1 className="text-lg font-semibold">{post?.title || 'Untitled'}</h1>
        <Badge variant={isDirty ? "destructive" : "secondary"}>
          {isDirty ? "Chưa lưu" : "Đã lưu"}
        </Badge>
      </div>

      {/* Center Section - View Mode */}
      <div className="flex items-center space-x-2">
        {/* Panel Toggles */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => toggleTreePanel?.()}
          title="Toggle tree panel"
        >
          <PanelLeftClose className="w-4 h-4" />
        </Button>

        {/* View Mode Tabs */}
        <div className="flex items-center">
          <div className="flex rounded-lg border-2 bg-gray-100 p-1">
            <button
              className={cn(
                "p-2 transition-all duration-200",
                "first:rounded-l-md last:rounded-r-md",
                viewMode === 'mobile'
                  ? "bg-blue-500 text-white"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
              )}
              onClick={() => setViewMode('mobile')}
              title="Mobile"
            >
              <Smartphone className="w-4 h-4" />
            </button>
            <button
              className={cn(
                "p-2 transition-all duration-200",
                "first:rounded-l-md last:rounded-r-md",
                viewMode === 'tablet'
                  ? "bg-blue-500 text-white"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
              )}
              onClick={() => setViewMode('tablet')}
              title="Tablet"
            >
              <Tablet className="w-4 h-4" />
            </button>
            <button
              className={cn(
                "p-2 transition-all duration-200",
                "first:rounded-l-md last:rounded-r-md",
                viewMode === 'desktop'
                  ? "bg-blue-500 text-white"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
              )}
              onClick={() => setViewMode('desktop')}
              title="Desktop"
            >
              <Monitor className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Editor Mode Tabs */}
        <div className="flex items-center">
          <div className="flex rounded-lg border-2 bg-gray-100 p-1">
            <button
              className={cn(
                "p-2 transition-all duration-200",
                "first:rounded-l-md last:rounded-r-md",
                editorMode === 'edit' 
                  ? "bg-blue-500 text-white" 
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
              )}
              onClick={() => setEditorMode('edit')}
              title="Sửa"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              className={cn(
                "p-2 transition-all duration-200",
                "first:rounded-l-md last:rounded-r-md",
                editorMode === 'preview' 
                  ? "bg-blue-500 text-white" 
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
              )}
              onClick={() => setEditorMode('preview')}
              title="Xem"
            >
              <Eye className="w-4 h-4" />
            </button>
            <button
              className={cn(
                "p-2 transition-all duration-200",
                "first:rounded-l-md last:rounded-r-md",
                editorMode === 'move' 
                  ? "bg-blue-500 text-white" 
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
              )}
              onClick={() => setEditorMode('move')}
              title="Di chuyển"
            >
              <Move className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Add/Delete/Copy/Paste Node Buttons */}
        <div className="flex items-center space-x-1 ml-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddNodeDialog(true)}
            disabled={editorMode !== 'edit'}
            title="Thêm node"
          >
            <Plus className="w-4 h-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleDeleteNode}
            disabled={!canDeleteNode}
            title="Xoá node"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
          
          <div className="w-px h-6 bg-gray-300 mx-1" />
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopyNode}
            disabled={!canCopyNode}
            title="Copy (Ctrl+C)"
          >
            <Copy className="w-4 h-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handlePasteNode}
            disabled={!canPasteNode}
            title="Paste (Ctrl+V)"
          >
            <Clipboard className="w-4 h-4" />
          </Button>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => togglePropertiesPanel?.()}
          title="Toggle properties panel"
        >
          <PanelRightClose className="w-4 h-4" />
        </Button>
      </div>

      {/* Right Section - Actions */}
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={undo}
          disabled={!canUndo()}
          title="Undo (Ctrl+Z)"
        >
          <Undo className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={redo}
          disabled={!canRedo()}
          title="Redo (Ctrl+Y)"
        >
          <Redo className="w-4 h-4" />
        </Button>
        <Button
          variant="default"
          size="sm"
          onClick={savePost}
          disabled={!isDirty}
        >
          <Save className="w-4 h-4 mr-2" />
          Lưu
        </Button>
      </div>
      
      {/* Add Node Dialog */}
      <AddNodeDialog 
        open={showAddNodeDialog}
        onOpenChange={setShowAddNodeDialog}
        onAddNode={handleAddNode}
      />
    </header>
  );
};