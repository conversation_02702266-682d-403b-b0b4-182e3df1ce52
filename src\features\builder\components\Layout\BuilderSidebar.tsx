import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import { MouseTrackingTree } from '../Tree/MouseTrackingTree';
import { useBuilderCombined } from '../../context/BuilderContext';
import { AddNodeDialog } from '../AddNodeDialog';
import { DropPosition } from '../../types/drag-drop';
import { TreeNodeType, TreeNode } from '../../context/types';

interface BuilderSidebarProps {
  className?: string;
  style?: React.CSSProperties;
}

export const BuilderSidebar: React.FC<BuilderSidebarProps> = ({ className, style }) => {
  const { post, addNode, uiState, moveNode, getSelectedNode, deleteNode } = useBuilderCombined();
  const editorMode = uiState?.editorMode ?? 'edit';
  const isPreviewMode = editorMode === 'preview';
  const isMoveMode = editorMode === 'move';
  const [showAddNodeDialog, setShowAddNodeDialog] = useState(false);
  const [currentHoverInfo, setCurrentHoverInfo] = useState<{ nodeId: string; position: 'top' | 'middle' | 'bottom' } | null>(null);
  const [dragHandled, setDragHandled] = useState(false);
  
  // Separate drag state from selection state
  const [dragState, setDragState] = useState<{
    isDragging: boolean;
    draggedNodeId: string | null;
  }>({
    isDragging: false,
    draggedNodeId: null
  });

  const handleAddNode = useCallback((type: TreeNodeType) => {
    const selectedNode = getSelectedNode();
    
    if (!selectedNode) {
      // No selection - add to root
      addNode(post?.content.id || 'root', type);
      return;
    }
    
    if (selectedNode.type === 'frame' || selectedNode.id === 'root') {
      // Selected is frame (including root) - add as last child
      addNode(selectedNode.id, type);
    } else {
      // Selected is not frame - add after it
      const findParentAndIndex = (node: TreeNode, targetId: string): { parentId: string; index: number } | null => {
        if (!node || !node.children) return null;
        
        for (let i = 0; i < node.children.length; i++) {
          if (node.children[i].id === targetId) {
            return { parentId: node.id, index: i + 1 };
          }
          
          const result = findParentAndIndex(node.children[i], targetId);
          if (result) return result;
        }
        return null;
      };

      if (post?.content) {
        const location = findParentAndIndex(post.content, selectedNode.id);
        if (location) {
          addNode(location.parentId, type, location.index);
        } else {
          addNode(post.content.id || 'root', type);
        }
      }
    }
  }, [addNode, getSelectedNode, post?.content]);

  const handleDeleteNode = useCallback(() => {
    const selectedNode = getSelectedNode();
    if (selectedNode && selectedNode.id !== 'root') {
      deleteNode(selectedNode.id);
    }
  }, [deleteNode, getSelectedNode]);

  const canDeleteNode = useMemo(() => {
    const selectedNode = getSelectedNode();
    return !isPreviewMode && uiState?.selectedNodeId && selectedNode && selectedNode.id !== 'root';
  }, [isPreviewMode, uiState?.selectedNodeId, getSelectedNode]);

  const handleMouseDebugInfo = (info: { nodeId: string; position: string } | null) => {
    // Disable drag interactions in preview mode or move mode
    if (isPreviewMode || isMoveMode) {
      setCurrentHoverInfo(null);
      return;
    }
    
    // Only show hover info when actively dragging
    if (dragState.isDragging && info) {
      setCurrentHoverInfo({
        nodeId: info.nodeId,
        position: info.position as 'top' | 'middle' | 'bottom'
      });
    } else if (!dragState.isDragging) {
      setCurrentHoverInfo(null);
    }
  };

  // Global mouse up listener to reset drag state if drag wasn't handled
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      if (dragState.isDragging && !dragHandled) {
        setDragState({
          isDragging: false,
          draggedNodeId: null
        });
        setCurrentHoverInfo(null);
      }
      setDragHandled(false);
    };

    document.addEventListener('mouseup', handleGlobalMouseUp);
    return () => document.removeEventListener('mouseup', handleGlobalMouseUp);
  }, [dragState.isDragging, dragHandled]);

  const handleDragStart = (nodeId: string) => {
    // Disable drag start in preview mode or move mode
    if (isPreviewMode || isMoveMode) {
      return;
    }
    
    setDragHandled(false);
    setDragState({
      isDragging: true,
      draggedNodeId: nodeId
    });
    // Don't auto-select when dragging starts
  };

  // Convert visual positions to standard DropPosition
  const convertPosition = (position: 'top' | 'middle' | 'bottom'): DropPosition => {
    switch (position) {
      case 'top': return 'before';
      case 'bottom': return 'after';
      case 'middle': return 'inside';
    }
  };

  const handleDragEnd = (targetNodeId: string, position: 'top' | 'middle' | 'bottom') => {
    setDragHandled(true); // Mark that drag was handled
    
    if (dragState.draggedNodeId) {
      try {
        const dropPosition = convertPosition(position);
        
        // Use clean position-based interface
        moveNode(dragState.draggedNodeId, targetNodeId, dropPosition);
      } catch (error) {
        console.error('Move failed:', error);
      }
    }
    
    // Always reset drag state
    setDragState({
      isDragging: false,
      draggedNodeId: null
    });
    setCurrentHoverInfo(null);
  };

  if (!post) return null;

  return (
    <aside className={cn("flex flex-col", className)} style={style}>
      {/* Header */}
      <div className="p-4 border-b flex items-center justify-between">
        <h2 className="font-semibold text-sm">Cấu trúc</h2>
        
        {/* Header Action Buttons */}
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAddNodeDialog(true)}
            disabled={isPreviewMode || isMoveMode}
            title="Thêm phần tử"
          >
            <Plus className="w-4 h-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDeleteNode}
            disabled={!canDeleteNode || isMoveMode}
            title="Xoá phần tử"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Tree */}
      <div 
        className="flex-1 overflow-y-auto p-2"
        onMouseUp={(e) => {
          // Handle drop on empty area - insert as last child of root
          if (dragState.isDragging && dragState.draggedNodeId) {
            e.stopPropagation(); // Prevent global handler
            handleDragEnd('root', 'middle');
          }
        }}
      >
        <MouseTrackingTree 
          nodes={[post.content]} 
          onMouseDebugInfo={handleMouseDebugInfo}
          currentHoverInfo={currentHoverInfo}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          dragState={dragState}
        />
      </div>

      {/* Footer Actions */}
      <div className="p-4 border-t space-y-3">
        {/* Preview Mode Notice */}
        {isPreviewMode && (
          <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
            <span className="font-medium">Preview Mode:</span> Tree editing is disabled
          </div>
        )}
        
        {/* Move Mode Notice */}
        {isMoveMode && (
          <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded border border-orange-200">
            <span className="font-medium">Move Mode:</span> Kéo thả trong cây đã tắt
          </div>
        )}
        
        {/* Footer Action Buttons */}
        {!isPreviewMode && !isMoveMode && (
          <div className="flex items-center justify-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAddNodeDialog(true)}
              disabled={isPreviewMode || isMoveMode}
              title="Thêm phần tử"
            >
              <Plus className="w-4 h-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleDeleteNode}
              disabled={!canDeleteNode || isMoveMode}
              title="Xoá phần tử"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Add Node Dialog */}
      <AddNodeDialog 
        open={showAddNodeDialog}
        onOpenChange={setShowAddNodeDialog}
        onAddNode={handleAddNode}
      />
    </aside>
  );
};