import React, { createContext, useContext, use<PERSON><PERSON>back, useMemo } from 'react';
import { BuilderUIState, PersistentUIState, TemporaryUIState, ViewMode, EditorMode } from './types';
import { BuilderStorage } from './storage';
import { B<PERSON>erAction, ACTION_TYPES } from './actions';

// UI-focused context for interface state
interface UIContextValue {
  uiState: BuilderUIState;
  isUIReady: boolean;
  
  // UI State operations
  setUIState: (updates: Partial<BuilderUIState>) => void;
  setPersistentUIState: (updates: Partial<PersistentUIState>) => void;
  setTemporaryUIState: (updates: Partial<TemporaryUIState>) => void;
  
  // Convenience methods for Properties Panel
  setPropertiesPanelMainTab: (tab: 'post' | 'block') => void;
  setPropertiesPanelSubTab: (tab: 'display' | 'content') => void;
  setPropertiesPanelAdvanced: (advanced: boolean) => void;
  setPropertiesPanelDisplaySection: (section: string) => void;
  
  // View and Editor modes
  setViewMode: (mode: ViewMode) => void;
  setEditorMode: (mode: EditorMode) => void;
  
  // Panel visibility
  toggleTreePanel: () => void;
  togglePropertiesPanel: () => void;
  
  // Node selection
  selectNode: (nodeId: string | null) => void;
  toggleNodeExpansion: (nodeId: string) => void;
  
  // Helper methods
  isNewlyCreatedNode: (nodeId: string) => boolean;
}

const UIContext = createContext<UIContextValue | null>(null);

interface UIProviderProps {
  children: React.ReactNode;
  uiState: BuilderUIState;
  isUIReady: boolean;
  dispatch: (action: BuilderAction) => void;
  editorMode: EditorMode;
}

export const UIProvider: React.FC<UIProviderProps> = ({ 
  children, 
  uiState, 
  isUIReady, 
  dispatch,
  editorMode 
}) => {
  // UI State operations
  const setUIState = useCallback((updates: Partial<BuilderUIState>) => {
    dispatch({ type: 'SET_UI_STATE', payload: updates });
  }, [dispatch]);

  const setPersistentUIState = useCallback((updates: Partial<PersistentUIState>) => {
    dispatch({ type: 'SET_PERSISTENT_UI_STATE', payload: updates });
    // Auto-save persistent UI state
    BuilderStorage.savePersistentUIState({ ...uiState, ...updates });
  }, [dispatch, uiState]);

  const setTemporaryUIState = useCallback((updates: Partial<TemporaryUIState>) => {
    dispatch({ type: 'SET_TEMPORARY_UI_STATE', payload: updates });
    // Auto-save temporary UI state
    BuilderStorage.saveTemporaryUIState({ 
      ...uiState, 
      temporary: { ...uiState.temporary, ...updates } 
    });
  }, [dispatch, uiState]);

  // Convenience methods for Properties Panel
  const setPropertiesPanelMainTab = useCallback((tab: 'post' | 'block') => {
    setTemporaryUIState({ propertiesPanelMainTab: tab });
  }, [setTemporaryUIState]);

  const setPropertiesPanelSubTab = useCallback((tab: 'display' | 'content') => {
    setTemporaryUIState({ propertiesPanelSubTab: tab });
  }, [setTemporaryUIState]);

  const setPropertiesPanelAdvanced = useCallback((advanced: boolean) => {
    setTemporaryUIState({ propertiesPanelAdvanced: advanced });
  }, [setTemporaryUIState]);

  const setPropertiesPanelDisplaySection = useCallback((section: string) => {
    setTemporaryUIState({ propertiesPanelDisplaySection: section });
  }, [setTemporaryUIState]);

  // View and Editor modes
  const setViewMode = useCallback((mode: ViewMode) => {
    dispatch({ type: ACTION_TYPES.SET_VIEW_MODE, payload: mode });
  }, [dispatch]);

  const setEditorMode = useCallback((mode: EditorMode) => {
    dispatch({ type: ACTION_TYPES.SET_EDITOR_MODE, payload: mode });
  }, [dispatch]);

  // Panel visibility
  const toggleTreePanel = useCallback(() => {
    dispatch({ type: ACTION_TYPES.TOGGLE_TREE_PANEL });
  }, [dispatch]);

  const togglePropertiesPanel = useCallback(() => {
    dispatch({ type: ACTION_TYPES.TOGGLE_PROPERTIES_PANEL });
  }, [dispatch]);

  // Node selection
  const selectNode = useCallback((nodeId: string | null) => {
    // In move mode, allow null selection for canceling move
    // In other modes, default to root to maintain UX consistency
    const targetNodeId = (editorMode === 'move' && nodeId === null) 
      ? null 
      : (nodeId || 'root');
    dispatch({ type: ACTION_TYPES.SELECT_NODE, payload: targetNodeId });
  }, [dispatch, editorMode]);

  const toggleNodeExpansion = useCallback((nodeId: string) => {
    dispatch({ type: ACTION_TYPES.TOGGLE_NODE_EXPANSION, payload: nodeId });
  }, [dispatch]);

  // Helper methods
  const isNewlyCreatedNode = useCallback((nodeId: string) => {
    return uiState.lastCreatedNodeId === nodeId;
  }, [uiState.lastCreatedNodeId]);

  const contextValue = useMemo<UIContextValue>(() => ({
    uiState,
    isUIReady,
    setUIState,
    setPersistentUIState,
    setTemporaryUIState,
    setPropertiesPanelMainTab,
    setPropertiesPanelSubTab,
    setPropertiesPanelAdvanced,
    setPropertiesPanelDisplaySection,
    setViewMode,
    setEditorMode,
    toggleTreePanel,
    togglePropertiesPanel,
    selectNode,
    toggleNodeExpansion,
    isNewlyCreatedNode
  }), [
    uiState,
    isUIReady,
    setUIState,
    setPersistentUIState,
    setTemporaryUIState,
    setPropertiesPanelMainTab,
    setPropertiesPanelSubTab,
    setPropertiesPanelAdvanced,
    setPropertiesPanelDisplaySection,
    setViewMode,
    setEditorMode,
    toggleTreePanel,
    togglePropertiesPanel,
    selectNode,
    toggleNodeExpansion,
    isNewlyCreatedNode
  ]);

  return (
    <UIContext.Provider value={contextValue}>
      {children}
    </UIContext.Provider>
  );
};

export const useUI = (): UIContextValue => {
  const context = useContext(UIContext);
  if (!context) {
    throw new Error('useUI must be used within a UIProvider');
  }
  return context;
};