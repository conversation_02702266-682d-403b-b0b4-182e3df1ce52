import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import type { ContactConfig, ContactState } from "./type";
import { fetchContactConfig, updateContactConfig } from "./api";

// Initial state
const initialState: ContactState = {
  data: null,
  savedData: null,
  loading: false,
  saving: false,
  error: null,
  isDirty: false,
};

// Async thunks
export const fetchContactAsync = createAsyncThunk(
  "contact/fetchContact",
  async () => {
    const response = await fetchContactConfig();
    return response.data;
  }
);

export const updateContactAsync = createAsyncThunk(
  "contact/updateContact",
  async (data: ContactConfig) => {
    const response = await updateContactConfig(data);
    return response.data;
  }
);

// Slice
const contactSlice = createSlice({
  name: "contact",
  initialState,
  reducers: {
    setContactData: (state, action) => {
      state.data = action.payload;
      state.isDirty = true;
    },
    setDirty: (state, action) => {
      state.isDirty = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch contact
      .addCase(fetchContactAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchContactAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload || null;
        state.savedData = action.payload || null;
        state.isDirty = false;
      })
      .addCase(fetchContactAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch contact data";
      })
      // Update contact
      .addCase(updateContactAsync.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updateContactAsync.fulfilled, (state, action) => {
        state.saving = false;
        state.data = action.payload || null;
        state.savedData = action.payload || null;
        state.isDirty = false;
      })
      .addCase(updateContactAsync.rejected, (state, action) => {
        state.saving = false;
        state.error = action.error.message || "Failed to update contact data";
      });
  },
});

export const { setContactData, setDirty, clearError } = contactSlice.actions;
export default contactSlice.reducer;
