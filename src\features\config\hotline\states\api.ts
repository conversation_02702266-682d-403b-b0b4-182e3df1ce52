import { restApi } from "@/api/restApi";
import type { HotlineConfig } from "./type";
import { AxiosResponse } from "axios";

// Base URLs for config API
const PUBLIC_CONFIG_URL = "/portal/v1/public/config";
const ADMIN_CONFIG_URL = "/portal/v1/admin/config";

// GET /portal/v1/public/config/hot_lines
export async function fetchHotlineConfig(): Promise<
  AxiosResponse<HotlineConfig>
> {
  const res = await restApi.get<AxiosResponse<HotlineConfig>>(
    `${PUBLIC_CONFIG_URL}/hot_lines`
  );
  return res as unknown as AxiosResponse<HotlineConfig>;
}

// POST /portal/v1/admin/config/hot_lines
export async function updateHotlineConfig(
  data: HotlineConfig
): Promise<AxiosResponse<HotlineConfig>> {
  const res = await restApi.post<AxiosResponse<HotlineConfig>>(
    `${ADMIN_CONFIG_URL}/hot_lines`,
    data
  );
  return res as unknown as AxiosResponse<HotlineConfig>;
}
