import React, { useContext } from 'react';
import { cn } from '@/lib/utils';
import { NodeRendererProps } from '../NodeFactory';
import { compareContainerNodeProps } from '@/lib/memo-utils';

// Create a context for the renderer to avoid circular dependency
const NodeRendererContext = React.createContext<React.ComponentType<NodeRendererProps> | null>(null);

export const NodeRendererProvider: React.FC<{
  renderer: React.ComponentType<NodeRendererProps>;
  children: React.ReactNode;
}> = ({ renderer, children }) => {
  return (
    <NodeRendererContext.Provider value={renderer}>
      {children}
    </NodeRendererContext.Provider>
  );
};

const useNodeRenderer = () => {
  const renderer = useContext(NodeRendererContext);
  if (!renderer) {
    throw new Error('useNodeRenderer must be used within NodeRendererProvider');
  }
  return renderer;
};
// Utility functions for className checking
const hasWidthClass = (className: string = ''): boolean => {
  return /\b(w-|width-|min-w-|max-w-)/.test(className);
};

const hasHeightClass = (className: string = ''): boolean => {
  return /\b(h-|height-|min-h-|max-h-)/.test(className);
};


const FrameNodeComponent = React.forwardRef<HTMLDivElement, NodeRendererProps & React.HTMLAttributes<HTMLDivElement>>(({ 
  node,
  ...htmlProps
}, ref) => {
  const NodeRenderer = useNodeRenderer();
  const hasChildren = node.children && node.children.length > 0;
  const isEmpty = !hasChildren;
  
  const userClassName = node.style?.className || '';
  
  // Check if user specified width/height
  const userHasWidth = hasWidthClass(userClassName);
  const userHasHeight = hasHeightClass(userClassName);
  
  // Build className: chỉ apply default khi user chưa specify
  const frameClassName = cn(
    // Default width nếu user chưa specify
    isEmpty && !userHasWidth && "w-full",
    // Default height nếu user chưa specify  
    isEmpty && !userHasHeight && "min-h-24",
    // User className luôn được apply
    userClassName
  );
  
  // Direction calculation removed - using simplified horizontal indicators
  
  // Style cho frame (không cần scale nữa, dùng overflow clipping)
  const frameStyle = node.style?.css;
  
  return (
    <div 
      ref={ref}
      className={frameClassName}
      style={frameStyle}
      {...htmlProps}
    >
      {node.children?.map((child) => (
        <NodeRenderer 
          key={child.id} 
          node={child}
        />
      ))}
    </div>
  );
});

FrameNodeComponent.displayName = 'FrameNode';

// Re-enable memoization for FrameNode - uses container comparison for children
export const FrameNode = React.memo(FrameNodeComponent, compareContainerNodeProps);