import React, { useEffect } from 'react';
import { BuilderHeader } from './BuilderHeader';
import { BuilderSidebar } from './BuilderSidebar';
import { BuilderCanvas } from './BuilderCanvas';
import { PropertiesPanel } from './PropertiesPanel';
import { ResizeHandle } from '@/components/ui/resize-handle';
import { useBuilderCombined } from '../../context/BuilderContext';
import { useResizablePanels } from '../../hooks/useResizablePanels';
import { PANEL_CONFIG, LAYOUT_DIMENSIONS, LAYOUT_STYLES } from '../../config/layout';

interface BuilderLayoutProps {
  children?: React.ReactNode;
}

export const BuilderLayout: React.FC<BuilderLayoutProps> = ({ children }) => {
  const { uiState, getSelectedNode, copyNode, pasteNode, isUIReady } = useBuilderCombined();
  
  // Configure resizable panels with stable config
  const { leftWidth, rightWidth, resizeLeftPanel, resizeRightPanel } = useResizablePanels(PANEL_CONFIG);
  
  // Create consistent style objects
  const panelPositioning = {
    top: `${LAYOUT_DIMENSIONS.headerHeight}px`,
    bottom: '0',
  };

  const leftPanelStyle = {
    ...panelPositioning,
    width: `${leftWidth}px`,
  };

  const rightPanelStyle = {
    ...panelPositioning,
    width: `${rightWidth}px`,
  };

  const leftHandleStyle = {
    ...panelPositioning,
    left: `${leftWidth}px`,
    width: `${LAYOUT_DIMENSIONS.resizeHandleWidth}px`,
  };

  const rightHandleStyle = {
    ...panelPositioning,
    right: `${rightWidth}px`,
    width: `${LAYOUT_DIMENSIONS.resizeHandleWidth}px`,
  };

  // Keyboard shortcuts for copy/paste
  useEffect(() => {
    const editorMode = uiState?.editorMode || 'edit';
    
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle keyboard shortcuts in edit mode
      if (editorMode !== 'edit') {
        return;
      }
      
      // Don't handle shortcuts if user is typing in an input/textarea
      const target = e.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        return;
      }
      
      // Check for Ctrl+C (Copy)
      if ((e.ctrlKey || e.metaKey) && e.key === 'c' && !e.shiftKey && !e.altKey) {
        const selectedNode = getSelectedNode();
        
        if (selectedNode && selectedNode.id !== 'root') {
          e.preventDefault();
          e.stopPropagation();
          copyNode(selectedNode.id);
        }
      }
      
      // Check for Ctrl+V (Paste)
      if ((e.ctrlKey || e.metaKey) && e.key === 'v' && !e.shiftKey && !e.altKey) {
        e.preventDefault();
        e.stopPropagation();
        pasteNode();
      }
    };
    
    // Use capture phase to handle events before other handlers
    window.addEventListener('keydown', handleKeyDown, true);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [uiState?.editorMode, getSelectedNode, copyNode, pasteNode]);

  return (
    <div className="h-screen w-full flex flex-col bg-gray-50">
      {/* Header */}
      <BuilderHeader className="h-14 border-b bg-white" />
      
      {/* Main Content */}
      <div className="flex-1 relative overflow-hidden">
        {/* Canvas - Always full width/height */}
        <div className="absolute inset-0" style={{ zIndex: LAYOUT_DIMENSIONS.zIndex.canvas }}>
          <BuilderCanvas className="w-full h-full">
            {children}
          </BuilderCanvas>
        </div>
        
        {/* Left Sidebar - Fixed Overlay */}
        {uiState?.showTreePanel && isUIReady && (
          <>
            <BuilderSidebar 
              className={`fixed left-0 ${LAYOUT_STYLES.panel.background} ${LAYOUT_STYLES.panel.shadow} ${LAYOUT_STYLES.panel.border.left}`}
              style={{ 
                ...leftPanelStyle,
                zIndex: LAYOUT_DIMENSIONS.zIndex.panels,
              }}
            />
            
            {/* Left Resize Handle */}
            <ResizeHandle 
              direction="vertical"
              onResize={resizeLeftPanel}
              className={`fixed ${LAYOUT_STYLES.resizeHandle.background} ${LAYOUT_STYLES.resizeHandle.backgroundHover} ${LAYOUT_STYLES.resizeHandle.cursor}`}
              style={{
                ...leftHandleStyle,
                zIndex: LAYOUT_DIMENSIONS.zIndex.resizeHandles,
              }}
            />
          </>
        )}
        
        {/* Right Panel - Fixed Overlay */}
        {uiState?.showPropertiesPanel && isUIReady && (
          <>
            {/* Right Resize Handle */}
            <ResizeHandle 
              direction="vertical"
              onResize={resizeRightPanel}
              className={`fixed ${LAYOUT_STYLES.resizeHandle.background} ${LAYOUT_STYLES.resizeHandle.backgroundHover} ${LAYOUT_STYLES.resizeHandle.cursor}`}
              style={{
                ...rightHandleStyle,
                zIndex: LAYOUT_DIMENSIONS.zIndex.resizeHandles,
              }}
            />
            
            <PropertiesPanel 
              className={`fixed right-0 ${LAYOUT_STYLES.panel.background} ${LAYOUT_STYLES.panel.shadow} ${LAYOUT_STYLES.panel.border.right}`}
              style={{ 
                ...rightPanelStyle,
                zIndex: LAYOUT_DIMENSIONS.zIndex.panels,
              }}
            />
          </>
        )}
      </div>
    </div>
  );
};