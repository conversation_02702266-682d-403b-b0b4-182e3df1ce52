// import { TreeNode } from "@/features/builder/basic/states/types";

// Temporary type to replace TreeNode
type TreeNode = {
  type: string;
  children: TreeNode[];
  [key: string]: unknown;
};

export type PostStatus =
  | "TRASH"
  | "PUBLISHED"
  | "UNPUBLISHED"
  | "DRAFT"
  | "REVIEW"
  | "REJECTED";
export type PostType = "ARTICLE" | "PAGE";

interface Excerpt {
  image: string;
  description: string;
  files: string[];
}

export interface Post {
  id: number;
  createdAt: number;
  updatedAt: number;
  publishedAt: number;

  title: string;
  slug: string;
  content: TreeNode;
  excerpt: Excerpt;

  postType: PostType;
  status: PostStatus;
  authorId: string | null;
  parentId: number | null;
}

export interface CreatePost {
  title: string;
  slug: string;
  excerpt: Excerpt | null;

  postType: PostType;
  authorId: string | null;
}
