/**
 * Configurable constants for the Builder system
 * Adjust these values for testing and optimization
 */

// History Management
export const HISTORY_CONFIG = {
  MAX_ACTIONS: 100,              // Maximum number of actions to keep in history
  SNAPSHOT_INTERVAL: 20,         // Create snapshot every N actions
  AUTO_SAVE_INTERVAL: 2000,      // Auto-save to localStorage every N milliseconds (2 seconds for testing)
  TEXT_GROUPING_DELAY: 500,      // Group text changes within N milliseconds
} as const;

// Storage
export const STORAGE_CONFIG = {
  CURRENT_VERSION: 2,            // Current storage schema version (v2: direct property values)
  KEY_PREFIX: 'builder_',        // Prefix for all localStorage keys
  KEYS: {
    POST: 'builder_post',
    UI_STATE: 'builder_ui_state', 
    TEMP_UI_STATE: 'builder_temp_ui_state',
    HISTORY: 'builder_history',
    LAST_SAVE: 'builder_last_save',
  },
} as const;

// UI Defaults
export const UI_DEFAULTS = {
  VIEW_MODE: 'desktop' as const,
  EDITOR_MODE: 'edit' as const,
  EXPANDED_ROOT: true,           // Always expand root node by default
} as const;

// UI State Persistence
export const UI_PERSISTENCE_CONFIG = {
  // Auto-save triggers for different UI state types
  IMMEDIATE_SAVE: ['selectedNodeId', 'viewMode'],           // Save immediately
  DEBOUNCED_SAVE: ['expandedNodeIds'],                      // Save after delay
  ACTION_SAVE: ['showTreePanel', 'showPropertiesPanel'],   // Save on user action
  SESSION_SAVE: ['propertiesPanelTab', 'isAdvancedMode'],  // Save for session
  
  // Timing
  DEBOUNCE_DELAY: 500,           // Delay for debounced saves (ms)
  SESSION_SAVE_INTERVAL: 1000,   // Save session state every N ms
  
  // UI state that should be tracked in history
  TRACKABLE_UI_CHANGES: [
    'selectedNodeId', 
    'viewMode', 
    'showTreePanel',
    'propertiesPanelMainTab',
    'propertiesPanelSubTab',
    'propertiesPanelDisplaySection'
  ],
} as const;

// Node Defaults
export const NODE_DEFAULTS = {
  TEXT_PLACEHOLDER: 'Enter text here...',
  FRAME_MIN_HEIGHT: 'min-h-[100px]',
  ROOT_CLASSES: 'w-full min-h-screen',
} as const;