import { useCallback, useRef, useState, useEffect } from 'react';
import { useBuilderCombined } from '../context/BuilderContext';
import { TreeNode } from '../context/types';
import { DropPosition } from '../types/drag-drop';
import { nodeValidation } from '../context/validation';
import { findParentNode } from '../utils/tree';
import { calculateSmartDropPosition, SmartDropPosition } from '../utils/drop-position';
import { calculateMoveIndex } from '../utils/move-position';
import { DRAG_DROP_CONFIG } from '../config/drag-drop';

interface DragState {
  isDragging: boolean;
  draggedNodeId: string | null;
  dragStartPosition: { x: number; y: number } | null;
  currentPosition: { x: number; y: number };
}

interface DropTarget {
  nodeId: string;
  position: DropPosition;
  element: HTMLElement;
}

interface DragPreview {
  visible: boolean;
  position: { x: number; y: number };
  node: TreeNode | null;
}

interface DropIndicator {
  targetId: string;
  position: DropPosition;
  visualPosition?: SmartDropPosition['visualPosition'];
}

export function useCanvasDragDrop() {
  const { post, moveNode, selectNode, findNodeById } = useBuilderCombined();
  
  // Drag state
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedNodeId: null,
    dragStartPosition: null,
    currentPosition: { x: 0, y: 0 }
  });
  
  // Preview state
  const [dragPreview, setDragPreview] = useState<DragPreview>({
    visible: false,
    position: { x: 0, y: 0 },
    node: null
  });
  
  // Drop indicator state
  const [dropIndicator, setDropIndicator] = useState<DropIndicator | null>(null);
  
  // Refs for performance
  const dragStarted = useRef(false);
  const lastThrottleTime = useRef(0);
  const currentDropTarget = useRef<DropTarget | null>(null);
  
  // Find valid drop target
  const findDropTarget = useCallback((x: number, y: number): DropTarget | null => {
    if (!dragState.draggedNodeId || !post) return null;
    
    // Hide dragged element temporarily to find element underneath
    const draggedElement = document.querySelector(`[data-node-id="${dragState.draggedNodeId}"]`) as HTMLElement;
    if (draggedElement) {
      draggedElement.style.pointerEvents = 'none';
    }
    
    const elementAtPoint = document.elementFromPoint(x, y);
    
    // Restore dragged element
    if (draggedElement) {
      draggedElement.style.pointerEvents = '';
    }
    
    // Find node element
    const nodeElement = elementAtPoint?.closest('[data-node-id]') as HTMLElement;
    if (!nodeElement) return null;
    
    const targetNodeId = nodeElement.dataset.nodeId;
    if (!targetNodeId || targetNodeId === dragState.draggedNodeId) return null;
    
    // Get nodes
    const draggedNode = findNodeById(dragState.draggedNodeId);
    const targetNode = findNodeById(targetNodeId);
    if (!draggedNode || !targetNode) return null;
    
    // Check if target is descendant of dragged node
    const isDescendant = (nodeId: string, ancestorId: string): boolean => {
      const parent = findParentNode(post.content, nodeId);
      if (!parent) return false;
      if (parent.id === ancestorId) return true;
      return isDescendant(parent.id, ancestorId);
    };
    
    if (isDescendant(targetNodeId, dragState.draggedNodeId)) return null;
    
    // Get parent for smart positioning
    const targetParent = findParentNode(post.content, targetNodeId);
    
    // Calculate smart drop position
    const smartPosition = calculateSmartDropPosition(
      targetNode,
      targetParent,
      x,
      y,
      nodeElement
    );
    
    if (!smartPosition) return null;
    
    // Validate the move
    if (smartPosition.position === 'inside') {
      if (!nodeValidation.canMove(draggedNode, targetNode)) return null;
    } else {
      if (!targetParent || !nodeValidation.canMove(draggedNode, targetParent)) return null;
    }
    
    return {
      nodeId: targetNodeId,
      position: smartPosition.position,
      element: nodeElement,
      visualPosition: smartPosition.visualPosition
    } as DropTarget & { visualPosition?: SmartDropPosition['visualPosition'] };
  }, [dragState.draggedNodeId, post, findNodeById]);
  
  // Handle drag start
  const handleDragStart = useCallback((e: React.MouseEvent, nodeId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    const node = findNodeById(nodeId);
    if (!node) return;
    
    setDragState({
      isDragging: false,
      draggedNodeId: nodeId,
      dragStartPosition: { x: e.clientX, y: e.clientY },
      currentPosition: { x: e.clientX, y: e.clientY }
    });
    
    dragStarted.current = false;
  }, [findNodeById]);
  
  // Handle drag move (throttled)
  const handleDragMove = useCallback((e: MouseEvent) => {
    if (!dragState.draggedNodeId || !dragState.dragStartPosition) return;
    
    const now = Date.now();
    const timeSinceLastUpdate = now - lastThrottleTime.current;
    
    // Update current position always (for smooth preview movement)
    setDragState(prev => ({
      ...prev,
      currentPosition: { x: e.clientX, y: e.clientY }
    }));
    
    // Check if we should start dragging
    if (!dragStarted.current) {
      const deltaX = Math.abs(e.clientX - dragState.dragStartPosition.x);
      const deltaY = Math.abs(e.clientY - dragState.dragStartPosition.y);
      
      if (deltaX > DRAG_DROP_CONFIG.DRAG_THRESHOLD || deltaY > DRAG_DROP_CONFIG.DRAG_THRESHOLD) {
        dragStarted.current = true;
        
        const node = findNodeById(dragState.draggedNodeId);
        if (!node) return;
        
        setDragState(prev => ({ ...prev, isDragging: true }));
        setDragPreview({
          visible: true,
          position: { x: e.clientX, y: e.clientY },
          node
        });
        
        // Clear selection
        selectNode(null);
        
        // Set cursor
        document.body.style.cursor = 'grabbing';
      }
    }
    
    // Update preview position
    if (dragStarted.current) {
      setDragPreview(prev => ({
        ...prev,
        position: { x: e.clientX, y: e.clientY }
      }));
      
      // Throttle drop target calculation
      if (timeSinceLastUpdate > DRAG_DROP_CONFIG.THROTTLE_DELAY) {
        lastThrottleTime.current = now;
        
        const dropTarget = findDropTarget(e.clientX, e.clientY);
        currentDropTarget.current = dropTarget;
        
        if (dropTarget) {
          setDropIndicator({
            targetId: dropTarget.nodeId,
            position: dropTarget.position,
            visualPosition: (dropTarget as { visualPosition?: 'horizontal' | 'vertical' | 'center' }).visualPosition
          });
        } else {
          setDropIndicator(null);
        }
      }
    }
  }, [dragState, findNodeById, findDropTarget, selectNode]);
  
  // Handle drag end
  const handleDragEnd = useCallback(() => {
    if (dragStarted.current && dragState.draggedNodeId && currentDropTarget.current) {
      const { nodeId: targetId, position } = currentDropTarget.current;
      
      // Calculate actual parent and index for the move
      let newParentId: string;
      let newIndex: number;
      
      if (position === 'inside') {
        newParentId = targetId;
        newIndex = 0;
      } else {
        const targetParent = findParentNode(post!.content, targetId);
        if (!targetParent) return;
        
        newParentId = targetParent.id;
        
        // Use utility to calculate correct index
        newIndex = calculateMoveIndex(
          dragState.draggedNodeId,
          targetId,
          position,
          targetParent
        );
      }
      
      // Execute the move
      moveNode(dragState.draggedNodeId, newParentId, newIndex);
      
      // Restore selection to the moved node
      selectNode(dragState.draggedNodeId);
    }
    
    // Reset everything
    document.body.style.cursor = '';
    setDragState({
      isDragging: false,
      draggedNodeId: null,
      dragStartPosition: null,
      currentPosition: { x: 0, y: 0 }
    });
    setDragPreview({
      visible: false,
      position: { x: 0, y: 0 },
      node: null
    });
    setDropIndicator(null);
    dragStarted.current = false;
    currentDropTarget.current = null;
  }, [dragState.draggedNodeId, post, moveNode]);
  
  // Global event listeners
  useEffect(() => {
    if (dragState.draggedNodeId) {
      const handleGlobalMove = (e: MouseEvent) => handleDragMove(e);
      const handleGlobalUp = () => handleDragEnd();
      
      document.addEventListener('mousemove', handleGlobalMove);
      document.addEventListener('mouseup', handleGlobalUp);
      
      return () => {
        document.removeEventListener('mousemove', handleGlobalMove);
        document.removeEventListener('mouseup', handleGlobalUp);
      };
    }
  }, [dragState.draggedNodeId, handleDragMove, handleDragEnd]);
  
  return {
    // State
    isDragging: dragState.isDragging,
    draggedNodeId: dragState.draggedNodeId,
    dragPreview,
    dropIndicator,
    
    // Handlers
    handleDragStart,
    
    // Utilities
    getDraggedNode: () => dragPreview.node
  };
}