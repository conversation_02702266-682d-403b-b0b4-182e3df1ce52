const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

console.log("🔍 ANALYZING BUILDER BUNDLE...\n");

// Analyze bundle size by category
const categories = {
  "Input Components": "src/features/builder/components/forms/inputs",
  "Section Components": "src/features/builder/components/forms/sections",
  Utilities: "src/features/builder/components/forms/utils",
  Context: "src/features/builder/context",
  Nodes: "src/features/builder/components/nodes",
};

function getDirectorySize(dirPath) {
  let totalSize = 0;
  try {
    const files = fs.readdirSync(dirPath, { recursive: true });
    files.forEach((file) => {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      if (
        stat.isFile() &&
        (filePath.endsWith(".ts") || filePath.endsWith(".tsx"))
      ) {
        totalSize += stat.size;
      }
    });
  } catch (e) {
    console.warn(`Warning: Could not analyze ${dirPath}`);
  }
  return totalSize;
}

function formatSize(bytes) {
  return (bytes / 1024).toFixed(1) + " KB";
}

console.log("📊 BUNDLE SIZE BREAKDOWN:\n");

let totalSize = 0;
for (const [name, dir] of Object.entries(categories)) {
  const size = getDirectorySize(dir);
  totalSize += size;
  console.log(`${name.padEnd(20)} ${formatSize(size)}`);
}

console.log(`${"─".repeat(30)}`);
console.log(`${"TOTAL SOURCE".padEnd(20)} ${formatSize(totalSize)}\n`);

// Check for optimization opportunities
console.log("💡 OPTIMIZATION OPPORTUNITIES:\n");

// Large files (>10KB)
console.log("📄 Large Files (>10KB):");
function findLargeFiles(dir, results = []) {
  try {
    const files = fs.readdirSync(dir);
    files.forEach((file) => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      if (stat.isDirectory()) {
        findLargeFiles(filePath, results);
      } else if (stat.isFile() && stat.size > 10240) {
        results.push({
          path: filePath.replace(process.cwd(), ""),
          size: stat.size,
        });
      }
    });
  } catch (e) {}
  return results;
}

const largeFiles = findLargeFiles("src/features/builder");
largeFiles.sort((a, b) => b.size - a.size);
largeFiles.slice(0, 10).forEach((file) => {
  console.log(`  ${file.path} (${formatSize(file.size)})`);
});

console.log("\n🚀 RECOMMENDATIONS:");
console.log("1. Lazy load sections > 15KB");
console.log("2. Split large utility files");
console.log("3. Optimize imports with tree-shaking");
console.log("4. Consider virtual scrolling for large lists");
console.log("5. Implement component-level code splitting");
