import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

/**
 * Props interface for StatCard component
 */
export interface StatCardProps {
  /** Statistical number to display */
  number: string;
  /** Name/title of the statistic */
  name: string;
  /** Description of the statistic */
  description: string;
  /** Additional CSS classes */
  className?: string;
  /** Click handler for the card */
  onClick?: () => void;
}

/**
 * StatCard component displays statistical information with proper typography hierarchy
 * 
 * @param props - Component props
 * @returns JSX element for stat card
 */
export const StatCard: React.FC<StatCardProps> = ({
  number,
  name,
  description,
  className,
  onClick,
}) => {
  return (
    <Card
      className={cn(
        "hover:shadow-lg transition-all duration-300 cursor-pointer bg-transparent border-[#BAE4FD] max-w-[200px]",
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-6 text-center">
        <div className="space-y-2">
          {/* Statistical Number */}
          <div className="text-[64px] font-bold text-[#0177BD] truncate overflow-hidden whitespace-nowrap">
            {number}
          </div>

          {/* Name/Title */}
          <div className="space-y-1.5">
            <h3 className="text-[20px] font-semibold text-gray-900">
              {name}
            </h3>

            {/* Description */}
            <p className="text-sm text-gray-600 leading-relaxed">
              {description}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
