/// <reference types="vitest" />
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import basicSsl from "@vitejs/plugin-basic-ssl";
import path from "path";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isLibrary = mode === "library";
  const isBuilder = mode === "builder";

  return {
    plugins: [react(), tailwindcss(), basicSsl()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    test: {
      globals: true,
      environment: "jsdom",
      setupFiles: "./src/test/setup.ts",
      css: true,
    },
    // Library build configuration
    ...(isLibrary && {
      build: {
        lib: {
          entry: path.resolve(__dirname, "src/index.ts"),
          name: "KhaFEComponents",
          formats: ["es", "umd"],
          fileName: (format) => `index.${format}.js`,
        },
        rollupOptions: {
          external: ["react", "react-dom"],
          output: {
            globals: {
              react: "React",
              "react-dom": "ReactDOM",
            },
          },
        },
      },
    }),
    // Builder package build configuration
    ...(isBuilder && {
      build: {
        lib: {
          entry: path.resolve(__dirname, "src/builder-entry.ts"),
          name: "KhaBuilder",
          formats: ["es", "umd"],
          fileName: (format) => `builder.${format}.js`,
        },
        rollupOptions: {
          external: ["react", "react-dom", "react-router-dom", "lucide-react"],
          output: {
            globals: {
              react: "React",
              "react-dom": "ReactDOM",
              "react-router-dom": "ReactRouterDOM",
              "lucide-react": "LucideReact",
            } as Record<string, string>,
          },
        },
      },
    }),
  };
});
