import { createSelector } from "@reduxjs/toolkit";
import type { RootState } from "@/store/rootReducer";
import type { PortalLinkState } from "./type";

// Base selectors
export const selectPortalLinkState = (state: RootState): PortalLinkState =>
  state.portalLink;

// Data selectors
export const selectPortalLinkData = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.data
);

export const selectPortalLinkSavedData = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.savedData
);

export const selectPortalLinkLoading = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.loading
);

export const selectPortalLinkSaving = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.saving
);

export const selectPortalLinkError = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.error
);

export const selectPortalLinkIsDirty = createSelector(
  [selectPortalLinkState],
  (portalLinkState) => portalLinkState.isDirty
);

// Utility selectors
export const selectPortalLinkIsOperating = createSelector(
  [selectPortalLinkLoading, selectPortalLinkSaving],
  (loading, saving) => loading || saving
);
