import React, { useState, useEffect, forwardRef } from "react";
import { cn } from "@/lib/utils";
import { NodeRendererProps } from "../NodeFactory";
import { CloudAlert, Loader2 } from "lucide-react";
import { compareNodeWithProperties } from '@/lib/memo-utils';

const ImageNodeComponent = forwardRef<
  HTMLImageElement | HTMLDivElement,
  NodeRendererProps & React.HTMLAttributes<HTMLImageElement>
>(({ 
  node,
  ...htmlProps
}, ref) => {
  const src = node.properties?.src as string;
  const alt = (node.properties?.alt as string) || node.label;
  
  const userClassName = node.style?.className || '';
  
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (!src) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setHasError(false);

    const img = new Image();
    img.src = src;

    if (img.complete) {
      setIsLoading(false);
      return;
    }

    img.onload = () => {
      setIsLoading(false);
    };

    img.onerror = () => {
      setHasError(true);
      setIsLoading(false);
    };
  }, [src]);

  // For loading and error states, we need a container
  if (isLoading || hasError) {
    const placeholder = (
      <div 
        ref={ref as React.Ref<HTMLDivElement>}
        className={cn(
          "inline-flex justify-center items-center bg-gray-100 w-48 h-32",
          userClassName
        )}
        style={node.style?.css}
        {...htmlProps}
      >
        {isLoading && (
          <Loader2 className="w-6 h-6 gpu-spinner text-gray-400" />
        )}
        {hasError && (
          <div className="text-center p-2">
            <CloudAlert className="w-6 h-6 text-gray-400 mx-auto mb-1" />
            <span className="text-xs text-gray-500">
              {src ? "Không thể tải ảnh" : "Chưa có ảnh"}
            </span>
          </div>
        )}
      </div>
    );
    return placeholder;
  }

  // For successful image load, return img directly without wrapper
  return (
    <img
      ref={ref as React.Ref<HTMLImageElement>}
      src={src || undefined}
      alt={alt ?? "Image"}
      className={cn(
        userClassName,
        node.style?.variants?.image
      )}
      style={node.style?.css}
      loading="lazy"
      decoding="async"
      {...htmlProps}
    />
  );
});

// Re-enable memoization for ImageNode - safe for form editing
export const ImageNode = React.memo(ImageNodeComponent, compareNodeWithProperties);

