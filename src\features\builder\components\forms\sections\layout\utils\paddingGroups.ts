// Padding value groups organized by Tailwind CSS spacing scale

export interface PaddingOption {
  value: string;
  label: string;
  description?: string;
}

export interface PaddingGroup {
  id: string;
  label: string;
  options: PaddingOption[];
}


// Fixed padding values - using Tailwind spacing scale
const PADDING_FIXED_VALUES = [
  { value: 'p-0', label: '0', description: '0px' },
  { value: 'p-0.5', label: '0.5', description: '2px' },
  { value: 'p-1', label: '1', description: '4px' },
  { value: 'p-1.5', label: '1.5', description: '6px' },
  { value: 'p-2', label: '2', description: '8px' },
  { value: 'p-2.5', label: '2.5', description: '10px' },
  { value: 'p-3', label: '3', description: '12px' },
  { value: 'p-3.5', label: '3.5', description: '14px' },
  { value: 'p-4', label: '4', description: '16px' },
  { value: 'p-5', label: '5', description: '20px' },
  { value: 'p-6', label: '6', description: '24px' },
  { value: 'p-7', label: '7', description: '28px' },
  { value: 'p-8', label: '8', description: '32px' },
  { value: 'p-9', label: '9', description: '36px' },
  { value: 'p-10', label: '10', description: '40px' },
  { value: 'p-11', label: '11', description: '44px' },
  { value: 'p-12', label: '12', description: '48px' },
  { value: 'p-14', label: '14', description: '56px' },
  { value: 'p-16', label: '16', description: '64px' },
  { value: 'p-20', label: '20', description: '80px' },
  { value: 'p-24', label: '24', description: '96px' },
  { value: 'p-28', label: '28', description: '112px' },
  { value: 'p-32', label: '32', description: '128px' },
  { value: 'p-36', label: '36', description: '144px' },
  { value: 'p-40', label: '40', description: '160px' },
  { value: 'p-44', label: '44', description: '176px' },
  { value: 'p-48', label: '48', description: '192px' },
  { value: 'p-52', label: '52', description: '208px' },
  { value: 'p-56', label: '56', description: '224px' },
  { value: 'p-60', label: '60', description: '240px' },
  { value: 'p-64', label: '64', description: '256px' },
  { value: 'p-72', label: '72', description: '288px' },
  { value: 'p-80', label: '80', description: '320px' },
  { value: 'p-96', label: '96', description: '384px' },
];

// Generate values for different padding types
const generatePaddingValues = (prefix: string, specialIncluded = true) => {
  const special = specialIncluded ? [
    { value: 'none', label: 'Không chọn', description: 'Không có padding' },
    { value: `${prefix}-px`, label: 'px', description: '1px' },
  ] : [
    { value: 'none', label: 'Không chọn', description: 'Không có padding' },
  ];

  const fixed = PADDING_FIXED_VALUES.map(opt => ({
    value: opt.value.replace('p-', `${prefix}-`),
    label: opt.label,
    description: opt.description
  }));

  return [...special, ...fixed];
};

// All padding groups - include "none" option in fixed group
const PADDING_ALL_FIXED_VALUES = [
  { value: 'none', label: 'Không chọn', description: 'Không có padding' },
  ...PADDING_FIXED_VALUES
];

// Padding groups for unified padding (p-*) - single group only
export const PADDING_GROUPS: PaddingGroup[] = [
  {
    id: 'fixed',
    label: 'Lề trong',
    options: PADDING_ALL_FIXED_VALUES
  }
];

// Padding groups for X axis (px-*)
export const PADDING_X_GROUPS: PaddingGroup[] = [
  {
    id: 'fixed',
    label: 'Lề trong',
    options: generatePaddingValues('px', false)
  }
];

// Padding groups for Y axis (py-*)
export const PADDING_Y_GROUPS: PaddingGroup[] = [
  {
    id: 'fixed',
    label: 'Lề trong',
    options: generatePaddingValues('py', false)
  }
];

// Padding groups for individual sides
export const PADDING_T_GROUPS: PaddingGroup[] = [
  {
    id: 'fixed',
    label: 'Lề trong',
    options: generatePaddingValues('pt', false)
  }
];

export const PADDING_R_GROUPS: PaddingGroup[] = [
  {
    id: 'fixed',
    label: 'Lề trong',
    options: generatePaddingValues('pr', false)
  }
];

export const PADDING_B_GROUPS: PaddingGroup[] = [
  {
    id: 'fixed',
    label: 'Lề trong',
    options: generatePaddingValues('pb', false)
  }
];

export const PADDING_L_GROUPS: PaddingGroup[] = [
  {
    id: 'fixed',
    label: 'Lề trong',
    options: generatePaddingValues('pl', false)
  }
];

// Padding groups for start/end (logical directions)
export const PADDING_S_GROUPS: PaddingGroup[] = [
  {
    id: 'fixed',
    label: 'Lề trong',
    options: generatePaddingValues('ps', false)
  }
];

export const PADDING_E_GROUPS: PaddingGroup[] = [
  {
    id: 'fixed',
    label: 'Lề trong',
    options: generatePaddingValues('pe', false)
  }
];