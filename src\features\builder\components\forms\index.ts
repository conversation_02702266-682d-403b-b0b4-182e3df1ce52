// =============================================================================
// UNIFIED FORMS SYSTEM - CLEAN ARCHITECTURE
// =============================================================================

// Form Sections (Business Logic)
export * from "./sections";

// Form Inputs (UI Components)
export * from "./inputs";

// Form Layouts (UI Orchestration)
export * from "./layouts";

// Form Registry (Component Factories)
export * from "./registry";

// Form Types (TypeScript Definitions)
export * from "./types";

// Form Utilities (Shared Logic)
export * from "./utils";

// UI Components (Reusable UI)
export * from "./ui";

// Editors (Specialized Input Components)
export * from "./editors";

// Legacy exports (for backward compatibility)
export { renderDisplayForm } from "./registry/DisplayFormFactory";
export { ContentForm } from "./layouts/ContentForm";
export { AdvancedDisplayForm } from "./layouts/AdvancedDisplayForm";

// Explicit re-exports to resolve conflicts
export type {
  DisplayFormComponent,
  DisplayFormProps,
} from "./registry/DisplayFormFactory";
