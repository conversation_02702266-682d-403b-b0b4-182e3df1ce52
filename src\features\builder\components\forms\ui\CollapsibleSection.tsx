import React from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CollapsibleSectionProps {
  title: string;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  disabled?: boolean;
}

export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  isOpen,
  onToggle,
  children,
  disabled
}) => {
  return (
    <div className={cn(
      "rounded-lg overflow-hidden transition-all",
      isOpen 
        ? "border border-blue-200 shadow-sm" 
        : "border border-gray-200"
    )}>
      {/* Section Header */}
      <button
        onClick={onToggle}
        disabled={disabled}
        className={cn(
          "w-full flex items-center justify-between p-3 text-left transition-colors",
          isOpen 
            ? "bg-blue-50 text-blue-700 border-b border-blue-100 hover:bg-blue-100 focus:bg-blue-100" 
            : "hover:bg-gray-50 focus:outline-none focus:bg-gray-50",
          disabled && "opacity-50 cursor-not-allowed hover:bg-white focus:bg-white"
        )}
      >
        <span className={cn(
          "text-sm font-semibold",
          isOpen ? "text-blue-700" : "text-gray-700"
        )}>{title}</span>
        <div className="flex items-center">
          {isOpen ? (
            <ChevronDown className={cn(
              "h-4 w-4",
              isOpen ? "text-blue-600" : "text-gray-500"
            )} />
          ) : (
            <ChevronRight className={cn(
              "h-4 w-4", 
              isOpen ? "text-blue-600" : "text-gray-500"
            )} />
          )}
        </div>
      </button>
      
      {/* Section Content */}
      {isOpen && (
        <div className="border-t border-gray-200 p-4 bg-white">
          {children}
        </div>
      )}
    </div>
  );
};