/**
 * Simple LRU cache implementation for expensive operations
 */
class LRUCache<K, V> {
  private cache = new Map<K, V>();
  private readonly maxSize: number;

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize;
  }

  get(key: K): V | undefined {
    const value = this.cache.get(key);
    if (value !== undefined) {
      // Move to end (most recently used)
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return undefined;
  }

  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      // Update existing
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      // Remove least recently used (first item)
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(key, value);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

// Global cache instances for different types of parsing
export const appearanceParseCache = new LRUCache<
  string,
  Record<string, unknown> | object
>(200);
export const borderParseCache = new LRUCache<
  string,
  Record<string, unknown> | object
>(200);
export const classNameCleanCache = new LRUCache<string, string>(500);

/**
 * Memoized function wrapper
 */
export function memoize<Args extends readonly unknown[], Return>(
  fn: (...args: Args) => Return,
  cache: LRUCache<string, Return>,
  keyFn?: (...args: Args) => string
): (...args: Args) => Return {
  return (...args: Args): Return => {
    const key = keyFn ? keyFn(...args) : JSON.stringify(args);

    let result = cache.get(key);
    if (result === undefined) {
      result = fn(...args);
      cache.set(key, result);
    }

    return result;
  };
}

/**
 * Clear all caches (useful for testing or memory management)
 */
export function clearAllCaches(): void {
  appearanceParseCache.clear();
  borderParseCache.clear();
  classNameCleanCache.clear();
}
