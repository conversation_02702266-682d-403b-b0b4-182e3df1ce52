// Core Types (Re-export from appropriate sources)
export type {
  // TreeNode types
  TreeN<PERSON>,
  TreeNodeType,
  TreeNodeGroup,
  TreeNodeProperty,

  // Post types
  Post,

  // UI State types
  BuilderUIState,
  BuilderState,
  ViewMode,
  EditorMode,
  PersistentUIState,
  SessionUIState,
  TemporaryUIState,

  // History types
  HistoryAction,
  HistoryState,

  // Storage types
  StorageSchema,
  StorageError,
  RecoveryOptions,
} from "../context/types";

// Base data types
export type {
  Post as BasePost,
  PostStatus,
  PostType,
  Excerpt,
} from "../states/posts";

// Configuration
export * from "../config/constants";
