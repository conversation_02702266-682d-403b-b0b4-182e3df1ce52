export interface HotlineItem {
  name: string;
  phone: string;
  email: string;
}

export interface HotlineConfig {
  hotlines: HotlineItem[];
}

export interface HotlineState {
  data: HotlineConfig | null;
  savedData: HotlineConfig | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
  isDirty: boolean;
}

export interface HotlineField {
  key: string;
  label: string;
  type: "text" | "email" | "url" | "textarea" | "boolean" | "array";
  required?: boolean;
  placeholder?: string;
}

export interface HotlineMetadata {
  title: string;
  description: string;
  type: "array" | "object";
  arrayKey?: string;
  fields: HotlineField[];
}

export interface EditableHotlineItem {
  id: string;
  isNew?: boolean;
  data: Record<string, string>;
}

// Props for hotline table component
export interface HotlineTableProps {
  metadata: HotlineMetadata;
  data: HotlineConfig | null;
  loading: boolean;
  onDataChange: (newData: HotlineConfig) => void;
  onValidationChange?: (isValid: boolean) => void;
}

// Props for hotline page layout component
export interface HotlinePageLayoutProps {
  title: string;
  description: string;
  children: React.ReactNode;
  loading?: boolean;
  error?: string | null;
  onSave?: () => void;
  onRefresh?: () => void;
  onPreview?: () => void;
  showPreview?: boolean;
  saveDisabled?: boolean;
  isDirty?: boolean;
  hasValidationErrors?: boolean;
}
