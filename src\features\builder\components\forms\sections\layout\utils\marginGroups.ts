// Margin value groups organized by Tailwind CSS spacing scale

export interface MarginOption {
  value: string;
  label: string;
  description?: string;
}

export interface MarginGroup {
  id: string;
  label: string;
  options: MarginOption[];
}

// Fixed margin values - using Tailwind spacing scale
const MARGIN_FIXED_VALUES = [
  { value: 'm-0', label: '0', description: '0px' },
  { value: 'm-0.5', label: '0.5', description: '2px' },
  { value: 'm-1', label: '1', description: '4px' },
  { value: 'm-1.5', label: '1.5', description: '6px' },
  { value: 'm-2', label: '2', description: '8px' },
  { value: 'm-2.5', label: '2.5', description: '10px' },
  { value: 'm-3', label: '3', description: '12px' },
  { value: 'm-3.5', label: '3.5', description: '14px' },
  { value: 'm-4', label: '4', description: '16px' },
  { value: 'm-5', label: '5', description: '20px' },
  { value: 'm-6', label: '6', description: '24px' },
  { value: 'm-7', label: '7', description: '28px' },
  { value: 'm-8', label: '8', description: '32px' },
  { value: 'm-9', label: '9', description: '36px' },
  { value: 'm-10', label: '10', description: '40px' },
  { value: 'm-11', label: '11', description: '44px' },
  { value: 'm-12', label: '12', description: '48px' },
  { value: 'm-14', label: '14', description: '56px' },
  { value: 'm-16', label: '16', description: '64px' },
  { value: 'm-20', label: '20', description: '80px' },
  { value: 'm-24', label: '24', description: '96px' },
  { value: 'm-28', label: '28', description: '112px' },
  { value: 'm-32', label: '32', description: '128px' },
  { value: 'm-36', label: '36', description: '144px' },
  { value: 'm-40', label: '40', description: '160px' },
  { value: 'm-44', label: '44', description: '176px' },
  { value: 'm-48', label: '48', description: '192px' },
  { value: 'm-52', label: '52', description: '208px' },
  { value: 'm-56', label: '56', description: '224px' },
  { value: 'm-60', label: '60', description: '240px' },
  { value: 'm-64', label: '64', description: '256px' },
  { value: 'm-72', label: '72', description: '288px' },
  { value: 'm-80', label: '80', description: '320px' },
  { value: 'm-96', label: '96', description: '384px' },
];

// Negative margin values
const MARGIN_NEGATIVE_VALUES = [
  { value: '-m-0.5', label: '-0.5', description: '-2px' },
  { value: '-m-1', label: '-1', description: '-4px' },
  { value: '-m-1.5', label: '-1.5', description: '-6px' },
  { value: '-m-2', label: '-2', description: '-8px' },
  { value: '-m-2.5', label: '-2.5', description: '-10px' },
  { value: '-m-3', label: '-3', description: '-12px' },
  { value: '-m-3.5', label: '-3.5', description: '-14px' },
  { value: '-m-4', label: '-4', description: '-16px' },
  { value: '-m-5', label: '-5', description: '-20px' },
  { value: '-m-6', label: '-6', description: '-24px' },
  { value: '-m-7', label: '-7', description: '-28px' },
  { value: '-m-8', label: '-8', description: '-32px' },
  { value: '-m-9', label: '-9', description: '-36px' },
  { value: '-m-10', label: '-10', description: '-40px' },
  { value: '-m-11', label: '-11', description: '-44px' },
  { value: '-m-12', label: '-12', description: '-48px' },
  { value: '-m-14', label: '-14', description: '-56px' },
  { value: '-m-16', label: '-16', description: '-64px' },
  { value: '-m-20', label: '-20', description: '-80px' },
  { value: '-m-24', label: '-24', description: '-96px' },
  { value: '-m-28', label: '-28', description: '-112px' },
  { value: '-m-32', label: '-32', description: '-128px' },
  { value: '-m-36', label: '-36', description: '-144px' },
  { value: '-m-40', label: '-40', description: '-160px' },
  { value: '-m-44', label: '-44', description: '-176px' },
  { value: '-m-48', label: '-48', description: '-192px' },
  { value: '-m-52', label: '-52', description: '-208px' },
  { value: '-m-56', label: '-56', description: '-224px' },
  { value: '-m-60', label: '-60', description: '-240px' },
  { value: '-m-64', label: '-64', description: '-256px' },
  { value: '-m-72', label: '-72', description: '-288px' },
  { value: '-m-80', label: '-80', description: '-320px' },
  { value: '-m-96', label: '-96', description: '-384px' },
];

// Generate values for different margin types
const generateMarginValues = (prefix: string, includeAuto = false, includeNegative = true) => {
  const none = [{ value: 'none', label: 'Không chọn', description: 'Không có margin' }];
  
  const fixed = MARGIN_FIXED_VALUES.map(opt => ({
    value: opt.value.replace('m-', `${prefix}-`),
    label: opt.label,
    description: opt.description
  }));

  const negative = includeNegative ? MARGIN_NEGATIVE_VALUES.map(opt => ({
    value: opt.value.replace('-m-', `-${prefix}-`),
    label: opt.label,
    description: opt.description
  })) : [];

  const auto = includeAuto ? [{ value: `${prefix}-auto`, label: 'Auto', description: 'Tự động' }] : [];

  return { none, fixed, negative, auto };
};

// Margin groups for unified margin (m-*) - with tabs
export const MARGIN_GROUPS: MarginGroup[] = [
  {
    id: 'fixed',
    label: 'Cố định',
    options: [
      { value: 'none', label: 'Không chọn', description: 'Không có margin' },
      ...MARGIN_FIXED_VALUES
    ]
  },
  {
    id: 'negative',
    label: 'Âm',
    options: MARGIN_NEGATIVE_VALUES
  },
  {
    id: 'auto',
    label: 'Tự động',
    options: [{ value: 'm-auto', label: 'Auto', description: 'Tự động' }]
  }
];

// Margin groups for X axis (mx-*)
const mxValues = generateMarginValues('mx', true, true);
export const MARGIN_X_GROUPS: MarginGroup[] = [
  {
    id: 'fixed',
    label: 'Cố định',
    options: [...mxValues.none, ...mxValues.fixed]
  },
  {
    id: 'negative',
    label: 'Âm',
    options: mxValues.negative
  },
  {
    id: 'auto',
    label: 'Tự động',
    options: mxValues.auto
  }
];

// Margin groups for Y axis (my-*)
const myValues = generateMarginValues('my', true, true);
export const MARGIN_Y_GROUPS: MarginGroup[] = [
  {
    id: 'fixed',
    label: 'Cố định',
    options: [...myValues.none, ...myValues.fixed]
  },
  {
    id: 'negative',
    label: 'Âm',
    options: myValues.negative
  },
  {
    id: 'auto',
    label: 'Tự động',
    options: myValues.auto
  }
];

// Margin groups for individual sides
const mtValues = generateMarginValues('mt', true, true);
export const MARGIN_T_GROUPS: MarginGroup[] = [
  {
    id: 'fixed',
    label: 'Cố định',
    options: [...mtValues.none, ...mtValues.fixed]
  },
  {
    id: 'negative',
    label: 'Âm',
    options: mtValues.negative
  },
  {
    id: 'auto',
    label: 'Tự động',
    options: mtValues.auto
  }
];

const mrValues = generateMarginValues('mr', true, true);
export const MARGIN_R_GROUPS: MarginGroup[] = [
  {
    id: 'fixed',
    label: 'Cố định',
    options: [...mrValues.none, ...mrValues.fixed]
  },
  {
    id: 'negative',
    label: 'Âm',
    options: mrValues.negative
  },
  {
    id: 'auto',
    label: 'Tự động',
    options: mrValues.auto
  }
];

const mbValues = generateMarginValues('mb', true, true);
export const MARGIN_B_GROUPS: MarginGroup[] = [
  {
    id: 'fixed',
    label: 'Cố định',
    options: [...mbValues.none, ...mbValues.fixed]
  },
  {
    id: 'negative',
    label: 'Âm',
    options: mbValues.negative
  },
  {
    id: 'auto',
    label: 'Tự động',
    options: mbValues.auto
  }
];

const mlValues = generateMarginValues('ml', true, true);
export const MARGIN_L_GROUPS: MarginGroup[] = [
  {
    id: 'fixed',
    label: 'Cố định',
    options: [...mlValues.none, ...mlValues.fixed]
  },
  {
    id: 'negative',
    label: 'Âm',
    options: mlValues.negative
  },
  {
    id: 'auto',
    label: 'Tự động',
    options: mlValues.auto
  }
];

// Margin groups for start/end (logical directions)
const msValues = generateMarginValues('ms', true, true);
export const MARGIN_S_GROUPS: MarginGroup[] = [
  {
    id: 'fixed',
    label: 'Cố định',
    options: [...msValues.none, ...msValues.fixed]
  },
  {
    id: 'negative',
    label: 'Âm',
    options: msValues.negative
  },
  {
    id: 'auto',
    label: 'Tự động',
    options: msValues.auto
  }
];

const meValues = generateMarginValues('me', true, true);
export const MARGIN_E_GROUPS: MarginGroup[] = [
  {
    id: 'fixed',
    label: 'Cố định',
    options: [...meValues.none, ...meValues.fixed]
  },
  {
    id: 'negative',
    label: 'Âm',
    options: meValues.negative
  },
  {
    id: 'auto',
    label: 'Tự động',
    options: meValues.auto
  }
];