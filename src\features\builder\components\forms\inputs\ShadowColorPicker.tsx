/**
 * Shadow Color Picker
 * Specialized color picker for shadow colors with opacity support
 * Supports Tailwind CSS v4 shadow color format: shadow-{color}/{opacity}
 */

import React, { useState } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// Shadow-specific colors with opacity variants
const SHADOW_COLORS = {
  // Grays - most common for shadows
  gray: {
    base: 'gray',
    shades: ['300', '400', '500', '600', '700', '800', '900'],
    opacities: ['10', '25', '50', '75']
  },
  slate: {
    base: 'slate',
    shades: ['300', '400', '500', '600', '700', '800', '900'],
    opacities: ['10', '25', '50', '75']
  },
  
  // Black variations
  black: {
    base: 'black',
    shades: [''],
    opacities: ['10', '25', '50', '75']
  },
  
  // Colored shadows for creative effects
  blue: {
    base: 'blue',
    shades: ['400', '500', '600', '700'],
    opacities: ['20', '30', '50']
  },
  red: {
    base: 'red',
    shades: ['400', '500', '600', '700'],
    opacities: ['20', '30', '50']
  },
  green: {
    base: 'green',
    shades: ['400', '500', '600', '700'],
    opacities: ['20', '30', '50']
  },
  yellow: {
    base: 'yellow',
    shades: ['400', '500', '600', '700'],
    opacities: ['20', '30', '50']
  },
  purple: {
    base: 'purple',
    shades: ['400', '500', '600', '700'],
    opacities: ['20', '30', '50']
  },
  pink: {
    base: 'pink',
    shades: ['400', '500', '600', '700'],
    opacities: ['20', '30', '50']
  }
};

// Special shadow options
const SPECIAL_SHADOWS = [
  { 
    label: 'Không có màu', 
    value: '', 
    preview: 'transparent',
    description: 'Sử dụng màu mặc định của shadow'
  }
];

interface ShadowColorPickerProps {
  value?: string;
  onChange: (color: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

/**
 * Parse shadow color to get preview color
 */
const getShadowColorPreview = (shadowColor: string): string => {
  if (!shadowColor) return 'transparent';
  
  // Extract color from shadow format: shadow-gray-500/50 -> gray-500
  const match = shadowColor.match(/shadow-(.+?)(?:\/|$)/);
  if (match) {
    return match[1];
  }
  
  return 'gray-500';
};

/**
 * Get display name for shadow color
 */
const getShadowColorDisplayName = (shadowColor: string): string => {
  if (!shadowColor) return 'Chưa chọn màu';
  
  const match = shadowColor.match(/shadow-(.+?)(?:\/(.+))?$/);
  if (match) {
    const color = match[1];
    const opacity = match[2];
    
    // Handle black special case
    if (color === 'black') {
      return `Đen${opacity ? ` (${opacity}%)` : ''}`;
    }
    
    // Handle other colors
    const colorParts = color.split('-');
    if (colorParts.length === 2) {
      const [colorName, shade] = colorParts;
      return `${colorName.charAt(0).toUpperCase() + colorName.slice(1)} ${shade}${opacity ? ` (${opacity}%)` : ''}`;
    }
    
    return color.charAt(0).toUpperCase() + color.slice(1);
  }
  
  return shadowColor;
};

export const ShadowColorPicker: React.FC<ShadowColorPickerProps> = ({
  value = '',
  onChange,
  disabled = false,
  placeholder = 'Chọn màu bóng đổ'
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleColorSelect = (colorValue: string) => {
    onChange(colorValue);
    setIsOpen(false);
  };

  const previewColor = getShadowColorPreview(value);
  const displayName = getShadowColorDisplayName(value);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "justify-start w-full h-10",
            !value && "text-muted-foreground"
          )}
          disabled={disabled}
        >
          <div className="flex items-center gap-2">
            <div 
              className={cn(
                "w-4 h-4 rounded-full border border-gray-300 flex-shrink-0",
                previewColor === 'transparent' ? 'bg-transparent' : `bg-${previewColor}`
              )}
            />
            <span className="truncate">
              {value ? displayName : placeholder}
            </span>
          </div>
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-4">
        <div className="space-y-4">
          <h4 className="text-sm font-semibold">Chọn màu bóng đổ</h4>
          
          {/* Special Options */}
          <div className="space-y-2">
            <h5 className="text-xs font-medium text-gray-600">Tùy chọn đặc biệt</h5>
            <div className="grid grid-cols-1 gap-2">
              {SPECIAL_SHADOWS.map((special) => (
                <button
                  key={special.value}
                  onClick={() => handleColorSelect(special.value)}
                  className={cn(
                    "flex items-center gap-2 p-2 text-left hover:bg-gray-50 rounded border",
                    value === special.value && "bg-blue-50 border-blue-200"
                  )}
                >
                  <div 
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{ backgroundColor: special.preview }}
                  />
                  <div>
                    <div className="text-sm font-medium">{special.label}</div>
                    <div className="text-xs text-gray-500">{special.description}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Shadow Colors */}
          <div className="space-y-4 max-h-80 overflow-y-auto">
            {Object.entries(SHADOW_COLORS).map(([colorName, config]) => (
              <div key={colorName}>
                <h5 className="text-xs font-medium mb-2 capitalize text-gray-600">
                  {colorName === 'black' ? 'Đen' : colorName}
                </h5>
                
                {/* Color Shades */}
                {config.shades.map((shade) => (
                  <div key={shade} className="mb-3">
                    {shade && (
                      <div className="text-xs text-gray-500 mb-1">{shade}</div>
                    )}
                    <div className="grid grid-cols-4 gap-1.5">
                      {config.opacities.map((opacity) => {
                        const shadowValue = colorName === 'black' 
                          ? `shadow-black/${opacity}`
                          : `shadow-${colorName}-${shade}/${opacity}`;
                        const bgColor = colorName === 'black'
                          ? 'black'
                          : `${colorName}-${shade}`;
                        
                        return (
                          <button
                            key={opacity}
                            onClick={() => handleColorSelect(shadowValue)}
                            className={cn(
                              "relative w-12 h-8 rounded border border-gray-200 hover:scale-105 transition-transform",
                              value === shadowValue && "ring-2 ring-blue-500"
                            )}
                            title={`${shadowValue} (${opacity}% opacity)`}
                          >
                            <div 
                              className={cn(
                                "w-full h-full rounded",
                                colorName === 'black' ? 'bg-black' : `bg-${bgColor}`
                              )}
                              style={{ 
                                opacity: parseInt(opacity) / 100 
                              }}
                            />
                            <div className="absolute bottom-0 right-0 text-xs text-white bg-black bg-opacity-60 px-1 rounded">
                              {opacity}%
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
