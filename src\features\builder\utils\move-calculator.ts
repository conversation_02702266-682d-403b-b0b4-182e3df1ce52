/**
 * Move Calculator Utility
 * Clean, testable functions for calculating tree moves
 */

import { TreeNode } from '../context/types';
import { DropPosition, CalculatedMove } from '../types/drag-drop';

/**
 * Find a node in the tree by ID
 */
export function findNodeInTree(root: TreeNode, targetId: string): TreeNode | null {
  if (root.id === targetId) return root;
  
  for (const child of root.children) {
    const found = findNodeInTree(child, targetId);
    if (found) return found;
  }
  
  return null;
}

/**
 * Find the parent of a node in the tree
 */
export function findParentOfNode(root: TreeNode, targetId: string): TreeNode | null {
  for (const child of root.children) {
    if (child.id === targetId) return root;
    
    const found = findParentOfNode(child, targetId);
    if (found) return found;
  }
  
  return null;
}

/**
 * Calculate the actual move operation from a position-based drop
 */
export function calculateMoveFromPosition(
  root: TreeNode,
  sourceNodeId: string,
  targetNodeId: string,
  position: DropPosition
): CalculatedMove {
  const targetNode = findNodeInTree(root, targetNodeId);
  if (!targetNode) {
    throw new Error(`Target node ${targetNodeId} not found`);
  }

  // For 'inside' position, insert as child at the end
  if (position === 'inside') {
    // Validate that target can accept children
    if (targetNode.type !== 'frame' && targetNode.id !== 'root') {
      throw new Error('Cannot drop inside leaf nodes (text/image)');
    }
    
    return {
      sourceNodeId,
      targetParentId: targetNodeId,
      targetIndex: targetNode.children.length
    };
  }

  // For 'before'/'after', find parent and calculate index
  const parent = findParentOfNode(root, targetNodeId);
  if (!parent) {
    throw new Error(`Parent of ${targetNodeId} not found`);
  }

  const targetIndex = parent.children.findIndex(child => child.id === targetNodeId);
  if (targetIndex === -1) {
    throw new Error(`Target ${targetNodeId} not found in parent's children`);
  }

  return {
    sourceNodeId,
    targetParentId: parent.id,
    targetIndex: position === 'before' ? targetIndex : targetIndex + 1
  };
}

/**
 * Validate if a move operation is valid
 */
export function validateMoveOperation(
  root: TreeNode,
  sourceNodeId: string,
  targetParentId: string,
  targetIndex: number
): { valid: boolean; reason?: string } {
  // Cannot move root
  if (sourceNodeId === 'root') {
    return { valid: false, reason: 'Cannot move root node' };
  }

  // Cannot move to self
  if (sourceNodeId === targetParentId) {
    return { valid: false, reason: 'Cannot move node to itself' };
  }

  // Check if target parent exists and can accept children
  const targetParent = findNodeInTree(root, targetParentId);
  if (!targetParent) {
    return { valid: false, reason: 'Target parent not found' };
  }

  if (targetParent.type !== 'frame' && targetParent.id !== 'root') {
    return { valid: false, reason: 'Target cannot accept children' };
  }

  // Validate target index bounds
  if (targetIndex < 0 || targetIndex > targetParent.children.length) {
    return { valid: false, reason: 'Invalid target index' };
  }

  // Check if trying to move to descendant (would create cycle)
  const sourceNode = findNodeInTree(root, sourceNodeId);
  if (!sourceNode) {
    return { valid: false, reason: 'Source node not found' };
  }

  function isDescendant(node: TreeNode, ancestorId: string): boolean {
    if (node.id === ancestorId) return true;
    return node.children.some(child => isDescendant(child, ancestorId));
  }

  if (isDescendant(sourceNode, targetParentId)) {
    return { valid: false, reason: 'Cannot move node to its descendant' };
  }

  return { valid: true };
}