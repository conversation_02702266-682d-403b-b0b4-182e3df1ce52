import React from 'react';
import { createPortal } from 'react-dom';
import { TreeNode } from '../../context/types';

interface DragPreviewProps {
  node: TreeNode;
  position: { x: number; y: number };
}

export const DragPreview: React.FC<DragPreviewProps> = ({ node, position }) => {
  return createPortal(
    <div
      className="fixed pointer-events-none z-[9999]"
      style={{
        left: `${position.x + 15}px`,
        top: `${position.y - 10}px`,
      }}
    >
      <div className="bg-white rounded-lg shadow-2xl border-2 border-blue-500 p-3 opacity-90 drag-preview gpu-accelerated">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full drop-indicator-pulse" />
          <span className="text-sm font-medium text-gray-700">
            {node.label}
          </span>
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {node.type}
        </div>
      </div>
    </div>,
    document.body
  );
};