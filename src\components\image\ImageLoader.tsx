import React, { useState, useEffect } from "react";
import { <PERSON>Alert, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils"; // Dùng từ shadcn nếu bạn có sẵn

interface ImageLoaderProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  containerClassName?: string;
  fallbackText?: string;
}

export const ImageLoader: React.FC<ImageLoaderProps> = ({
  className,
  containerClassName,
  src,
  alt,
  fallbackText,
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (!src) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setHasError(false);

    const img = new Image();
    img.src = src;

    if (img.complete) {
      setIsLoading(false);
      return;
    }

    img.onload = () => {
      setIsLoading(false);
    };

    img.onerror = () => {
      setHasError(true);
      setIsLoading(false);
    };
  }, [src]);

  const loadingContent = (
    <div className="flex justify-center items-center w-full h-full bg-gray-100">
      <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
    </div>
  );

  const errorContent = (
    <div className="flex justify-center items-center w-full h-full bg-gray-100 text-center p-2">
      <div>
        <CloudAlert className="w-6 h-6 text-gray-400 mx-auto mb-1" />
        <span className="text-xs text-gray-500">
          {fallbackText || alt || "Không thể tải ảnh"}
        </span>
      </div>
    </div>
  );

  const imageContent = (
    <img
      src={src}
      alt={alt ?? "Image"}
      className={cn("object-cover", className)}
      loading={props.loading || "lazy"}
      decoding={props.decoding || "async"}
      {...props}
    />
  );

  if (containerClassName) {
    return (
      <div className={cn("relative overflow-hidden", containerClassName)}>
        {isLoading && loadingContent}
        {hasError && errorContent}
        {!isLoading && !hasError && imageContent}
      </div>
    );
  }

  if (isLoading) return loadingContent;
  if (hasError) return errorContent;
  return imageContent;
};
