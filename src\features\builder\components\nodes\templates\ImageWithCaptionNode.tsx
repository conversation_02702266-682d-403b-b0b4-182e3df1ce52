import React, { useState, useEffect } from "react";
import { NodeRendererProps } from "../NodeFactory";
import { CloudAlert, Loader2 } from "lucide-react";
import { compareNodeWithProperties } from '@/lib/memo-utils';

const ImageWithCaptionNodeComponent: React.FC<NodeRendererProps & React.HTMLAttributes<HTMLElement>> = ({ 
  node,
  ...htmlProps
}) => {
  const src = node.properties?.src as string;
  const alt = (node.properties?.alt as string) || "";
  const caption = (node.properties?.caption as string) || alt;
  
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (!src) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setHasError(false);

    const img = new Image();
    img.src = src;

    if (img.complete) {
      setIsLoading(false);
      return;
    }

    img.onload = () => {
      setIsLoading(false);
    };

    img.onerror = () => {
      setHasError(true);
      setIsLoading(false);
    };
  }, [src]);

  const loadingContent = (
    <div className="flex justify-center items-center w-full h-full bg-gray-100">
      <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
    </div>
  );

  const errorContent = (
    <div className="flex justify-center items-center w-full h-full bg-gray-100 text-center p-2">
      <div>
        <CloudAlert className="w-6 h-6 text-gray-400 mx-auto mb-1" />
        <span className="text-xs text-gray-500">
          {src ? "Không thể tải ảnh" : "Chưa có ảnh"}
        </span>
      </div>
    </div>
  );

  const imageContent = (
    <img
      src={src}
      alt={alt ?? "Image"}
      className="w-full h-full rounded-lg shadow-md object-cover"
      loading="lazy"
      decoding="async"
    />
  );

  return (
    <figure
      className={node.style?.className}
      style={node.style?.css}
      {...htmlProps}
    >
      <div className="flex flex-col gap-2">
        <div className="w-full h-48 rounded-lg shadow-md overflow-hidden relative">
          {isLoading && loadingContent}
          {hasError && errorContent}
          {!isLoading && !hasError && imageContent}
        </div>

        {caption && (
          <figcaption className="text-sm text-gray-600 text-center italic">
            {caption}
          </figcaption>
        )}
      </div>
    </figure>
  );
};

// Memoized template node để prevent re-renders
export const ImageWithCaptionNode = React.memo(
  ImageWithCaptionNodeComponent,
  compareNodeWithProperties
);
