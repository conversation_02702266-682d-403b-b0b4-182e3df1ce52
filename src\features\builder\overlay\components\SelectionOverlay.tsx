import React, { useEffect, useState, useCallback, useRef } from 'react';
import { EditorMode } from '../../context/types';
import { ObserverManager } from '../utils/ObserverManager';
import { ElementFinder } from '../utils/ElementFinder';

interface SelectionOverlayProps {
  nodeId: string;
  getNodeElement: (nodeId: string) => HTMLElement | undefined;
  editorMode: EditorMode;
  containerElement: HTMLElement;
}

export const SelectionOverlay: React.FC<SelectionOverlayProps> = ({
  nodeId,
  getNodeElement,
  editorMode,
  containerElement,
}) => {
  const [rect, setRect] = useState<DOMRect | null>(null);
  const observerManager = useRef(ObserverManager.getInstance());
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced update để tránh update quá thường xuyên
  const debouncedUpdateRect = useCallback(() => {
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
    
    updateTimeoutRef.current = setTimeout(() => {
      try {
        const wrapperElement = getNodeElement(nodeId);
        if (!wrapperElement || !containerElement) {
          setRect(null);
          return;
        }

        // Tìm element thực tế để render overlay
        const targetElement = ElementFinder.findActualElement(wrapperElement, nodeId);
        if (!targetElement) {
          setRect(null);
          return;
        }

        // Tính toán position an toàn
        const relativeRect = ElementFinder.calculateRelativePosition(
          targetElement, 
          containerElement
        );
        
        setRect(relativeRect);
      } catch {
        setRect(null);
      }
    }, 16); // ~60fps
  }, [nodeId, getNodeElement, containerElement]);

  useEffect(() => {
    const wrapperElement = getNodeElement(nodeId);
    if (!wrapperElement) return;

    // Initial update
    debouncedUpdateRect();

    // Use centralized observer manager
    const targetElement = ElementFinder.findActualElement(wrapperElement);
    if (targetElement) {
      observerManager.current.observe(nodeId, targetElement, debouncedUpdateRect);
    }

    // Listen for container changes
    const containerObserver = new ResizeObserver(debouncedUpdateRect);
    containerObserver.observe(containerElement);

    // Listen for scroll and resize
    const handleScrollResize = () => debouncedUpdateRect();
    containerElement.addEventListener('scroll', handleScrollResize, { passive: true });
    window.addEventListener('resize', handleScrollResize, { passive: true });

    // Listen for builder content changes (throttled)
    let contentChangeTimeout: NodeJS.Timeout;
    const handleContentChange = () => {
      if (contentChangeTimeout) clearTimeout(contentChangeTimeout);
      contentChangeTimeout = setTimeout(debouncedUpdateRect, 100);
    };
    window.addEventListener('builder-content-changed', handleContentChange);

    return () => {
      // Cleanup
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      if (contentChangeTimeout) {
        clearTimeout(contentChangeTimeout);
      }
      
      if (targetElement) {
        observerManager.current.unobserve(nodeId, targetElement);
      }
      containerObserver.disconnect();
      containerElement.removeEventListener('scroll', handleScrollResize);
      window.removeEventListener('resize', handleScrollResize);
      window.removeEventListener('builder-content-changed', handleContentChange);
    };
  }, [nodeId, getNodeElement, containerElement, debouncedUpdateRect]);

  if (!rect || rect.width <= 0 || rect.height <= 0) {
    return null;
  }

  const isMove = editorMode === 'move';
  const borderColor = isMove ? '#ff6b35' : '#0ea5e9'; // Orange for move, blue for edit
  
  // Handle size and positions
  const handleSize = 8;
  const handleOffset = handleSize / 2;

  return (
    <>
      {/* Main selection border */}
      <div
        className="absolute pointer-events-none"
        style={{
          left: `${Math.round(rect.left)}px`,
          top: `${Math.round(rect.top)}px`,
          width: `${Math.round(rect.width)}px`,
          height: `${Math.round(rect.height)}px`,
          border: `2px solid ${borderColor}`,
          zIndex: 1001,
        }}
      />
      
      {/* Corner handles */}
      {/* Top-left */}
      <div
        className="absolute pointer-events-none"
        style={{
          left: `${Math.round(rect.left - handleOffset)}px`,
          top: `${Math.round(rect.top - handleOffset)}px`,
          width: `${handleSize}px`,
          height: `${handleSize}px`,
          backgroundColor: borderColor,
          border: '1px solid white',
          zIndex: 1002,
        }}
      />
      
      {/* Top-right */}
      <div
        className="absolute pointer-events-none"
        style={{
          left: `${Math.round(rect.left + rect.width - handleOffset)}px`,
          top: `${Math.round(rect.top - handleOffset)}px`,
          width: `${handleSize}px`,
          height: `${handleSize}px`,
          backgroundColor: borderColor,
          border: '1px solid white',
          zIndex: 1002,
        }}
      />
      
      {/* Bottom-left */}
      <div
        className="absolute pointer-events-none"
        style={{
          left: `${Math.round(rect.left - handleOffset)}px`,
          top: `${Math.round(rect.top + rect.height - handleOffset)}px`,
          width: `${handleSize}px`,
          height: `${handleSize}px`,
          backgroundColor: borderColor,
          border: '1px solid white',
          zIndex: 1002,
        }}
      />
      
      {/* Bottom-right */}
      <div
        className="absolute pointer-events-none"
        style={{
          left: `${Math.round(rect.left + rect.width - handleOffset)}px`,
          top: `${Math.round(rect.top + rect.height - handleOffset)}px`,
          width: `${handleSize}px`,
          height: `${handleSize}px`,
          backgroundColor: borderColor,
          border: '1px solid white',
          zIndex: 1002,
        }}
      />
      
      {/* Edge handles (only show for larger elements) */}
      {rect.width > 40 && (
        <>
          {/* Top edge */}
          <div
            className="absolute pointer-events-none"
            style={{
              left: `${Math.round(rect.left + rect.width / 2 - handleOffset)}px`,
              top: `${Math.round(rect.top - handleOffset)}px`,
              width: `${handleSize}px`,
              height: `${handleSize}px`,
              backgroundColor: borderColor,
              border: '1px solid white',
              zIndex: 1002,
            }}
          />
          
          {/* Bottom edge */}
          <div
            className="absolute pointer-events-none"
            style={{
              left: `${Math.round(rect.left + rect.width / 2 - handleOffset)}px`,
              top: `${Math.round(rect.top + rect.height - handleOffset)}px`,
              width: `${handleSize}px`,
              height: `${handleSize}px`,
              backgroundColor: borderColor,
              border: '1px solid white',
              zIndex: 1002,
            }}
          />
        </>
      )}
      
      {rect.height > 40 && (
        <>
          {/* Left edge */}
          <div
            className="absolute pointer-events-none"
            style={{
              left: `${Math.round(rect.left - handleOffset)}px`,
              top: `${Math.round(rect.top + rect.height / 2 - handleOffset)}px`,
              width: `${handleSize}px`,
              height: `${handleSize}px`,
              backgroundColor: borderColor,
              border: '1px solid white',
              zIndex: 1002,
            }}
          />
          
          {/* Right edge */}
          <div
            className="absolute pointer-events-none"
            style={{
              left: `${Math.round(rect.left + rect.width - handleOffset)}px`,
              top: `${Math.round(rect.top + rect.height / 2 - handleOffset)}px`,
              width: `${handleSize}px`,
              height: `${handleSize}px`,
              backgroundColor: borderColor,
              border: '1px solid white',
              zIndex: 1002,
            }}
          />
        </>
      )}
    </>
  );
};