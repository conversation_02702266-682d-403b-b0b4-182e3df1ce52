import { DoublePanelsData } from "../states/type";

export interface LibraryItem {
  id: number;
  title: string;
  url: string;
  thumbnail: string;
  type: "image" | "video";
}

export interface ProcessingTicket {
  id: number;
  ticketName: string;
  ticketCode: string;
  createdDate: string;
  status: "pending" | "processing" | "completed" | "rejected";
}

export interface StatData {
  number: string;
  name: string;
  description: string;
}

// ===== MOCK DATA =====

export const mockLibraryItems: LibraryItem[] = [
  {
    id: 1,
    title: "<PERSON>ình ảnh cảnh quan <PERSON>ánh <PERSON>a",
    url: "https://api.hocnhe.com/storage/v1/public/view/2025-06-04/original/d0b5850f77c9238dad2b6b08c81858df0f324f56-1749010250125.jpg",
    thumbnail:
      "https://api.hocnhe.com/storage/v1/public/view/2025-06-04/original/d0b5850f77c9238dad2b6b08c81858df0f324f56-1749010250125.jpg",
    type: "image",
  },
  {
    id: 2,
    title: "Video giới thiệu du lịch",
    url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    thumbnail: "https://picsum.photos/300/200?random=2",
    type: "video",
  },
  {
    id: 3,
    title: "Bộ sưu tập ảnh văn hóa",
    url: "https://picsum.photos/300/200?random=3",
    thumbnail: "https://picsum.photos/300/200?random=3",
    type: "image",
  },
  {
    id: 4,
    title: "Video hướng dẫn dịch vụ",
    url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    thumbnail: "https://picsum.photos/300/200?random=4",
    type: "video",
  },
  {
    id: 5,
    title: "Hình ảnh lễ hội địa phương",
    url: "https://picsum.photos/300/200?random=5",
    thumbnail: "https://picsum.photos/300/200?random=5",
    type: "image",
  },
  {
    id: 6,
    title: "Video quảng bá du lịch",
    url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    thumbnail: "https://picsum.photos/300/200?random=6",
    type: "video",
  },
  {
    id: 7,
    title: "Video quảng bá du lịch",
    url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    thumbnail: "https://picsum.photos/300/200?random=7",
    type: "video",
  },
  {
    id: 8,
    title: "Video quảng bá du lịch",
    url: "https://picsum.photos/300/200?random=8",
    thumbnail: "https://picsum.photos/300/200?random=8",
    type: "image",
  },
];

export const mockProcessingTickets: ProcessingTicket[] = [
  {
    id: 1,
    ticketName: "Đăng ký kinh doanh",
    ticketCode: "DKK001",
    createdDate: "2025-06-15",
    status: "pending",
  },
  {
    id: 2,
    ticketName: "Cấp phép xây dựng",
    ticketCode: "CPX002",
    createdDate: "2025-06-14",
    status: "processing",
  },
  {
    id: 3,
    ticketName: "Đăng ký tạm trú",
    ticketCode: "DTT003",
    createdDate: "2025-06-13",
    status: "completed",
  },
  {
    id: 4,
    ticketName: "Cấp giấy phép lao động",
    ticketCode: "GLD004",
    createdDate: "2025-06-12",
    status: "rejected",
  },
];

export const mockStats: StatData[] = [
  {
    number: "12",
    name: "Người dùng",
    description: "Tổng số người dùng đã đăng ký",
  },
  {
    number: "567000",
    name: "Bài viết",
    description: "Số bài viết đã được xuất bản",
  },
  {
    number: "89",
    name: "Dịch vụ",
    description: "Số dịch vụ công trực tuyến",
  },
  {
    number: "12",
    name: "Phòng ban",
    description: "Số phòng ban tham gia",
  },
];

export const mockDoublePanelsData: DoublePanelsData[] = [
  {
    leftPanel: {
      type: "text",
      content:
        "<h1 class='text-[20px]'>Giúp giám đốc Sở Nội vụ thực hiện những nhiệm vụ sau</h1>",
      title: "01",
      size: "1/3",
    },
    rightPanel: {
      type: "text",
      content:
        " - Trình cấp có thẩm quyền ban hành Danh mục cơ quan, tổ chức thuộc nguồn nộp lưu tài liệu vào Lưu trữ lịch sử. \n - Hướng dẫn các cơ quan, tổ chức thuộc nguồn nộp lưu, chuẩn bị hồ sơ, tài liệu nộp lưu vào Lưu trữ lịch sử tỉnh.\n  - Thu thập hồ sơ, tài liệu của các cơ quan, tổ chức đến hạn nộp lưu vào Lưu trữ lịch sử tỉnh theo quy định; tổ chức đánh giá, phân loại, chỉnh lý, xác định giá trị tài liệu; tổ chức tiêu hủy tài liệu hết giá trị; hệ thống hóa các phông tài liệu đã thu thập và đang bảo quản tại Lưu trữ lịch sử tỉnh\n  - Tổ chức sưu tầm tài liệu lưu trữ quý, hiếm và lập bản sao bảo hiểm tài liệu lưu trữ quý, hiếm (ở dạng số hóa) theo quy định;\n  - Bảo vệ, bảo quản an toàn tài liệu, tư liệu lưu trữ; bồi nền, phục chế tài liệu, tư liệu hư hỏng theo quy định;\n  - Xây dựng và quản lý hệ thống công cụ thống kê, tra cứu tài liệu, tư liệu lưu trữ, tổ chức giải mật tài liệu lưu trữ; lập danh mục tài liệu thuộc chế độ hạn chế sử dụng;\n  - Tuyên truyển, giới thiệu, trưng bày và triển lãm tài liệu lưu trữ; công bố tài liệu lưu trữ bằng các ấn phẩm; tổ chức phục vụ độc giả tại Phòng đọc; cấp bản sao, chứng thực tài liệu lưu trữ...;\n- Số hóa tài liệu lưu trữ; ứng dụng khoa học - công nghệ vào quản lý hồ sơ, tài liệu lưu trữ lịch sử; tổ chức thực hiện các thủ tục hành chính liên quan đến chức nâng, nhiệm vụ của Trung tâm;\n - Thực hiện nghiên cứu, ứng dụng khoa học và công nghệ vào thực tiễn công tác của Trung tâm Lưu trữ lịch sử.",
      title: "",
      size: "2/3",
    },
  },
  {
    leftPanel: {
      type: "image",
      content: "src/assets/images/banner-02.jpg",
      title: "",
    },
    rightPanel: {
      type: "text",
      content:
        "<h1 class='text-[20px]'>Tổ chức thực hiện các hoạt động dịch vụ công về lưu trữ theo quy định của pháp luật</h1>",
      title: "02",
    },
  },
  {
    leftPanel: {
      type: "text",
      content:
        "<h1 class='text-[20px]'>Quản lý về tổ chức bộ máy, số lượng người làm việc, tài chính, tài sản được giao; thực hiện các chế độ, chính sách đối với viên chức, người lao động của Trung tâm theo phân cấp của Giám đốc Sở và theo quy định của Pháp luật.</h1>",
      title: "03",
    },
    rightPanel: {
      type: "image",
      content: "src/assets/images/banner-03.jpg",
      title: "",
    },
  },
  {
    leftPanel: {
      type: "image",
      content: "src/assets/images/banner-04.jpg",
      title: "",
    },
    rightPanel: {
      type: "text",
      content:
        "<h1 class='text-[20px]'>Thực hiện các nhiệm vụ khác do Giám đốc Sở giao hoặc cơ quan có thẩm quyền giao theo quy định của pháp luật </h1>",
      title: "04",
    },
  },
];
