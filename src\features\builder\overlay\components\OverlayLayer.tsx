import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { useOverlay } from '../context/OverlayContext';
import { SelectionOverlay } from './SelectionOverlay';
import { HoverOverlay } from './HoverOverlay';
import { DropTargetOverlay } from './DropTargetOverlay';

interface OverlayLayerProps {
  canvasRef: React.RefObject<HTMLElement>;
}

export const OverlayLayer: React.FC<OverlayLayerProps> = ({ canvasRef }) => {
  const { state, getNodeElement } = useOverlay();
  const [overlayContainer, setOverlayContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    // Ensure canvas has relative positioning
    const canvas = canvasRef.current;
    const computedStyle = window.getComputedStyle(canvas);
    if (computedStyle.position === 'static') {
      canvas.style.position = 'relative';
    }

    // Create overlay container inside canvas
    const overlay = document.createElement('div');
    overlay.className = 'builder-overlay-layer';
    overlay.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      z-index: 1000;
      overflow: visible;
    `;

    canvas.appendChild(overlay);
    setOverlayContainer(overlay);

    return () => {
      overlay.remove();
    };
  }, [canvasRef]);

  if (!overlayContainer) return null;

  // Don't render overlays in preview mode
  if (state.editorMode === 'preview') {
    return null;
  }

  return createPortal(
    <>
      {state.selectedNodeId && (
        <SelectionOverlay
          nodeId={state.selectedNodeId}
          getNodeElement={getNodeElement}
          editorMode={state.editorMode}
          containerElement={overlayContainer}
        />
      )}
      {state.hoveredNodeId && state.hoveredNodeId !== state.selectedNodeId && (
        <HoverOverlay
          nodeId={state.hoveredNodeId}
          getNodeElement={getNodeElement}
          containerElement={overlayContainer}
        />
      )}
      {state.dropTargetNodeId && (
        <DropTargetOverlay
          nodeId={state.dropTargetNodeId}
          getNodeElement={getNodeElement}
          containerElement={overlayContainer}
        />
      )}
    </>,
    overlayContainer
  );
};