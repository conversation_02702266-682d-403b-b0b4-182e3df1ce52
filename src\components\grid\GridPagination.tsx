
import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";

export interface GridPaginationProps {
    length: number;
    itemsPerPage: number;
    onChangePagination?: (page: number) => void;
}

export const GridPagination: React.FC<GridPaginationProps> = ({
    length = 0,
    itemsPerPage,
    onChangePagination,
}) => {
    const [currentPage, setCurrentPage] = useState(0);

    // Reset pagination when links data changes
    useEffect(() => {
        setCurrentPage(0);
    }, [length]);

    if (length === 0) {
        return null;
    }

    const totalPages = Math.ceil(length / itemsPerPage);

    const handlePagination = (index: number) => {
        setCurrentPage(index);
        onChangePagination?.(index);
    };

    return (
        <div className="flex items-center justify-center gap-2 mt-6">
            {Array.from({ length: totalPages }, (_, index) => (
                <Button
                    key={index}
                    variant={currentPage === index ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePagination(index)}
                    className={`${currentPage === index ? 'w-12' : 'w-2'} h-2 p-0`}
                >
                </Button>
            ))}
        </div>
    );
};
