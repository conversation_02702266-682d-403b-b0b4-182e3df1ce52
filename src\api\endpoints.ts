/**
 * Centralized API endpoints constants
 * Structure: /{prefix}/v{version}/{type}/{resource}
 *
 * Prefixes:
 * - auth: Authentication, authorization, user management
 * - portal: Content management (posts, categories, pages)
 * - storage: File storage and management
 *
 * Types:
 * - public: Public access APIs
 * - admin: Admin access APIs
 * - private: Private/internal APIs
 */

export const API_ENDPOINTS = {
  // Authentication & User Management APIs
  AUTH: {
    // Public auth APIs
    PUBLIC: {
      USERS: {
        BASE: "/auth/v1/public/users",
        LOGIN: "/auth/v1/public/users/login",
        REGISTER: "/auth/v1/public/users/register",
        FORGOT_PASSWORD: "/auth/v1/public/users/forgot-password",

        FILTER: "/auth/v1/public/users/filter",
      },
    },

    // Private auth APIs
    PRIVATE: {
      USERS: {
        GET_ME: "/auth/v1/private/users/me",
        REFRESH_TOKEN: "/auth/v1/private/users/refresh-token",
        LOGOUT: "/auth/v1/private/users/logout",
      },
    },

    // Admin auth APIs
    ADMIN: {
      USERS: "/auth/v1/admin/users",
      ROLES: "/auth/v1/admin/roles",
      PERMISSIONS: "/auth/v1/admin/permissions",
      GROUPS: "/auth/v1/admin/groups",
      GROUP_ROLES: "/auth/v1/admin/group-has-role",
      RESOURCES: "/auth/v1/admin/resources",
      ACTIONS: "/auth/v1/admin/actions",
    },
  },

  // Portal Content Management APIs
  PORTAL: {
    // Public portal APIs
    PUBLIC: {
      POSTS: "/portal/v1/public/posts",
      CATEGORIES: "/portal/v1/public/categories",
      ALBUMS: "/portal/v1/public/albums",
      MEDIA: "/portal/v1/public/media",
      QUESTIONS: "/portal/v1/public/questions",
      ANSWERS: "/portal/v1/public/answers",
      QA_CONFIG: "/portal/v1/public/config/qa_config",
    },

    // Private portal APIs
    PRIVATE: {
      QUESTIONS: "/portal/v1/private/questions",
    },

    // Admin portal APIs
    ADMIN: {
      POSTS: "/portal/v1/admin/posts",
      CATEGORIES: "/portal/v1/admin/categories",
      ALBUMS: "/portal/v1/admin/albums",
      MEDIA: "/portal/v1/admin/media",
      MEDIA_IN_ALBUM: "/portal/v1/admin/media-in-album",
      QUESTIONS: "/portal/v1/admin/questions",
      ANSWERS: "/portal/v1/admin/answers",
      QA_CONFIG_UPDATE: "/portal/v1/admin/config/qa_config",
    },

    // Category-Post linking (no auth level specified in original)
    CATEGORIES: "/portal/v1/categories",
  },

  // File Storage APIs
  STORAGE: {
    // Public storage APIs
    PUBLIC: {
      UPLOAD: "/storage/v1/public/upload",
      VIEW: "/storage/v1/public/view",
    },
  },
} as const;
