// Coordinate value groups for positioning

export interface CoordinateOption {
  value: string;
  label: string;
  description?: string;
}

export interface CoordinateGroup {
  id: string;
  label: string;
  options: CoordinateOption[];
}

// Fixed coordinate values - using Tailwind spacing scale
const COORDINATE_FIXED_VALUES = [
  { value: '0', label: '0', description: '0px' },
  { value: '0.5', label: '0.5', description: '2px' },
  { value: '1', label: '1', description: '4px' },
  { value: '1.5', label: '1.5', description: '6px' },
  { value: '2', label: '2', description: '8px' },
  { value: '2.5', label: '2.5', description: '10px' },
  { value: '3', label: '3', description: '12px' },
  { value: '3.5', label: '3.5', description: '14px' },
  { value: '4', label: '4', description: '16px' },
  { value: '5', label: '5', description: '20px' },
  { value: '6', label: '6', description: '24px' },
  { value: '7', label: '7', description: '28px' },
  { value: '8', label: '8', description: '32px' },
  { value: '9', label: '9', description: '36px' },
  { value: '10', label: '10', description: '40px' },
  { value: '11', label: '11', description: '44px' },
  { value: '12', label: '12', description: '48px' },
  { value: '14', label: '14', description: '56px' },
  { value: '16', label: '16', description: '64px' },
  { value: '20', label: '20', description: '80px' },
  { value: '24', label: '24', description: '96px' },
  { value: '28', label: '28', description: '112px' },
  { value: '32', label: '32', description: '128px' },
  { value: '36', label: '36', description: '144px' },
  { value: '40', label: '40', description: '160px' },
  { value: '44', label: '44', description: '176px' },
  { value: '48', label: '48', description: '192px' },
  { value: '52', label: '52', description: '208px' },
  { value: '56', label: '56', description: '224px' },
  { value: '60', label: '60', description: '240px' },
  { value: '64', label: '64', description: '256px' },
  { value: '72', label: '72', description: '288px' },
  { value: '80', label: '80', description: '320px' },
  { value: '96', label: '96', description: '384px' },
];

// Negative coordinate values
const COORDINATE_NEGATIVE_VALUES = [
  { value: '-0.5', label: '-0.5', description: '-2px' },
  { value: '-1', label: '-1', description: '-4px' },
  { value: '-1.5', label: '-1.5', description: '-6px' },
  { value: '-2', label: '-2', description: '-8px' },
  { value: '-2.5', label: '-2.5', description: '-10px' },
  { value: '-3', label: '-3', description: '-12px' },
  { value: '-3.5', label: '-3.5', description: '-14px' },
  { value: '-4', label: '-4', description: '-16px' },
  { value: '-5', label: '-5', description: '-20px' },
  { value: '-6', label: '-6', description: '-24px' },
  { value: '-7', label: '-7', description: '-28px' },
  { value: '-8', label: '-8', description: '-32px' },
  { value: '-9', label: '-9', description: '-36px' },
  { value: '-10', label: '-10', description: '-40px' },
  { value: '-11', label: '-11', description: '-44px' },
  { value: '-12', label: '-12', description: '-48px' },
  { value: '-14', label: '-14', description: '-56px' },
  { value: '-16', label: '-16', description: '-64px' },
  { value: '-20', label: '-20', description: '-80px' },
  { value: '-24', label: '-24', description: '-96px' },
  { value: '-28', label: '-28', description: '-112px' },
  { value: '-32', label: '-32', description: '-128px' },
  { value: '-36', label: '-36', description: '-144px' },
  { value: '-40', label: '-40', description: '-160px' },
  { value: '-44', label: '-44', description: '-176px' },
  { value: '-48', label: '-48', description: '-192px' },
  { value: '-52', label: '-52', description: '-208px' },
  { value: '-56', label: '-56', description: '-224px' },
  { value: '-60', label: '-60', description: '-240px' },
  { value: '-64', label: '-64', description: '-256px' },
  { value: '-72', label: '-72', description: '-288px' },
  { value: '-80', label: '-80', description: '-320px' },
  { value: '-96', label: '-96', description: '-384px' },
];

// Percentage values
const COORDINATE_PERCENTAGE_VALUES = [
  { value: '1/2', label: '50%', description: '1/2' },
  { value: '1/3', label: '33.33%', description: '1/3' },
  { value: '2/3', label: '66.67%', description: '2/3' },
  { value: '1/4', label: '25%', description: '1/4' },
  { value: '3/4', label: '75%', description: '3/4' },
  { value: 'full', label: '100%', description: 'Toàn bộ' },
];

// Generate coordinate groups for each position (top, right, bottom, left)
const generateCoordinateGroups = (position: string): CoordinateGroup[] => {
  const prefix = position.charAt(0); // t, r, b, l
  
  // Transform values with appropriate prefix
  const fixedValues = COORDINATE_FIXED_VALUES.map(opt => ({
    value: `${prefix}-${opt.value}`,
    label: opt.label,
    description: opt.description
  }));
  
  const negativeValues = COORDINATE_NEGATIVE_VALUES.map(opt => ({
    value: `-${prefix}-${opt.value.substring(1)}`,
    label: opt.label,
    description: opt.description
  }));
  
  const percentageValues = COORDINATE_PERCENTAGE_VALUES.map(opt => ({
    value: `${prefix}-${opt.value}`,
    label: opt.label,
    description: opt.description
  }));
  
  return [
    {
      id: 'fixed',
      label: 'Cố định',
      options: [
        { value: 'none', label: 'Không đặt', description: 'Không xác định' },
        ...fixedValues
      ]
    },
    {
      id: 'negative',
      label: 'Âm',
      options: negativeValues
    },
    {
      id: 'percentage',
      label: 'Phần trăm',
      options: percentageValues
    },
    {
      id: 'auto',
      label: 'Tự động',
      options: [{ value: `${prefix}-auto`, label: 'Auto', description: 'Tự động' }]
    }
  ];
};

export const TOP_GROUPS = generateCoordinateGroups('top');
export const RIGHT_GROUPS = generateCoordinateGroups('right');
export const BOTTOM_GROUPS = generateCoordinateGroups('bottom');
export const LEFT_GROUPS = generateCoordinateGroups('left');