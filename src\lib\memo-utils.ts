/**
 * Memo utilities for React.memo optimization
 * Provides reusable comparison functions for common component patterns
 */

import { TreeNode } from '../features/builder/context/types';

// Basic node comparison for simple components - very conservative
export const compareBasicNodeProps = <T extends { node: TreeNode }>(
  prevProps: T,
  nextProps: T
): boolean => {
  // Only optimize if it's the exact same node reference and ID
  return (
    prevProps.node === nextProps.node &&
    prevProps.node.id === nextProps.node.id
  );
};

// Node comparison with properties check - less strict for form editing
export const compareNodeWithProperties = <T extends { node: TreeNode }>(
  prevProps: T,
  nextProps: T
): boolean => {
  // Always re-render if node ID changes (different node)
  if (prevProps.node.id !== nextProps.node.id) {
    return false;
  }
  
  // Always re-render if properties change (form editing)
  if (prevProps.node.properties !== nextProps.node.properties) {
    return false;
  }
  
  // Allow other basic changes
  return compareBasicNodeProps(prevProps, nextProps);
};

// Container node comparison (checks children) - less strict
export const compareContainerNodeProps = <T extends { node: TreeNode }>(
  prevProps: T,
  nextProps: T
): boolean => {
  // Always re-render if node ID changes
  if (prevProps.node.id !== nextProps.node.id) {
    return false;
  }
  
  // Always re-render if children array reference changes
  if (prevProps.node.children !== nextProps.node.children) {
    return false;
  }
  
  // Allow style changes
  return compareBasicNodeProps(prevProps, nextProps);
};

// Form component comparison (node + disabled state)
export const compareFormProps = <T extends { node: TreeNode; disabled?: boolean }>(
  prevProps: T,
  nextProps: T
): boolean => {
  return (
    compareBasicNodeProps(prevProps, nextProps) &&
    prevProps.disabled === nextProps.disabled
  );
};

// Shallow object comparison for performance
export const shallowEqual = (obj1: Record<string, unknown>, obj2: Record<string, unknown>): boolean => {
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) {
    return false;
  }
  
  for (const key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }
  
  return true;
};

// Optimized comparison cho style objects
export const compareStyles = (
  style1?: { className?: string; css?: Record<string, string | number> },
  style2?: { className?: string; css?: Record<string, string | number> }
): boolean => {
  if (!style1 && !style2) return true;
  if (!style1 || !style2) return false;
  
  return (
    style1.className === style2.className &&
    JSON.stringify(style1.css) === JSON.stringify(style2.css)
  );
};

// Types for tree node drag and hover state
export interface HoverInfo {
  nodeId: string;
  position: 'top' | 'middle' | 'bottom';
}

export interface DragState {
  isDragging: boolean;
  draggedNodeId: string | null;
}

// Tree node comparison for MouseTrackingTreeNode (includes UI state)
export const compareTreeNodeProps = <T extends { 
  node: TreeNode; 
  level: number;
  isSelected: boolean;
  isExpanded: boolean;
  currentHoverInfo?: HoverInfo | null;
  parentHighlight?: boolean;
  dragState?: DragState;
}>(
  prevProps: T,
  nextProps: T
): boolean => {
  // Always re-render if drag state changes
  if (prevProps.dragState?.isDragging !== nextProps.dragState?.isDragging) {
    return false;
  }
  
  // Always re-render if dragged node changes
  if (prevProps.dragState?.draggedNodeId !== nextProps.dragState?.draggedNodeId) {
    return false;
  }
  
  // Always re-render if hover state changes
  if (prevProps.currentHoverInfo?.nodeId !== nextProps.currentHoverInfo?.nodeId) {
    return false;
  }
  
  if (prevProps.currentHoverInfo?.position !== nextProps.currentHoverInfo?.position) {
    return false;
  }
  
  // Basic comparisons
  return (
    compareBasicNodeProps(prevProps, nextProps) &&
    prevProps.level === nextProps.level &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.isExpanded === nextProps.isExpanded &&
    prevProps.parentHighlight === nextProps.parentHighlight
  );
};

export default {
  compareBasicNodeProps,
  compareNodeWithProperties,
  compareContainerNodeProps,
  compareFormProps,
  compareTreeNodeProps,
  shallowEqual,
  compareStyles
};