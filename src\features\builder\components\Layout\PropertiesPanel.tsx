import React from 'react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useBuilderCombined } from '../../context/BuilderContext';
import { DetailTabs } from '../PropertiesPanel/DetailTabs';

interface PropertiesPanelProps {
  className?: string;
  style?: React.CSSProperties;
}

/**
 * PropertiesPanel - Main properties editing panel for the builder
 * Manages two main tabs: "Thông tin" (Post info) and "Chi tiết" (Node details)
 */
export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({ className, style }) => {
  const { post, uiState, updatePost, getSelectedNode, updateNode, setPropertiesPanelMainTab } = useBuilderCombined();
  const selectedNode = getSelectedNode();
  const editorMode = uiState?.editorMode ?? 'edit';
  const isPreviewMode = editorMode === 'preview';
  const isMoveMode = editorMode === 'move';
  const isRootSelected = selectedNode?.id === 'root';
  
  // Get active tab from context, fallback to default logic
  const getDefaultTab = () => {
    if (selectedNode?.id === 'root') return 'post';
    return 'block';
  };
  const activeTab = uiState?.temporary?.propertiesPanelMainTab ?? getDefaultTab();
  
  // Auto-switch tab when node selection changes
  React.useEffect(() => {
    if (selectedNode) {
      // Force switch to "post" tab when root is selected (content selection)
      if (selectedNode.id === 'root') {
        if (activeTab !== 'post') {
          setPropertiesPanelMainTab('post');
        }
        return;
      }
      
      // For non-root nodes, always prioritize "block" tab (like when creating new)
      if (activeTab !== 'block') {
        setPropertiesPanelMainTab('block');
      }
    }
  }, [selectedNode?.id]);

  if (isMoveMode) {
    return (
      <aside className={cn("flex flex-col", className)} style={style}>
        <div className="p-4 space-y-4">
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h3 className="font-semibold text-orange-800 mb-2">
              Chế độ Di chuyển
            </h3>
            <div className="space-y-2 text-sm text-orange-700">
              <p>• Click chọn node cần di chuyển</p>
              <p>• Di chuột đến vị trí mới</p>
              <p>• Click lại để thả node</p>
              <p>• Nhấn ESC để hủy</p>
            </div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <p className="text-sm text-yellow-800">
              ⚠️ Không thể chỉnh sửa thuộc tính trong chế độ di chuyển
            </p>
          </div>
        </div>
      </aside>
    );
  }

  return (
    <aside className={cn("flex flex-col h-full", className)} style={style}>
      <div className="flex-1 flex flex-col min-h-0">
        {/* Main Tabs - Clean underline style */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setPropertiesPanelMainTab('post')}
            className={`flex-1 py-3 px-4 text-sm font-medium transition-all duration-200 border-b-2 ${
              activeTab === 'post'
                ? 'text-blue-600 border-blue-600'
                : 'text-gray-400 border-transparent hover:text-gray-600'
            }`}
          >
            Thông tin
          </button>
          <button
            onClick={() => setPropertiesPanelMainTab('block')}
            disabled={isRootSelected}
            className={`flex-1 py-3 px-4 text-sm font-medium transition-all duration-200 border-b-2 ${
              isRootSelected
                ? 'text-gray-300 border-transparent cursor-not-allowed opacity-50'
                : activeTab === 'block'
                  ? 'text-blue-600 border-blue-600'
                  : 'text-gray-400 border-transparent hover:text-gray-600'
            }`}
          >
            Chi tiết
          </button>
        </div>

        {/* Tab Content */}
        <div className="flex-1 flex flex-col min-h-0">
          {activeTab === 'post' && (
            <div className="p-4 pb-8 space-y-4 h-full overflow-auto">
              {isPreviewMode && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
                  <p className="text-sm text-yellow-800">Đang ở chế độ xem. Chuyển sang chế độ Sửa để thay đổi.</p>
                </div>
              )}
              <div>
                <Label htmlFor="post-title">Title</Label>
                <Input
                  id="post-title"
                  value={post?.title || ''}
                  onChange={(e) => updatePost({ title: e.target.value })}
                  placeholder="Enter post title"
                  disabled={isPreviewMode}
                />
              </div>

              <div>
                <Label htmlFor="post-slug">Slug</Label>
                <Input
                  id="post-slug"
                  value={post?.slug || ''}
                  onChange={(e) => updatePost({ slug: e.target.value })}
                  placeholder="post-slug"
                  disabled={isPreviewMode}
                />
              </div>

              <div>
                <Label>Status</Label>
                <div className="mt-1">
                  <Badge variant={post?.status === 'PUBLISHED' ? 'default' : 'secondary'}>
                    {post?.status || 'DRAFT'}
                  </Badge>
                </div>
              </div>

              <div>
                <Label>Created</Label>
                <p className="text-sm text-gray-600">
                  {post?.createdAt ? new Date(post.createdAt).toLocaleString() : '-'}
                </p>
              </div>

              <div>
                <Label>Updated</Label>
                <p className="text-sm text-gray-600">
                  {post?.updatedAt ? new Date(post.updatedAt).toLocaleString() : '-'}
                </p>
              </div>
            </div>
          )}

          {activeTab === 'block' && (
            <div className="flex-1 pb-8" style={{ overflowY: 'auto', maxHeight: '100%' }}>
              {selectedNode ? (
                <DetailTabs 
                  node={selectedNode}
                  onChange={(updates) => updateNode(selectedNode.id, updates)}
                  disabled={isPreviewMode}
                />
              ) : (
                <div className="text-center text-gray-500 mt-8">
                  <p>Select a node to edit its properties</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </aside>
  );
};