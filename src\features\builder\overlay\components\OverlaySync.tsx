import { useEffect, useRef } from 'react';
import { useBuilder } from '../../context/BuilderContext';
import { useOverlay } from '../context/OverlayContext';

/**
 * Component để đồng bộ state giữa Builder context và Overlay context
 */
export const OverlaySync: React.FC = () => {
  const { uiState, post } = useBuilder();
  const { updateSelection, updateEditorMode, state } = useOverlay();
  const prevPostRef = useRef(post);

  // Sync selectedNodeId from builder to overlay
  useEffect(() => {
    const builderSelected = uiState?.selectedNodeId || null;
    if (state.selectedNodeId !== builderSelected) {
      updateSelection(builderSelected);
    }
  }, [uiState?.selectedNodeId, state.selectedNodeId, updateSelection]);

  // Sync editorMode from builder to overlay
  useEffect(() => {
    const builderMode = uiState?.editorMode || 'edit';
    if (state.editorMode !== builderMode) {
      updateEditorMode(builderMode);
    }
  }, [uiState?.editorMode, state.editorMode, updateEditorMode]);

  // Listen for post content changes and trigger overlay update
  useEffect(() => {
    if (post && prevPostRef.current !== post) {
      // Post content changed, trigger overlay update by dispatching custom event
      const event = new CustomEvent('builder-content-changed', { 
        detail: { post } 
      });
      window.dispatchEvent(event);
      prevPostRef.current = post;
    }
  }, [post]);

  return null; // This component doesn't render anything
};