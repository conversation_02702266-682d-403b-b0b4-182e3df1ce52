import React from "react";
import { AppearancePopover } from "../../../inputs/AppearancePopover";
import type { RoundedOption } from "./types";

interface CornersControlProps {
  value: RoundedOption;
  onValueChange: (value: RoundedOption) => void;
  disabled?: boolean;
}

export const CornersControl: React.FC<CornersControlProps> = ({
  value,
  onValueChange,
  disabled,
}) => {
  // Create options for each corner dynamically
  const getCornerOptions = (corner: string) => [
    {
      id: "corner",
      label: "Corner",
      options: [
        {
          value: `rounded-${corner}-none`,
          label: "Không có",
          description: "0px",
        },
        { value: `rounded-${corner}-sm`, label: "sm", description: "2px" },
        { value: `rounded-${corner}-md`, label: "md", description: "6px" },
        { value: `rounded-${corner}-lg`, label: "lg", description: "8px" },
        { value: `rounded-${corner}-xl`, label: "xl", description: "12px" },
        { value: `rounded-${corner}-2xl`, label: "2xl", description: "16px" },
        { value: `rounded-${corner}-3xl`, label: "3xl", description: "24px" },
        {
          value: `rounded-${corner}-full`,
          label: "full",
          description: "9999px",
        },
      ],
    },
  ];

  const handleCornerChange = (
    corner: "tl" | "tr" | "br" | "bl",
    newValue: string
  ) => {
    // Extract size from value like "rounded-tl-sm" -> "sm"
    const prefix = `rounded-${corner}-`;
    let size = "sm";

    if (newValue.startsWith(prefix)) {
      size = newValue.substring(prefix.length) || "sm";
    }

    onValueChange({
      ...value,
      [corner]: size,
    });
  };

  return (
    <div className="grid grid-cols-2 gap-2">
      {/* Top Left */}
      <div className="flex items-center gap-1">
        <span className="text-xs text-gray-600 w-6">TL</span>
        <AppearancePopover
          value={`rounded-tl-${value.tl || "sm"}`}
          onValueChange={(val) => handleCornerChange("tl", val)}
          groups={getCornerOptions("tl")}
          placeholder="sm"
          disabled={disabled}
          className="flex-1 h-6 text-xs"
        />
      </div>

      {/* Top Right */}
      <div className="flex items-center gap-1">
        <span className="text-xs text-gray-600 w-6">TR</span>
        <AppearancePopover
          value={`rounded-tr-${value.tr || "sm"}`}
          onValueChange={(val) => handleCornerChange("tr", val)}
          groups={getCornerOptions("tr")}
          placeholder="sm"
          disabled={disabled}
          className="flex-1 h-6 text-xs"
        />
      </div>

      {/* Bottom Left */}
      <div className="flex items-center gap-1">
        <span className="text-xs text-gray-600 w-6">BL</span>
        <AppearancePopover
          value={`rounded-bl-${value.bl || "sm"}`}
          onValueChange={(val) => handleCornerChange("bl", val)}
          groups={getCornerOptions("bl")}
          placeholder="sm"
          disabled={disabled}
          className="flex-1 h-6 text-xs"
        />
      </div>

      {/* Bottom Right */}
      <div className="flex items-center gap-1">
        <span className="text-xs text-gray-600 w-6">BR</span>
        <AppearancePopover
          value={`rounded-br-${value.br || "sm"}`}
          onValueChange={(val) => handleCornerChange("br", val)}
          groups={getCornerOptions("br")}
          placeholder="sm"
          disabled={disabled}
          className="flex-1 h-6 text-xs"
        />
      </div>
    </div>
  );
};
