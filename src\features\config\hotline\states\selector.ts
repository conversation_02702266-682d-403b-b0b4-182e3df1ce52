import { createSelector } from "@reduxjs/toolkit";
import type { RootState } from "@/store/rootReducer";
import type { HotlineState } from "./type";

// Base selectors
export const selectHotlineState = (state: RootState): HotlineState =>
  state.hotlineState;

// Data selectors
export const selectHotlineData = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.data || null
);

export const selectHotlineSavedData = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.savedData || null
);

export const selectHotlineLoading = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.loading
);

export const selectHotlineSaving = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.saving
);

export const selectHotlineError = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.error
);

export const selectHotlineIsDirty = createSelector(
  [selectHotlineState],
  (hotlineState) => hotlineState.isDirty
);

// Utility selectors
export const selectHotlineIsOperating = createSelector(
  [selectHotlineLoading, selectHotlineSaving],
  (loading, saving) => loading || saving
);
