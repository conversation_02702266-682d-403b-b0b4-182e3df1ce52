import { useContext, useCallback, useMemo } from 'react';
import { BuilderContext } from './context';
import { TreeNode } from './types';
import { Excerpt } from '../states/posts';
import { useData } from './DataContext';
import { useUI } from './UIContext';
import { useActions } from './ActionsContext';

/**
 * Custom hooks for Builder functionality
 */

// Main hook - access all context values
export const useBuilder = () => {
  const context = useContext(BuilderContext);
  if (!context) {
    throw new Error('useBuilder must be used within BuilderProvider');
  }
  return context;
};

// Get selected node
export const useSelectedNode = (): TreeNode | null => {
  const { getSelectedNode } = useData();
  return getSelectedNode();
};

// Node operations
export const useNodeOperations = () => {
  const { addNode, updateNode, deleteNode, moveNode } = useActions();
  return { addNode, updateNode, deleteNode, moveNode };
};

// UI state and controls
export const useBuilderUI = () => {
  const { 
    uiState, 
    setViewMode, 
    setEditorMode, 
    selectNode, 
    toggleNodeExpansion 
  } = useUI();
  
  return {
    ...uiState,
    setViewMode,
    setEditorMode,
    selectNode,
    toggleNodeExpansion
  };
};

// History operations
export const useHistory = () => {
  const { undo, redo, canUndo, canRedo } = useActions();
  
  return {
    undo,
    redo,
    canUndo,
    canRedo
  };
};

// Post operations
export const usePost = () => {
  const { post } = useData();
  const { updatePost, savePost } = useActions();
  
  const updateTitle = useCallback((title: string) => {
    updatePost({ title });
  }, [updatePost]);
  
  const updateSlug = useCallback((slug: string) => {
    updatePost({ slug });
  }, [updatePost]);
  
  const updateExcerpt = useCallback((excerptUpdate: Partial<Excerpt>) => {
    if (!post) return;
    
    const currentExcerpt = post.excerpt || {
      image: '',
      description: '',
      metadata: {}
    };
    
    updatePost({ 
      excerpt: { 
        ...currentExcerpt,
        ...excerptUpdate 
      } as Excerpt
    });
  }, [updatePost, post]);
  
  return {
    post,
    updatePost,
    updateTitle,
    updateSlug,
    updateExcerpt,
    savePost
  };
};

// Auto-save status
export const useAutoSaveStatus = () => {
  const { uiState } = useBuilder();
  
  const timeSinceLastSave = useMemo(() => {
    if (!uiState.lastSaved) return null;
    return Date.now() - uiState.lastSaved;
  }, [uiState.lastSaved]);
  
  const isSaving = uiState.isDirty && timeSinceLastSave !== null && timeSinceLastSave < 2000;
  
  return {
    isDirty: uiState.isDirty,
    lastSaved: uiState.lastSaved,
    timeSinceLastSave,
    isSaving
  };
};

// Node path hook
export const useNodePath = (nodeId: string | null) => {
  const { getNodePath } = useData();
  
  return useMemo(() => {
    if (!nodeId) return [];
    return getNodePath(nodeId);
  }, [nodeId, getNodePath]);
};

// Node validation hook
export const useNodeValidation = () => {
  const { findNodeById, canAddChildToNode } = useData();
  
  const canAddChild = useCallback((parentId: string) => {
    const parent = findNodeById(parentId);
    if (!parent) return false;
    return canAddChildToNode(parentId);
  }, [findNodeById, canAddChildToNode]);
  
  return {
    canAddChild
  };
};