import React from "react";
import { cn } from "@/lib/utils";
import { LibraryPanel } from "./LibraryPanel";
import { LibraryItem } from "../data/mockData";
import { toast } from "sonner";

export interface LibraryDisplayProps {
  items: LibraryItem[];
  className?: string;
}

export const LibraryDisplay: React.FC<LibraryDisplayProps> = ({
  items,
  className,
}) => {
  const onItemClick = () => {
    // Handle item click logic here
    console.log("Item clicked");
    toast.success("Đã click vào mục xem thêm");
  };
  return (
    <div className={cn("flex flex-col gap-12", className)}>
      <LibraryPanel
        type="image"
        items={items}
        title="Hình ảnh nổi bật"
        description="Đại biểu Quốc hội tỉnh tiếp xúc cử tri 5 xã, phường của thành phố Nha Trang Đại biểu <PERSON>ố<PERSON> hội tỉnh tiếp xúc cử tri 5 xã, phường của thành phố ..."
        onViewMoreClick={onItemClick}
      />
      <LibraryPanel
        type="video"
        items={items}
        title="Video nổi bật"
        description="Đại biểu Quốc hội tỉnh tiếp xúc cử tri 5 xã, phường của thành phố Nha Trang Đại biểu Quốc hội tỉnh tiếp xúc cử tri 5 xã, phường của thành phố ..."
        onViewMoreClick={onItemClick}
      />
    </div>
  );
};
