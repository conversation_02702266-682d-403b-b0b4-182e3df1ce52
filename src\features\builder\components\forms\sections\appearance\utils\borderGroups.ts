// Border value groups organized by Tailwind CSS documentation structure

export interface BorderOption {
  value: string;
  label: string;
  description?: string;
}

export interface BorderGroup {
  id: string;
  label: string;
  options: BorderOption[];
}

// Border types
export const BORDER_TYPE_OPTIONS: BorderOption[] = [
  { value: 'none', label: 'Không viền' },
  { value: 'border', label: 'Ngoài (border)' },
  { value: 'outline', label: 'Giữa (outline)' },
  { value: 'ring', label: 'Trong (ring)' },
];

// Border widths for each type
export const BORDER_WIDTH_GROUPS: BorderGroup[] = [
  {
    id: 'border-widths',
    label: 'Độ dày',
    options: [
      { value: '0', label: '0px' },
      { value: '1', label: '1px' },
      { value: '2', label: '2px' },
      { value: '4', label: '4px' },
      { value: '8', label: '8px' },
    ]
  }
];

// Border styles (chỉ áp dụng cho border, không dùng cho outline/ring)
export const BORDER_STYLE_OPTIONS: BorderOption[] = [
  { value: 'solid', label: 'Liền' },
  { value: 'dashed', label: 'Gạch' },
  { value: 'dotted', label: 'Chấm' },
  { value: 'double', label: 'Đôi' },
];

// Border radius options - theo Tailwind CSS
export const BORDER_RADIUS_GROUPS: BorderGroup[] = [
  {
    id: 'border-radius',
    label: 'Bo góc',
    options: [
      { value: 'none', label: 'Không' },
      { value: 'sm', label: 'Nhỏ' },
      { value: 'md', label: 'Vừa' },
      { value: 'lg', label: 'Lớn' },
      { value: 'xl', label: 'XL' },
      { value: '2xl', label: '2XL' },
      { value: '3xl', label: '3XL' },
      { value: 'full', label: 'Tròn' },
    ]
  }
];

// For individual corners
export const CORNER_RADIUS_GROUPS: BorderGroup[] = [
  {
    id: 'tl',
    label: 'Trên trái',
    options: BORDER_RADIUS_GROUPS[0].options
  },
  {
    id: 'tr', 
    label: 'Trên phải',
    options: BORDER_RADIUS_GROUPS[0].options
  },
  {
    id: 'br',
    label: 'Dưới phải', 
    options: BORDER_RADIUS_GROUPS[0].options
  },
  {
    id: 'bl',
    label: 'Dưới trái',
    options: BORDER_RADIUS_GROUPS[0].options
  }
];

// Tailwind color options for borders
export const BORDER_COLOR_GROUPS: BorderGroup[] = [
  {
    id: 'border-grays',
    label: 'Màu xám',
    options: [
      { value: 'transparent', label: 'Trong suốt' },
      { value: 'black', label: 'Đen' },
      { value: 'white', label: 'Trắng' },
      { value: 'gray-200', label: 'Xám nhạt' },
      { value: 'gray-400', label: 'Xám vừa' },
      { value: 'gray-600', label: 'Xám đậm' },
      { value: 'gray-800', label: 'Xám rất đậm' },
    ]
  },
  {
    id: 'border-colors',
    label: 'Màu sắc',
    options: [
      { value: 'red-500', label: 'Đỏ' },
      { value: 'orange-500', label: 'Cam' },
      { value: 'yellow-500', label: 'Vàng' },
      { value: 'green-500', label: 'Xanh lá' },
      { value: 'blue-500', label: 'Xanh dương' },
      { value: 'indigo-500', label: 'Chàm' },
      { value: 'purple-500', label: 'Tím' },
      { value: 'pink-500', label: 'Hồng' },
    ]
  }
];