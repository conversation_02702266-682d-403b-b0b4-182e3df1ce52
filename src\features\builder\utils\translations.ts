// Vietnamese translations for node types and UI elements
import React from 'react';
import { FrameIcon, TypeIcon, ImageIcon, VideoIcon } from 'lucide-react';

export const nodeTypeTranslations = {
  frame: 'Khung',
  text: '<PERSON><PERSON><PERSON> bản',
  image: 'Ảnh',
  video: 'Video'
} as const;

export const getNodeTypeLabel = (type: string): string => {
  return nodeTypeTranslations[type as keyof typeof nodeTypeTranslations] || type;
};

export const getNodeTypeIcon = (type: string): React.ComponentType<{ className?: string }> => {
  const icons = {
    frame: FrameIcon,
    text: TypeIcon,
    image: ImageIcon,
    video: VideoIcon
  };
  return icons[type as keyof typeof icons] || FrameIcon;
};