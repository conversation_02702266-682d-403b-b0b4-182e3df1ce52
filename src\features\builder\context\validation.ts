import { TreeNode, TreeNodeType } from './types';
import { isDescendantOf } from '../utils/tree';

/**
 * Node validation rules and functions
 */

export const nodeValidation = {
  // Check if a parent can accept a child of given type
  canAddChild: (parentNode: TreeNode, childType: TreeNodeType): boolean => {
    // Only frame nodes can have children
    if (parentNode.type !== 'frame') {
      return false;
    }
    
    // Allow all child types in frame nodes
    void childType;
    
    return true;
  },
  
  // Check if a node can be deleted
  canDelete: (node: TreeNode): boolean => {
    // Root node cannot be deleted
    if (node.id === 'root') {
      return false;
    }
    
    // Add more rules as needed
    // For example: nodes with specific properties might be protected
    
    return true;
  },
  
  // Check if a node can be moved to a new parent
  canMove: (node: TreeNode, targetParent: TreeNode): boolean => {
    // Cannot move root
    if (node.id === 'root') {
      return false;
    }
    
    // Cannot move to non-frame nodes
    if (targetParent.type !== 'frame') {
      return false;
    }
    
    // Cannot move node into its own descendants
    const isDescendant = isDescendantOf(node, targetParent.id);
    if (isDescendant) {
      return false;
    }
    
    // Cannot move node to itself
    if (node.id === targetParent.id) {
      return false;
    }
    
    return true;
  },
  
  // Validate node structure
  validateNode: (node: Partial<TreeNode>): string[] => {
    const errors: string[] = [];

    if (!node.id || node.id.trim() === '') {
      errors.push('Node must have an ID');
    }

    if (!node.label || node.label.trim() === '') {
      errors.push('Node must have a label');
    }

    if (!node.type) {
      errors.push('Node must have a type');
    }

    if (!node.group) {
      errors.push('Node must have a group');
    }

    // Validate children array exists
    if (node.type === 'frame' && !Array.isArray(node.children)) {
      errors.push('Frame nodes must have a children array');
    }

    // Non-frame nodes should not have children
    if (node.type !== 'frame' && node.children && node.children.length > 0) {
      errors.push(`${node.type} nodes cannot have children`);
    }

    // Validate specific node type properties
    if (node.type === 'libraryDisplay' && node.properties) {
      const props = node.properties;

      // Validate image position
      if (props.imagePosition && !['left', 'right'].includes(props.imagePosition as string)) {
        errors.push('LibraryDisplay imagePosition must be "left" or "right"');
      }

      // Validate button URL format (basic check)
      if (props.buttonUrl && typeof props.buttonUrl === 'string' && props.buttonUrl.trim() !== '') {
        const url = props.buttonUrl as string;
        if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('#') && !url.startsWith('/')) {
          errors.push('LibraryDisplay buttonUrl should be a valid URL or relative path');
        }
      }
    }

    return errors;
  },
  
  // Check if node type can have children
  canHaveChildren: (type: TreeNodeType): boolean => {
    return type === 'frame';
  },
  
  // Validate tree structure recursively
  validateTree: (root: TreeNode): string[] => {
    const errors: string[] = [];
    const visitedIds = new Set<string>();
    
    const validateRecursive = (node: TreeNode, path: string): void => {
      // Check for duplicate IDs
      if (visitedIds.has(node.id)) {
        errors.push(`Duplicate node ID found: ${node.id} at ${path}`);
      }
      visitedIds.add(node.id);
      
      // Validate current node
      const nodeErrors = nodeValidation.validateNode(node);
      errors.push(...nodeErrors.map(e => `${e} at ${path}`));
      
      // Validate children
      if (node.children) {
        node.children.forEach((child, index) => {
          validateRecursive(child, `${path}/${child.label}[${index}]`);
        });
      }
    };
    
    validateRecursive(root, 'root');
    return errors;
  }
};