import { TreeNode } from '../context/types';
import { DropPosition } from '../types/drag-drop';

/**
 * Calculate the correct index for inserting a node after move/drag operation
 * 
 * @param draggedNodeId - ID of the node being moved
 * @param targetNodeId - ID of the target node (where indicator is shown)
 * @param position - Position relative to target (before/after/inside)
 * @param parentNode - Parent node containing both dragged and target
 * @returns The correct index for insertion
 */
export const calculateMoveIndex = (
  draggedNodeId: string,
  targetNodeId: string,
  position: DropPosition,
  parentNode: TreeNode
): number => {
  const children = parentNode.children;
  const draggedIndex = children.findIndex(child => child.id === draggedNodeId);
  const targetIndex = children.findIndex(child => child.id === targetNodeId);
  
  // If position is 'inside', always insert at beginning
  if (position === 'inside') {
    return 0;
  }
  
  // If nodes are in different parents, simple calculation
  if (draggedIndex === -1) {
    return position === 'before' ? targetIndex : targetIndex + 1;
  }
  
  // Nodes are in same parent - need to adjust for removal
  let newIndex: number;
  
  if (position === 'before') {
    newIndex = targetIndex;
    // If dragging from after to before, no adjustment needed
    // If dragging from before to after-1, no adjustment needed
  } else { // position === 'after'
    newIndex = targetIndex + 1;
  }
  
  // Adjust for the removal of dragged node
  if (draggedIndex < newIndex) {
    // When dragging from before to after, the removal shifts indices down
    newIndex--;
  }
  
  return newIndex;
};

/**
 * Check if a move would result in no position change
 */
export const isSamePosition = (
  draggedNodeId: string,
  targetNodeId: string,
  position: DropPosition,
  parentNode: TreeNode
): boolean => {
  const children = parentNode.children;
  const draggedIndex = children.findIndex(child => child.id === draggedNodeId);
  const targetIndex = children.findIndex(child => child.id === targetNodeId);
  
  if (draggedIndex === -1) return false;
  
  // Check if it would end up in the same position
  if (position === 'before') {
    return draggedIndex === targetIndex - 1 || draggedIndex === targetIndex;
  } else if (position === 'after') {
    return draggedIndex === targetIndex + 1 || draggedIndex === targetIndex;
  }
  
  return false;
};