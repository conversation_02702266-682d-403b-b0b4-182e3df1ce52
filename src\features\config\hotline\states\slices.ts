import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import type { HotlineConfig, HotlineState } from "./type";
import { fetchHotlineConfig, updateHotlineConfig } from "./api";

// Initial state
const initialState: HotlineState = {
  data: null,
  savedData: null,
  loading: false,
  saving: false,
  error: null,
  isDirty: false,
};

// Async thunks
export const fetchHotlineAsync = createAsyncThunk(
  "hotline/fetchHotline",
  async () => {
    const response = await fetchHotlineConfig();
    return response.data;
  }
);

export const updateHotlineAsync = createAsyncThunk(
  "hotline/updateHotline",
  async (data: HotlineConfig) => {
    const response = await updateHotlineConfig(data);
    return response.data;
  }
);

// Slice
const hotlineSlice = createSlice({
  name: "hotline",
  initialState,
  reducers: {
    setHotlineData: (state, action) => {
      state.data = action.payload;
      state.isDirty = true;
    },
    setDirty: (state, action) => {
      state.isDirty = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch hotline
      .addCase(fetchHotlineAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchHotlineAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload || null;
        state.savedData = action.payload || null;
        state.isDirty = false;
      })
      .addCase(fetchHotlineAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch hotline data";
      })
      // Update hotline
      .addCase(updateHotlineAsync.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updateHotlineAsync.fulfilled, (state, action) => {
        state.saving = false;
        state.data = action.payload || null;
        state.savedData = action.payload || null;
        state.isDirty = false;
      })
      .addCase(updateHotlineAsync.rejected, (state, action) => {
        state.saving = false;
        state.error = action.error.message || "Failed to update hotline data";
      });
  },
});

export const { setHotlineData, setDirty, clearError } = hotlineSlice.actions;
export default hotlineSlice.reducer;
