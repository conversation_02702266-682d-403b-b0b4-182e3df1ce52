import React from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Plus, X, Check, Edit2 } from 'lucide-react';
import { TreeNode } from '../../context/types';

interface CSSProperty {
  key: string;
  value: string;
}

interface CSSPropertyItemProps {
  property: CSSProperty;
  isNew?: boolean;
  onSave?: (key: string, value: string) => void;
  onEdit?: () => void;
  onDelete?: () => void;
  disabled?: boolean;
}

interface CSSPropertiesEditorProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

// Single CSS Property Item Component
const CSSPropertyItem: React.FC<CSSPropertyItemProps> = ({
  property,
  isNew = false,
  onSave,
  onEdit,
  onDelete,
  disabled
}) => {
  const [isEditing, setIsEditing] = React.useState(isNew);
  const [key, setKey] = React.useState(property.key);
  const [value, setValue] = React.useState(property.value);

  // Reset state when property changes (for undo/redo)
  React.useEffect(() => {
    setKey(property.key);
    setValue(property.value);
    if (!isNew) {
      setIsEditing(false);
    }
  }, [property.key, property.value, isNew]);

  const handleSave = () => {
    if (key.trim() && value.trim() && onSave) {
      onSave(key.trim(), value.trim());
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    if (isNew && onDelete) {
      onDelete();
    } else {
      setKey(property.key);
      setValue(property.value);
      setIsEditing(false);
    }
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    }
    setIsEditing(true);
  };

  if (isEditing) {
    return (
      <div className="space-y-2 p-3 bg-gray-50 rounded-md border border-gray-200">
        <Textarea
          value={key}
          onChange={(e) => setKey(e.target.value)}
          placeholder="property-name"
          className="min-h-[32px] resize-none text-xs font-mono"
          rows={1}
          disabled={disabled}
        />
        <Textarea
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder="value"
          className="min-h-[32px] resize-none text-xs font-mono"
          rows={1}
          disabled={disabled}
        />
        <div className="flex gap-2 justify-end">
          <Button
            size="sm"
            variant="ghost"
            onClick={handleCancel}
            disabled={disabled}
            className="h-7 px-2 text-xs"
          >
            Hủy
          </Button>
          <Button
            size="sm"
            variant="default"
            onClick={handleSave}
            disabled={disabled || !key.trim() || !value.trim()}
            className="h-7 px-2 text-xs"
          >
            <Check className="h-3 w-3 mr-1" />
            Lưu
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="group flex items-start gap-2 p-2 hover:bg-gray-50 rounded">
      <div className="flex-1 space-y-1">
        <div className="text-xs font-mono text-gray-700">{property.key}</div>
        <div className="text-xs font-mono text-gray-500 break-all">{property.value}</div>
      </div>
      <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          size="sm"
          variant="ghost"
          onClick={handleEdit}
          disabled={disabled}
          className="h-6 w-6 p-0"
          title="Sửa"
        >
          <Edit2 className="h-3 w-3" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={onDelete}
          disabled={disabled}
          className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
          title="Xóa"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
};

// Main CSS Properties Editor Component
const CSSPropertiesEditorComponent: React.FC<CSSPropertiesEditorProps> = ({
  node,
  onChange,
  disabled
}) => {
  // Always read properties directly from node - no local state
  const properties = React.useMemo(() => {
    if (node.style?.css) {
      return Object.entries(node.style.css).map(([key, value]) => ({
        key: key.replace(/([A-Z])/g, '-$1').toLowerCase(),
        value: value as string
      }));
    }
    return [];
  }, [node.style?.css]);

  const [newProperty, setNewProperty] = React.useState<CSSProperty | null>(null);

  // Reset when node changes
  React.useEffect(() => {
    setNewProperty(null);
  }, [node.id]);

  const saveToNode = (updatedProperties: CSSProperty[]) => {
    // Convert to camelCase for CSS object
    const css = updatedProperties.reduce((acc, prop) => {
      if (prop.key && prop.value) {
        const camelKey = prop.key.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
        acc[camelKey] = prop.value;
      }
      return acc;
    }, {} as Record<string, string>);

    // Update node with history support
    onChange({
      style: {
        ...node.style,
        css: Object.keys(css).length > 0 ? css : undefined
      }
    });
  };

  const handleAddNew = () => {
    setNewProperty({ key: '', value: '' });
  };

  const handleSaveNew = (key: string, value: string) => {
    const updatedProperties = [...properties, { key, value }];
    saveToNode(updatedProperties);
    setNewProperty(null);
  };

  const handleCancelNew = () => {
    setNewProperty(null);
  };

  const handleEdit = (index: number, key: string, value: string) => {
    const updatedProperties = [...properties];
    updatedProperties[index] = { key, value };
    saveToNode(updatedProperties);
  };

  const handleDelete = (index: number) => {
    const updatedProperties = properties.filter((_, i) => i !== index);
    saveToNode(updatedProperties);
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <label className="text-sm font-semibold">CSS Properties</label>
        {!newProperty && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleAddNew}
            disabled={disabled}
            className="h-7 px-2 text-xs"
          >
            <Plus className="h-3 w-3 mr-1" />
            Thêm
          </Button>
        )}
      </div>

      <div className="space-y-1">
        {properties.length === 0 && !newProperty ? (
          <div className="text-center py-6 text-gray-500 bg-gray-50 rounded-md border-2 border-dashed">
            <p className="text-xs">Chưa có CSS properties nào</p>
            <p className="text-xs">Nhấn "Thêm" để thêm property</p>
          </div>
        ) : (
          <>
            {properties.map((prop, index) => (
              <CSSPropertyItem
                key={`${node.id}-${index}`}
                property={prop}
                isNew={false}
                onSave={(key, value) => handleEdit(index, key, value)}
                onEdit={() => {}}
                onDelete={() => handleDelete(index)}
                disabled={disabled}
              />
            ))}
            {newProperty && (
              <CSSPropertyItem
                property={newProperty}
                isNew
                onSave={handleSaveNew}
                onDelete={handleCancelNew}
                disabled={disabled}
              />
            )}
          </>
        )}
      </div>

      <p className="text-xs text-gray-500">
        CSS properties sẽ được áp dụng trực tiếp vào element.
      </p>
    </div>
  );
};

// Memoized CSS Properties Editor để prevent re-renders
export const CSSPropertiesEditor = React.memo(
  CSSPropertiesEditorComponent,
  (prevProps, nextProps) => {
    return (
      prevProps.node.id === nextProps.node.id &&
      prevProps.disabled === nextProps.disabled &&
      // Deep compare CSS properties
      JSON.stringify(prevProps.node.style?.css) === JSON.stringify(nextProps.node.style?.css)
    );
  }
);