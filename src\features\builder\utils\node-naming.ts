import { TreeNode, TreeNodeType } from '../context/types';

/**
 * Node naming manager with instance-based state
 */
export class NodeNamingManager {
  private nodeCounters: Record<string, number> = {};

  /**
   * Analyze existing tree to find max counter for each node type
   */
  private analyzeTreeCounters(node: TreeNode, counters: Record<string, number> = {}): Record<string, number> {
    // Extract number from node label with format: "nodeType-number" (e.g., "frame-1", "text-3")
    const match = node.label.match(/^([a-zA-Z]+)-(\d+)$/);
    if (match) {
      const labelType = match[1].toLowerCase();
      const num = parseInt(match[2], 10);
      
      // Only count if the label type matches the node type
      if (labelType === node.type.toLowerCase()) {
        const currentMax = counters[node.type] || 0;
        counters[node.type] = Math.max(currentMax, num);
      }
    }
    
    // Recursively analyze children
    if (node.children) {
      for (const child of node.children) {
        this.analyzeTreeCounters(child, counters);
      }
    }
    
    return counters;
  }

  /**
   * Initialize counters by analyzing current post tree
   */
  initializeFromPost(rootNode: TreeNode | null): void {
    this.nodeCounters = {};
    if (rootNode) {
      this.nodeCounters = this.analyzeTreeCounters(rootNode);
    }
  }

  /**
   * Generate next node name with format: nodeType-counter (e.g., frame-1, text-2)
   */
  generateNextNodeName(type: TreeNodeType): string {
    const currentMax = this.nodeCounters[type] || 0;
    const nextNumber = currentMax + 1;
    
    // Update counter
    this.nodeCounters[type] = nextNumber;
    
    return `${type.toLowerCase()}-${nextNumber}`;
  }

  /**
   * Debug function to get current counters
   */
  getCurrentCounters(): Record<string, number> {
    return { ...this.nodeCounters };
  }
}

// Create a singleton instance for backward compatibility
const globalNodeNamingManager = new NodeNamingManager();

// Export functions that use the singleton (for backward compatibility)
export const initializeCountersFromPost = (rootNode: TreeNode | null): void => {
  globalNodeNamingManager.initializeFromPost(rootNode);
};

export const generateNextNodeName = (type: TreeNodeType): string => {
  return globalNodeNamingManager.generateNextNodeName(type);
};

export const getCurrentCounters = (): Record<string, number> => {
  return globalNodeNamingManager.getCurrentCounters();
};