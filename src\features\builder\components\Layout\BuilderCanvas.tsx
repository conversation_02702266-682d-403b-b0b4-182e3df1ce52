import React, { useRef } from 'react';
import { cn } from '@/lib/utils';
import { useUI } from '../../context/UIContext';
import { useCanvasMove } from '../../hooks/useCanvasMove';
import { OverlayLayer } from '../../overlay';

interface BuilderCanvasProps {
  className?: string;
  children?: React.ReactNode;
}

export const BuilderCanvas: React.FC<BuilderCanvasProps> = ({ 
  className, 
  children
}) => {
  const { uiState, selectNode } = useUI();
  const { handleNodeClick } = useCanvasMove();
  const canvasContentRef = useRef<HTMLElement>(null);
  const viewMode = uiState?.viewMode ?? 'desktop';
  const editorMode = uiState?.editorMode ?? 'edit';

  // Canvas sizes based on view mode
  const canvasSize = {
    mobile: 'max-w-sm',
    tablet: 'max-w-2xl', 
    desktop: 'max-w-7xl'
  };

  // Event delegation: handle all clicks
  const handleCanvasClick = (e: React.MouseEvent) => {
    // Find closest element with data-node-id
    const target = e.target as HTMLElement;
    const nodeElement = target.closest('[data-node-id]') as HTMLElement;
    
    if (nodeElement) {
      const nodeId = nodeElement.getAttribute('data-node-id');
      if (nodeId) {
        e.stopPropagation();
        
        if (editorMode === 'move') {
          // Handle move mode
          handleNodeClick(nodeId);
        } else {
          // Handle selection
          selectNode(nodeId);
        }
        return;
      }
    }
    
    // Click on canvas background
    if (editorMode === 'move' && uiState?.selectedNodeId) {
      handleNodeClick('');
    } else {
      // Deselect current node
      selectNode(null);
    }
  };

  return (
    <div 
      className={cn("bg-gray-100 overflow-auto", className)}
      onClick={handleCanvasClick}
    >
      {/* Canvas Container - Extra padding for drop-shadows */}
      <div className="min-h-full flex items-start justify-center p-16">
        <div 
          className={cn(
            "bg-white shadow-lg transition-all duration-300 mx-auto",
            canvasSize[viewMode],
            "w-full min-h-[600px]",
            editorMode === 'preview' && "ring-2 ring-blue-200"
          )}
        >
          {/* Canvas Header */}
          <div className="border-b px-4 py-2 bg-gray-50">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">
                {viewMode === 'mobile' && '375px / Điện thoại'}
                {viewMode === 'tablet' && '768px / Máy tính bảng'}
                {viewMode === 'desktop' && '1280px / Máy tính'}
              </span>
              <span className="text-xs text-gray-500">
                {editorMode === 'edit' && 'Chế độ sửa'}
                {editorMode === 'preview' && 'Chế độ xem'}
                {editorMode === 'move' && 'Chế độ di chuyển'}
              </span>
            </div>
          </div>

          {/* Canvas Content */}
          <div 
            ref={canvasContentRef as React.RefObject<HTMLDivElement>}
            className="p-4 relative"
          >
            {children || (
              <div className="h-96 flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <p className="text-lg mb-2">Canvas Placeholder</p>
                  <p className="text-sm">Your page content will appear here</p>
                </div>
              </div>
            )}
            <OverlayLayer canvasRef={canvasContentRef as React.RefObject<HTMLElement>} />
          </div>
        </div>
      </div>
    </div>
  );
};