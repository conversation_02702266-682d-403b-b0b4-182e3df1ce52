import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { ImageLoader } from "@/components/image/ImageLoader";
import { cn } from "@/lib/utils";
import { Post } from "@/features/post/states/types";

/**
 * Props interface for MostViewedDisplay component
 */
export interface MostViewedDisplayProps {
  /** Array of posts to display */
  posts: Post[];
  /** Maximum number of images to display */
  maxImages?: number;
  /** Additional CSS classes */
  className?: string;
  /** Click handler for the display */
  onClick?: () => void;
}

/**
 * MostViewedDisplay component shows most viewed articles with images on left and content on right
 * 
 * @param props - Component props
 * @returns JSX element for most viewed display
 */
export const MostViewedDisplay: React.FC<MostViewedDisplayProps> = ({
  posts,
  maxImages = 3,
  className,
  onClick,
}) => {
  // Get the first post for main content
  const mainPost = posts[0];
  
  // Get images from posts (up to maxImages)
  const postImages = posts
    .filter(post => post.excerpt?.image)
    .slice(0, maxImages)
    .map(post => ({
      id: post.id,
      image: post.excerpt.image,
      title: post.title,
    }));

  if (!mainPost) {
    return null;
  }

  return (
    <Card 
      className={cn(
        "overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer bg-transparent border-gray-200",
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-0">
        <div className="flex h-64">
          {/* Left Side - Images */}
          <div className="w-1/2 p-4">
            <div className="h-full grid gap-2">
              {postImages.length === 1 && (
                <div className="h-full">
                  <ImageLoader
                    src={postImages[0].image}
                    alt={postImages[0].title}
                    className="w-full h-full object-cover rounded-lg"
                    fallbackText={postImages[0].title}
                  />
                </div>
              )}
              
              {postImages.length === 2 && (
                <>
                  <div className="h-1/2">
                    <ImageLoader
                      src={postImages[0].image}
                      alt={postImages[0].title}
                      className="w-full h-full object-cover rounded-lg"
                      fallbackText={postImages[0].title}
                    />
                  </div>
                  <div className="h-1/2">
                    <ImageLoader
                      src={postImages[1].image}
                      alt={postImages[1].title}
                      className="w-full h-full object-cover rounded-lg"
                      fallbackText={postImages[1].title}
                    />
                  </div>
                </>
              )}
              
              {postImages.length >= 3 && (
                <>
                  <div className="h-1/2">
                    <ImageLoader
                      src={postImages[0].image}
                      alt={postImages[0].title}
                      className="w-full h-full object-cover rounded-lg"
                      fallbackText={postImages[0].title}
                    />
                  </div>
                  <div className="h-1/2 grid grid-cols-2 gap-2">
                    <ImageLoader
                      src={postImages[1].image}
                      alt={postImages[1].title}
                      className="w-full h-full object-cover rounded-lg"
                      fallbackText={postImages[1].title}
                    />
                    <ImageLoader
                      src={postImages[2].image}
                      alt={postImages[2].title}
                      className="w-full h-full object-cover rounded-lg"
                      fallbackText={postImages[2].title}
                    />
                  </div>
                </>
              )}
              
              {postImages.length === 0 && (
                <div className="h-full bg-gray-100 rounded-lg flex items-center justify-center">
                  <span className="text-gray-400 text-sm">Không có hình ảnh</span>
                </div>
              )}
            </div>
          </div>

          {/* Right Side - Content */}
          <div className="w-1/2 p-6 flex flex-col justify-center">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                {mainPost.title}
              </h3>
              
              <p className="text-sm text-gray-600 line-clamp-4">
                {mainPost.excerpt?.description || "Không có mô tả"}
              </p>
              
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>Tác giả: {mainPost.authorId}</span>
                <span>{new Date(mainPost.publishedAt || mainPost.createdAt).toLocaleDateString('vi-VN')}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
