import React from "react";
import { useForm } from "@tanstack/react-form";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import MapComponent from "@/components/map/MapComponent";
import SectionTitle from "@/features/post/components/SectionTitle";

/**
 * Contact form data type
 */
export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

/**
 * Props interface for ContactUsDisplay component
 */
export interface ContactUsDisplayProps {
  /** Additional CSS classes */
  className?: string;
  /** Form submission handler */
  onSubmit?: (data: ContactFormData) => void;
}

/**
 * ContactUsDisplay component with contact form on left and interactive map on right
 * 
 * @param props - Component props
 * @returns JSX element for contact us display
 */
export const ContactUsDisplay: React.FC<ContactUsDisplayProps> = ({
  className,
  onSubmit,
}) => {
  const form = useForm({
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: "",
    },
    onSubmit: async ({ value }) => {
      try {
        if (onSubmit) {
          await onSubmit(value);
        } else {
          // Default behavior - show success message
          toast.success("Gửi liên hệ thành công! Chúng tôi sẽ phản hồi sớm nhất có thể.");
        }
        form.reset();
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toast.error("Có lỗi xảy ra khi gửi liên hệ. Vui lòng thử lại.");
      }
    },
  });

  return (
    <div className="flex flex-col gap-8 p-6 bg-primary-foreground min-h-screen">
      <div className="p-4 flex items-center gap-4">
        <SectionTitle title="LIÊN HỆ VỚI CHÚNG TÔI" />
        <div className="flex-grow border-t border-gray-300" />
      </div>
      <div className={cn("grid grid-cols-1 lg:grid-cols-2 gap-8", className)}>

        {/* Left Side - Contact Form */}
        <Card className="bg-transparent border-gray-200">
          <CardContent className="p-6">
            <div className="space-y-6">

              <form onSubmit={(e) => {
                e.preventDefault();
                form.handleSubmit();
              }} className="space-y-4">
                {/* Name Field */}
                <form.Field name="name">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="name">Họ và tên *</Label>
                      <Input
                        id="name"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        placeholder="Nhập họ và tên"
                      />
                    </div>
                  )}
                </form.Field>

                {/* Email Field */}
                <form.Field name="email">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        placeholder="Nhập địa chỉ email"
                      />
                    </div>
                  )}
                </form.Field>

                {/* Phone Field */}
                <form.Field name="phone">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="phone">Số điện thoại *</Label>
                      <Input
                        id="phone"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        placeholder="Nhập số điện thoại"
                      />
                    </div>
                  )}
                </form.Field>

                {/* Subject Field */}
                <form.Field name="subject">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="subject">Chủ đề *</Label>
                      <Input
                        id="subject"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        placeholder="Nhập chủ đề"
                      />
                    </div>
                  )}
                </form.Field>

                {/* Message Field */}
                <form.Field name="message">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="message">Nội dung *</Label>
                      <Textarea
                        id="message"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        placeholder="Nhập nội dung liên hệ"
                        rows={4}
                      />
                    </div>
                  )}
                </form.Field>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={form.state.isSubmitting}
                  className="w-full"
                >
                  {form.state.isSubmitting ? "Đang gửi..." : "Gửi liên hệ"}
                </Button>
              </form>
            </div>
          </CardContent>
        </Card>

        {/* Right Side - Interactive Map */}
        <Card className="bg-transparent border-gray-200">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Vị trí của chúng tôi</h3>
                <p className="text-gray-600">05 Pastuer, phường Xương Huân, TP. Nha Trang, Khánh Hòa</p>
              </div>

              <div className="h-96">
                <MapComponent />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
