/**
 * Shadow Color Picker
 * Two-step color picker: 1) Choose color, 2) Choose opacity
 * Supports all Tailwind colors with Vietnamese labels
 */

import React, { useState } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { Label } from '@/components/ui/label';
import { SHADOW_OPACITY_OPTIONS, DEFAULT_SHADOW_OPACITY, DEFAULT_SHADOW_COLOR } from './types';

// Full Tailwind color palette with Vietnamese names
const TAILWIND_COLORS = {
  // Grays (most common for shadows)
  gray: {
    name: 'Xám',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  slate: {
    name: '<PERSON><PERSON><PERSON> đá',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  zinc: {
    name: 'Kẽm',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  neutral: {
    name: 'Trung tính',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  stone: {
    name: 'Đá',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  
  // Colors
  red: {
    name: 'Đỏ',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  orange: {
    name: 'Cam',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  amber: {
    name: 'Hổ phách',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  yellow: {
    name: 'Vàng',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  lime: {
    name: 'Vàng chanh',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  green: {
    name: 'Xanh lá',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  emerald: {
    name: 'Ngọc lục',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  teal: {
    name: 'Xanh mòng két',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  cyan: {
    name: 'Xanh lam',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  sky: {
    name: 'Xanh trời',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  blue: {
    name: 'Xanh dương',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  indigo: {
    name: 'Chàm',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  violet: {
    name: 'Tím',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  purple: {
    name: 'Tím',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  fuchsia: {
    name: 'Hồng tím',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  pink: {
    name: 'Hồng',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  },
  rose: {
    name: 'Hồng hoa',
    shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950']
  }
};

// Special colors
const SPECIAL_COLORS = [
  { key: 'black', name: 'Đen', shades: [''] },
  { key: 'white', name: 'Trắng', shades: [''] }
];

interface ShadowColorPickerProps {
  color?: string;
  opacity?: string;
  onColorChange: (color: string) => void;
  onOpacityChange: (opacity: string) => void;
  disabled?: boolean;
  placeholder?: string;
  compact?: boolean;
}

/**
 * Get display name for color
 */
const getColorDisplayName = (color: string, opacity: string): string => {
  if (!color) return 'Chưa chọn màu';
  
  // Handle special colors
  if (color === 'black') return `Đen (${opacity}%)`;
  if (color === 'white') return `Trắng (${opacity}%)`;
  
  // Handle color-shade format
  const colorParts = color.split('-');
  if (colorParts.length === 2) {
    const [colorName, shade] = colorParts;
    const colorInfo = TAILWIND_COLORS[colorName as keyof typeof TAILWIND_COLORS];
    if (colorInfo) {
      return `${colorInfo.name} ${shade} (${opacity}%)`;
    }
  }
  
  return `${color} (${opacity}%)`;
};

export const ShadowColorPicker: React.FC<ShadowColorPickerProps> = ({
  color = DEFAULT_SHADOW_COLOR,
  opacity = DEFAULT_SHADOW_OPACITY,
  onColorChange,
  onOpacityChange,
  disabled = false,
  placeholder = 'Chọn màu bóng đổ',
  compact = false
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleColorSelect = (colorValue: string) => {
    onColorChange(colorValue);
    setIsOpen(false);
  };


  if (compact) {
    return (
      <div className="grid grid-cols-2 gap-4">
        {/* Color Picker */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "justify-start w-full h-10",
                !color && "text-muted-foreground"
              )}
              disabled={disabled}
            >
              <div className="flex items-center justify-center">
                <div 
                  className={cn(
                    "w-6 h-6 rounded-full border border-gray-300 flex-shrink-0",
                    color ? `bg-${color}` : 'bg-gray-200'
                  )}
                  style={color === 'black' ? { backgroundColor: 'black' } : color === 'white' ? { backgroundColor: 'white' } : undefined}
                />
              </div>
            </Button>
          </PopoverTrigger>
          
          <PopoverContent className="w-80 p-4">
            <div className="space-y-4">
              <h4 className="text-sm font-semibold">Chọn màu bóng đổ</h4>
              
              {/* Special Colors */}
              <div className="space-y-2">
                <h5 className="text-xs font-medium text-gray-600">Màu đặc biệt</h5>
                <div className="grid grid-cols-2 gap-2">
                  {SPECIAL_COLORS.map((special) => (
                    <button
                      key={special.key}
                      onClick={() => handleColorSelect(special.key)}
                      className={cn(
                        "flex items-center gap-2 p-2 text-left hover:bg-gray-50 rounded border",
                        color === special.key && "bg-blue-50 border-blue-200"
                      )}
                    >
                      <div 
                        className="w-4 h-4 rounded-full border border-gray-300"
                        style={{ backgroundColor: special.key }}
                      />
                      <span className="text-sm">{special.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Tailwind Colors */}
              <div className="space-y-4 max-h-80 overflow-y-auto">
                {Object.entries(TAILWIND_COLORS).map(([colorKey, colorInfo]) => (
                  <div key={colorKey}>
                    <h5 className="text-xs font-medium mb-2 text-gray-600">{colorInfo.name}</h5>
                    <div className="grid grid-cols-11 gap-1">
                      {colorInfo.shades.map((shade) => {
                        const colorValue = `${colorKey}-${shade}`;
                        return (
                          <button
                            key={shade}
                            onClick={() => handleColorSelect(colorValue)}
                            className={cn(
                              `w-6 h-6 rounded-full bg-${colorKey}-${shade} hover:scale-110 transition-transform border border-gray-200`,
                              color === colorValue && "ring-2 ring-blue-500"
                            )}
                            title={`${colorInfo.name} ${shade}`}
                          />
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Clear Selection */}
              <div className="pt-2 border-t">
                <button
                  onClick={() => handleColorSelect('')}
                  className="w-full text-sm text-gray-500 hover:text-gray-700 py-2"
                >
                  Xóa màu
                </button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Opacity Picker */}
        <Select 
          value={opacity} 
          onValueChange={onOpacityChange}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue placeholder={`${opacity}%`} />
          </SelectTrigger>
          <SelectContent>
            {SHADOW_OPACITY_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.value}%
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Color Picker */}
      <div className="space-y-2">
        <Label>Màu sắc</Label>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "justify-start w-full h-10",
                !color && "text-muted-foreground"
              )}
              disabled={disabled}
            >
              <div className="flex items-center gap-2">
                <div 
                  className={cn(
                    "w-4 h-4 rounded-full border border-gray-300 flex-shrink-0",
                    color ? `bg-${color}` : 'bg-transparent'
                  )}
                  style={color === 'black' ? { backgroundColor: 'black' } : color === 'white' ? { backgroundColor: 'white' } : undefined}
                />
                <span className="truncate">
                  {color ? getColorDisplayName(color, opacity) : placeholder}
                </span>
              </div>
            </Button>
          </PopoverTrigger>
          
          <PopoverContent className="w-80 p-4">
            <div className="space-y-4">
              <h4 className="text-sm font-semibold">Chọn màu bóng đổ</h4>
              
              {/* Special Colors */}
              <div className="space-y-2">
                <h5 className="text-xs font-medium text-gray-600">Màu đặc biệt</h5>
                <div className="grid grid-cols-2 gap-2">
                  {SPECIAL_COLORS.map((special) => (
                    <button
                      key={special.key}
                      onClick={() => handleColorSelect(special.key)}
                      className={cn(
                        "flex items-center gap-2 p-2 text-left hover:bg-gray-50 rounded border",
                        color === special.key && "bg-blue-50 border-blue-200"
                      )}
                    >
                      <div 
                        className="w-4 h-4 rounded-full border border-gray-300"
                        style={{ backgroundColor: special.key }}
                      />
                      <span className="text-sm">{special.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Tailwind Colors */}
              <div className="space-y-4 max-h-80 overflow-y-auto">
                {Object.entries(TAILWIND_COLORS).map(([colorKey, colorInfo]) => (
                  <div key={colorKey}>
                    <h5 className="text-xs font-medium mb-2 text-gray-600">{colorInfo.name}</h5>
                    <div className="grid grid-cols-11 gap-1">
                      {colorInfo.shades.map((shade) => {
                        const colorValue = `${colorKey}-${shade}`;
                        return (
                          <button
                            key={shade}
                            onClick={() => handleColorSelect(colorValue)}
                            className={cn(
                              `w-6 h-6 rounded-full bg-${colorKey}-${shade} hover:scale-110 transition-transform border border-gray-200`,
                              color === colorValue && "ring-2 ring-blue-500"
                            )}
                            title={`${colorInfo.name} ${shade}`}
                          />
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Clear Selection */}
              <div className="pt-2 border-t">
                <button
                  onClick={() => handleColorSelect('')}
                  className="w-full text-sm text-gray-500 hover:text-gray-700 py-2"
                >
                  Xóa màu
                </button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Opacity Picker - Only show when color is selected */}
      {color && (
        <div className="space-y-2">
          <Label>Độ trong suốt</Label>
          <Select 
            value={opacity} 
            onValueChange={onOpacityChange}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {SHADOW_OPACITY_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
    </div>
  );
};
