import { TreeNode } from "../context/types";
import { cloneState } from "../../../lib/immer-utils";

/**
 * Tree traversal and manipulation utilities
 */

// Find a node by ID in the tree
export const findNodeById = (root: TreeNode, id: string): TreeNode | null => {
  if (root.id === id) return root;

  for (const child of root.children) {
    const found = findNodeById(child, id);
    if (found) return found;
  }

  return null;
};

// Find parent node of a given child
export const findParentNode = (
  root: TreeNode,
  childId: string
): TreeNode | null => {
  for (const child of root.children) {
    if (child.id === childId) return root;

    const found = findParentNode(child, childId);
    if (found) return found;
  }

  return null;
};

// Get path from root to a specific node
export const getNodePath = (root: TreeNode, nodeId: string): TreeNode[] => {
  if (root.id === nodeId) return [root];

  for (const child of root.children) {
    const path = getNodePath(child, nodeId);
    if (path.length > 0) {
      return [root, ...path];
    }
  }

  return [];
};

// Delete a node from the tree
export const deleteNodeFromTree = (root: TreeNode, nodeId: string): boolean => {
  const index = root.children.findIndex((child) => child.id === nodeId);

  if (index > -1) {
    root.children.splice(index, 1);
    return true;
  }

  for (const child of root.children) {
    if (deleteNodeFromTree(child, nodeId)) return true;
  }

  return false;
};

// Count total nodes in tree
export const countNodes = (root: TreeNode): number => {
  let count = 1;
  for (const child of root.children) {
    count += countNodes(child);
  }
  return count;
};

// Get all node IDs in tree
export const getAllNodeIds = (root: TreeNode): string[] => {
  const ids: string[] = [root.id];
  for (const child of root.children) {
    ids.push(...getAllNodeIds(child));
  }
  return ids;
};

// Check if a node is descendant of another (i.e., if ancestorId is found in node's subtree)
export const isDescendantOf = (node: TreeNode, ancestorId: string): boolean => {
  // If node ID equals ancestor ID, they're the same (node is not descendant of itself)
  if (node.id === ancestorId) return false;

  // Check if ancestorId is found in node's subtree (meaning ancestor would be descendant of node)
  const checkSubtree = (root: TreeNode, targetId: string): boolean => {
    if (root.id === targetId) return true;
    for (const child of root.children) {
      if (checkSubtree(child, targetId)) return true;
    }
    return false;
  };

  return checkSubtree(node, ancestorId);
};

// Clone a node deeply
export const cloneNode = (node: TreeNode): TreeNode => {
  return cloneState(node);
};

// Flatten tree to array
export const flattenTree = (root: TreeNode): TreeNode[] => {
  const result: TreeNode[] = [root];
  for (const child of root.children) {
    result.push(...flattenTree(child));
  }
  return result;
};

// Get the base ID for copying (removes existing -copy-N suffixes)
const getBaseIdForCopy = (id: string): string => {
  // Remove any existing -copy-N suffix
  const match = id.match(/^(.+)-copy-\d+$/);
  return match ? match[1] : id;
};

// Create a deep copy of a node with new unique IDs
export const createNodeCopy = (node: TreeNode, root?: TreeNode): TreeNode => {
  let newId: string;
  const baseId = getBaseIdForCopy(node.id);

  // If we have a root, find the next available ID
  if (root) {
    const allIds = getAllNodeIds(root);
    let counter = 1;

    // Find all existing copies of this base ID
    const copyPattern = new RegExp(
      `^${baseId.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}-copy-(\\d+)$`
    );
    const existingCopies = allIds
      .map((id) => {
        const match = id.match(copyPattern);
        return match ? parseInt(match[1], 10) : 0;
      })
      .filter((n) => n > 0);

    // Find the next available number
    if (existingCopies.length > 0) {
      counter = Math.max(...existingCopies) + 1;
    }

    newId = `${baseId}-copy-${counter}`;
  } else {
    // Fallback - use timestamp to ensure uniqueness
    newId = `${baseId}-copy-${Date.now()}`;
  }

  // Create deep copy with new IDs
  const copyNodeRecursive = (originalNode: TreeNode): TreeNode => {
    const nodeNewId =
      originalNode.id === node.id
        ? newId
        : `${originalNode.id}-${Date.now()}-${Math.random()
            .toString(36)
            .substring(2, 11)}`;

    return {
      ...originalNode,
      id: nodeNewId,
      children: originalNode.children.map((child) => copyNodeRecursive(child)),
    };
  };

  return copyNodeRecursive(node);
};

// Add node to tree at specific position
export const addNodeToTree = (
  root: TreeNode,
  parentId: string,
  newNode: TreeNode,
  index?: number
): TreeNode | null => {
  if (root.id === parentId) {
    const children = [...root.children];
    if (index !== undefined && index >= 0 && index <= children.length) {
      children.splice(index, 0, newNode);
    } else {
      children.push(newNode);
    }
    return { ...root, children };
  }

  const children = root.children.map((child) => {
    const result = addNodeToTree(child, parentId, newNode, index);
    return result || child;
  });

  return { ...root, children };
};
