import { describe, it, expect } from 'vitest';
import { createDefaultNode } from '../features/builder/utils/node-factory';
import { TreeNodeType } from '../features/builder/context/types';

describe('News Template Nodes', () => {
  describe('NewsCustomGridDisplay', () => {
    it('should create default node with correct properties', () => {
      const nodeType: TreeNodeType = 'newsCustomGridDisplay';
      const defaults = createDefaultNode(nodeType);
      
      expect(defaults.group).toBe('template');
      expect(defaults.properties).toEqual({});
    });
  });

  describe('NewsGridDisplay', () => {
    it('should create default node with numberOfPosts property', () => {
      const nodeType: TreeNodeType = 'newsGridDisplay';
      const defaults = createDefaultNode(nodeType);
      
      expect(defaults.group).toBe('template');
      expect(defaults.properties).toEqual({
        numberOfPosts: 3,
      });
    });
  });
});
