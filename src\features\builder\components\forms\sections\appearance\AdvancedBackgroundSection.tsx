import React, { useMemo, useCallback } from "react";
import { TreeNode } from "../../../../context/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ColorPicker } from "../../inputs/ColorPicker";
import { safeMatch, combineClassNames } from "../../utils/classNameUtils";

interface AdvancedBackgroundSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const GRADIENT_OPTIONS = [
  { value: "none", label: "Không có gradient" },
  { value: "bg-gradient-to-t", label: "Từ dưới lên trên" },
  { value: "bg-gradient-to-tr", label: "Từ dưới-trái lên trên-phải" },
  { value: "bg-gradient-to-r", label: "Từ tr<PERSON><PERSON> sang phải" },
  { value: "bg-gradient-to-br", label: "Từ trên-trái xuống dưới-phải" },
  { value: "bg-gradient-to-b", label: "Từ trên xuống dưới" },
  { value: "bg-gradient-to-bl", label: "Từ trên-phải xuống dưới-trái" },
  { value: "bg-gradient-to-l", label: "Từ phải sang trái" },
  { value: "bg-gradient-to-tl", label: "Từ dưới-phải lên trên-trái" },
];

const BACKGROUND_SIZE_OPTIONS = [
  { value: "default", label: "Mặc định" },
  { value: "bg-auto", label: "Auto" },
  { value: "bg-cover", label: "Cover (Che phủ)" },
  { value: "bg-contain", label: "Contain (Vừa khít)" },
];

const BACKGROUND_POSITION_OPTIONS = [
  { value: "default", label: "Mặc định" },
  { value: "bg-bottom", label: "Bottom" },
  { value: "bg-center", label: "Center" },
  { value: "bg-left", label: "Left" },
  { value: "bg-left-bottom", label: "Left Bottom" },
  { value: "bg-left-top", label: "Left Top" },
  { value: "bg-right", label: "Right" },
  { value: "bg-right-bottom", label: "Right Bottom" },
  { value: "bg-right-top", label: "Right Top" },
  { value: "bg-top", label: "Top" },
];

const BACKGROUND_REPEAT_OPTIONS = [
  { value: "default", label: "Mặc định" },
  { value: "bg-repeat", label: "Repeat" },
  { value: "bg-no-repeat", label: "No Repeat" },
  { value: "bg-repeat-x", label: "Repeat X" },
  { value: "bg-repeat-y", label: "Repeat Y" },
  { value: "bg-repeat-round", label: "Repeat Round" },
  { value: "bg-repeat-space", label: "Repeat Space" },
];

const BACKGROUND_ATTACHMENT_OPTIONS = [
  { value: "default", label: "Mặc định" },
  { value: "bg-fixed", label: "Fixed" },
  { value: "bg-local", label: "Local" },
  { value: "bg-scroll", label: "Scroll" },
];

const removeAdvancedBgClasses = (className: string): string => {
  return className
    .split(" ")
    .filter(
      (cls) =>
        !cls.startsWith("bg-gradient-") &&
        !cls.startsWith("from-") &&
        !cls.startsWith("via-") &&
        !cls.startsWith("to-") &&
        !cls.startsWith("bg-auto") &&
        !cls.startsWith("bg-cover") &&
        !cls.startsWith("bg-contain") &&
        !cls.startsWith("bg-bottom") &&
        !cls.startsWith("bg-center") &&
        !cls.startsWith("bg-left") &&
        !cls.startsWith("bg-right") &&
        !cls.startsWith("bg-top") &&
        !cls.startsWith("bg-repeat") &&
        !cls.startsWith("bg-no-repeat") &&
        !cls.startsWith("bg-fixed") &&
        !cls.startsWith("bg-local") &&
        !cls.startsWith("bg-scroll")
    )
    .join(" ")
    .trim();
};

const AdvancedBackgroundSectionComponent: React.FC<
  AdvancedBackgroundSectionProps
> = ({ node, onChange, disabled }) => {
  const currentClassName = node.style?.className || "";

  const currentState = useMemo(() => {
    const gradient = safeMatch(
      currentClassName,
      /\bbg-gradient-to-\S+\b/,
      "none"
    );

    const fromColorMatch = safeMatch(currentClassName, /\bfrom-\S+\b/);
    const fromColor = fromColorMatch ? fromColorMatch.replace("from-", "") : "";

    const viaColorMatch = safeMatch(currentClassName, /\bvia-\S+\b/);
    const viaColor = viaColorMatch ? viaColorMatch.replace("via-", "") : "";

    const toColorMatch = safeMatch(currentClassName, /\bto-\S+\b/);
    const toColor = toColorMatch ? toColorMatch.replace("to-", "") : "";

    const bgSize = safeMatch(
      currentClassName,
      /\bbg-(auto|cover|contain)\b/,
      "default"
    );
    const bgPosition = safeMatch(
      currentClassName,
      /\bbg-(bottom|center|left|left-bottom|left-top|right|right-bottom|right-top|top)\b/,
      "default"
    );
    const bgRepeat = safeMatch(
      currentClassName,
      /\bbg-(repeat|no-repeat|repeat-x|repeat-y|repeat-round|repeat-space)\b/,
      "default"
    );
    const bgAttachment = safeMatch(
      currentClassName,
      /\bbg-(fixed|local|scroll)\b/,
      "default"
    );

    return {
      gradient,
      fromColor,
      viaColor,
      toColor,
      bgSize,
      bgPosition,
      bgRepeat,
      bgAttachment,
    };
  }, [currentClassName]);

  const cleanClassName = useMemo(() => {
    return removeAdvancedBgClasses(currentClassName);
  }, [currentClassName]);

  const updateClassName = (newClassName: string) => {
    onChange({
      style: {
        ...node.style,
        className: newClassName,
      },
    });
  };

  const handleChange = useCallback(
    (type: string, value: string) => {
      const newState = { ...currentState, [type]: value };

      const classes = [
        cleanClassName,
        newState.gradient !== "none" ? newState.gradient : "",
        newState.fromColor ? `from-${newState.fromColor}` : "",
        newState.viaColor ? `via-${newState.viaColor}` : "",
        newState.toColor ? `to-${newState.toColor}` : "",
        newState.bgSize !== "default" ? newState.bgSize : "",
        newState.bgPosition !== "default" ? newState.bgPosition : "",
        newState.bgRepeat !== "default" ? newState.bgRepeat : "",
        newState.bgAttachment !== "default" ? newState.bgAttachment : "",
      ].filter(Boolean);

      updateClassName(combineClassNames(...classes));
    },
    [cleanClassName, currentState]
  );

  const showGradientColors = currentState.gradient !== "none";

  return (
    <div className="space-y-3">
      {/* Gradient Direction */}
      <Select
        value={currentState.gradient}
        onValueChange={(value) => handleChange("gradient", value)}
        disabled={disabled}
      >
        <SelectTrigger>
          <SelectValue placeholder="Hướng gradient" />
        </SelectTrigger>
        <SelectContent>
          {GRADIENT_OPTIONS.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Gradient Colors */}
      {showGradientColors && (
        <>
          <div className="flex gap-2 items-center">
            <span className="text-sm w-12">Từ</span>
            <ColorPicker
              value={currentState.fromColor}
              onChange={(value) => handleChange("fromColor", value)}
              disabled={disabled}
              className="h-9 w-12 p-0 flex items-center justify-center"
              renderValue={(color) => (
                <div
                  className={`w-5 h-5 rounded-full border border-gray-300 ${
                    color ? `bg-${color}` : "bg-gray-200"
                  }`}
                  style={
                    color === "black"
                      ? { backgroundColor: "black" }
                      : color === "white"
                      ? { backgroundColor: "white" }
                      : undefined
                  }
                />
              )}
            />
            <span className="text-sm w-12">Qua</span>
            <ColorPicker
              value={currentState.viaColor}
              onChange={(value) => handleChange("viaColor", value)}
              disabled={disabled}
              className="h-9 w-12 p-0 flex items-center justify-center"
              renderValue={(color) => (
                <div
                  className={`w-5 h-5 rounded-full border border-gray-300 ${
                    color ? `bg-${color}` : "bg-gray-200"
                  }`}
                  style={
                    color === "black"
                      ? { backgroundColor: "black" }
                      : color === "white"
                      ? { backgroundColor: "white" }
                      : undefined
                  }
                />
              )}
            />
            <span className="text-sm w-12">Đến</span>
            <ColorPicker
              value={currentState.toColor}
              onChange={(value) => handleChange("toColor", value)}
              disabled={disabled}
              className="h-9 w-12 p-0 flex items-center justify-center"
              renderValue={(color) => (
                <div
                  className={`w-5 h-5 rounded-full border border-gray-300 ${
                    color ? `bg-${color}` : "bg-gray-200"
                  }`}
                  style={
                    color === "black"
                      ? { backgroundColor: "black" }
                      : color === "white"
                      ? { backgroundColor: "white" }
                      : undefined
                  }
                />
              )}
            />
          </div>
        </>
      )}

      {/* Background Size & Position */}
      <div className="flex gap-2">
        <Select
          value={currentState.bgSize}
          onValueChange={(value) => handleChange("bgSize", value)}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Kích thước nền" />
          </SelectTrigger>
          <SelectContent>
            {BACKGROUND_SIZE_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={currentState.bgPosition}
          onValueChange={(value) => handleChange("bgPosition", value)}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Vị trí nền" />
          </SelectTrigger>
          <SelectContent>
            {BACKGROUND_POSITION_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Background Repeat & Attachment */}
      <div className="flex gap-2">
        <Select
          value={currentState.bgRepeat}
          onValueChange={(value) => handleChange("bgRepeat", value)}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Lặp nền" />
          </SelectTrigger>
          <SelectContent>
            {BACKGROUND_REPEAT_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={currentState.bgAttachment}
          onValueChange={(value) => handleChange("bgAttachment", value)}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Gắn nền" />
          </SelectTrigger>
          <SelectContent>
            {BACKGROUND_ATTACHMENT_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export const AdvancedBackgroundSection = React.memo(
  AdvancedBackgroundSectionComponent
);
