export interface ContactItem {
  name: string;
  phone: string;
  email: string;
  address: string;
}

export interface ContactConfig {
  contacts: ContactItem[];
}

export interface ContactState {
  data: ContactConfig | null;
  savedData: ContactConfig | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
  isDirty: boolean;
}

// Configuration field definition
export interface ContactField {
  key: string;
  label: string;
  type: "text" | "email" | "url" | "textarea" | "boolean" | "array";
  required?: boolean;
  placeholder?: string;
}

// Configuration metadata
export interface ContactMetadata {
  title: string;
  description: string;
  type: "array" | "object";
  arrayKey?: string;
  fields: ContactField[];
}

/**
 * Edit state for tracking changes in contact items
 */
export interface EditableContactItem {
  /** Unique identifier */
  id: string;
  /** Whether this is a new item */
  isNew?: boolean;
  /** Whether this item is marked for deletion */
  isDeleted?: boolean;
  /** Item data with string values */
  data: Record<string, string>;
}
