import React, { useMemo, useCallback } from 'react';
import { TreeNode } from '../../../../context/types';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import { removeClassesByPrefix } from '../../utils/classNameUtils';

type FilterType = 'none' | 'blur' | 'brightness' | 'contrast' | 'saturate' | 'hue-rotate';

interface FilterState {
  type: FilterType;
  value: string;
}

interface FilterSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const FILTER_TYPE_OPTIONS = [
  { value: 'none', label: 'Không có' },
  { value: 'blur', label: 'Làm mờ' },
  { value: 'brightness', label: 'Độ sáng' },
  { value: 'contrast', label: '<PERSON><PERSON> tương phản' },
  { value: 'saturate', label: '<PERSON><PERSON> bão hòa' },
  { value: 'hue-rotate', label: 'Xoay màu' }
] as const;

const FILTER_VALUE_OPTIONS = {
  blur: [
    { value: 'blur-none', label: 'Không mờ' },
    { value: 'blur-sm', label: 'Mờ nhẹ' },
    { value: 'blur', label: 'Mờ vừa' },
    { value: 'blur-md', label: 'Mờ' },
    { value: 'blur-lg', label: 'Mờ nhiều' },
    { value: 'blur-xl', label: 'Mờ rất nhiều' },
    { value: 'blur-2xl', label: 'Mờ cực nhiều' },
    { value: 'blur-3xl', label: 'Mờ tối đa' }
  ],
  brightness: [
    { value: 'brightness-0', label: '0% (Tối)' },
    { value: 'brightness-50', label: '50%' },
    { value: 'brightness-75', label: '75%' },
    { value: 'brightness-90', label: '90%' },
    { value: 'brightness-95', label: '95%' },
    { value: 'brightness-100', label: '100% (Bình thường)' },
    { value: 'brightness-105', label: '105%' },
    { value: 'brightness-110', label: '110%' },
    { value: 'brightness-125', label: '125%' },
    { value: 'brightness-150', label: '150%' },
    { value: 'brightness-200', label: '200% (Sáng)' }
  ],
  contrast: [
    { value: 'contrast-0', label: '0% (Không tương phản)' },
    { value: 'contrast-50', label: '50%' },
    { value: 'contrast-75', label: '75%' },
    { value: 'contrast-100', label: '100% (Bình thường)' },
    { value: 'contrast-125', label: '125%' },
    { value: 'contrast-150', label: '150%' },
    { value: 'contrast-200', label: '200% (Tương phản cao)' }
  ],
  saturate: [
    { value: 'saturate-0', label: '0% (Xám)' },
    { value: 'saturate-50', label: '50%' },
    { value: 'saturate-100', label: '100% (Bình thường)' },
    { value: 'saturate-150', label: '150%' },
    { value: 'saturate-200', label: '200% (Bão hòa cao)' }
  ],
  'hue-rotate': [
    { value: 'hue-rotate-0', label: '0° (Bình thường)' },
    { value: 'hue-rotate-15', label: '15°' },
    { value: 'hue-rotate-30', label: '30°' },
    { value: 'hue-rotate-60', label: '60°' },
    { value: 'hue-rotate-90', label: '90°' },
    { value: 'hue-rotate-180', label: '180°' }
  ]
} as const;

const parseFilterState = (className: string = ''): FilterState => {
  const classes = className.split(' ').filter(Boolean);
  
  for (const filterType of Object.keys(FILTER_VALUE_OPTIONS)) {
    const filterClass = classes.find(cls => cls.startsWith(filterType as string));
    if (filterClass) {
      return {
        type: filterType as FilterType,
        value: filterClass
      };
    }
  }
  
  return { type: 'none', value: '' };
};

const generateFilterClasses = (filterState: FilterState): string => {
  if (filterState.type === 'none' || !filterState.value) {
    return '';
  }
  
  return filterState.value;
};

const removeFilterClasses = (className: string): string => {
  return removeClassesByPrefix([
    'blur', 
    'brightness', 
    'contrast', 
    'saturate', 
    'hue-rotate'
  ])(className);
};

const updateFilterClasses = (currentClassName: string, filterState: FilterState): string => {
  const cleanClassName = removeFilterClasses(currentClassName);
  const newFilterClasses = generateFilterClasses(filterState);
  const allClasses = [cleanClassName, newFilterClasses]
    .filter(Boolean)
    .join(' ')
    .trim();
  
  return allClasses;
};

export const FilterSection: React.FC<FilterSectionProps> = ({
  node,
  onChange,
  disabled = false
}) => {
  const currentFilterState = useMemo(() => {
    return parseFilterState(node.style?.className || '');
  }, [node.style?.className]);

  const updateFilterState = useCallback((newFilterState: Partial<FilterState>) => {
    const updatedState = { ...currentFilterState, ...newFilterState };
    const newClassName = updateFilterClasses(node.style?.className || '', updatedState);
    
    onChange({
      style: {
        ...node.style,
        className: newClassName
      }
    });
  }, [currentFilterState, node.style, onChange]);

  const handleTypeChange = useCallback((type: FilterType) => {
    if (type === 'none') {
      updateFilterState({ type, value: '' });
    } else {
      const defaultValue = FILTER_VALUE_OPTIONS[type]?.[0]?.value || '';
      updateFilterState({ type, value: defaultValue });
    }
  }, [updateFilterState]);

  const handleValueChange = useCallback((value: string) => {
    updateFilterState({ value });
  }, [updateFilterState]);

  const showControls = currentFilterState.type !== 'none';
  const currentFilterOptions = currentFilterState.type !== 'none' 
    ? FILTER_VALUE_OPTIONS[currentFilterState.type] || []
    : [];

  return (
    <div className="space-y-3">
      <Select 
        value={currentFilterState.type} 
        onValueChange={handleTypeChange}
        disabled={disabled}
      >
        <SelectTrigger>
          <SelectValue placeholder="Loại bộ lọc" />
        </SelectTrigger>
        <SelectContent>
          {FILTER_TYPE_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {showControls && (
        <Select 
          value={currentFilterState.value} 
          onValueChange={handleValueChange}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue placeholder="Giá trị" />
          </SelectTrigger>
          <SelectContent>
            {currentFilterOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
    </div>
  );
};
