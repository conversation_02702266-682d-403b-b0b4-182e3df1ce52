import { cn } from "@/lib/utils";
interface SectionTitleProps {
  title: string;
  className?: string;
}

const SectionTitle: React.FC<SectionTitleProps> = ({ title, className }) => {
  return (
    <div className={cn("flex items-center gap-4", className)}>
      <h2 className="text-2xl lg:text-3xl font-medium whitespace-nowrap">
        {title}
      </h2>
    </div>
  );
};

export default SectionTitle;
