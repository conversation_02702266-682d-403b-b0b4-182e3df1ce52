import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { PortalLinkCard } from "./components/PortalLinkCard";
import { GridHeader } from "@/components/grid/GridHeader";
import { GridPagination } from "@/components/grid/GridPagination";

export interface PortalLinkItem {
  title: string;
  url: string;
  image?: string;
}

export interface PortalLinkDisplayProps {
  links: PortalLinkItem[];
  itemsPerPage?: number;
  className?: string;
  title?: string;
  showPagination?: boolean;
}

export const PortalLinkDisplay: React.FC<PortalLinkDisplayProps> = ({
  links,
  itemsPerPage = 3,
  className,
  title,
  showPagination = true,
}) => {
  const [currentPage, setCurrentPage] = useState(0);

  // Reset pagination when links data changes
  useEffect(() => {
    setCurrentPage(0);
  }, [links]);

  if (!links || links.length === 0) {
    return null;
  }

  const totalPages = Math.ceil(links.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentLinks = links.slice(startIndex, endIndex);

  const handlePrevious = () => {
    setCurrentPage((prev) => (prev === 0 ? totalPages - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => (prev === totalPages - 1 ? 0 : prev + 1));
  };

  return (

    <div className={cn("w-full", className)}>
      {title && (
        <GridHeader
          length={links.length}
          itemsPerPage={itemsPerPage}
          title={title}
          onPrevious={handlePrevious}
          onNext={handleNext}
        />
      )}

      <div className="grid grid-cols-3 gap-8">
        {currentLinks.map((link, index) => (
          <PortalLinkCard
            key={`${startIndex + index}-${link.url}-${link.title}`}
            title={link.title}
            url={link.url}
            image={link.image}
          />
        ))}
      </div>

      {showPagination && (
        <GridPagination
          length={links.length}
          itemsPerPage={itemsPerPage}
          onChangePagination={(index) => setCurrentPage(index)} />
      )}
    </div >
  );
};
