import React from "react";
import { ImageLoader } from "@/components/image/ImageLoader";
import { cn } from "@/lib/utils";
import { Post } from "@/features/post/states/types";
import { Clock, Share2, Star } from "lucide-react";
import { Separator } from "@radix-ui/react-separator";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export type NewsArticleCardSize = "small" | "medium" | "large" | "stretch";

export interface NewsArticleCardProps {
  post: Post;
  size?: NewsArticleCardSize;
  className?: string;
  darkMode?: boolean;
  displayTag?: boolean;
  onClick?: () => void;
  onFavorite?: () => void;
  onShare?: () => void;
}

const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleDateString('vi-VN');
};

export const NewsArticleCard: React.FC<NewsArticleCardProps> = ({
  post,
  size = "stretch",
  className,
  darkMode = false,
  displayTag = false,
  onClick,
  onFavorite,
  onShare,
}) => {
  let color;
  let secondaryColor;
  let borderColor;
  let backgroundImage;
  let foregroundImage;

  const sizeClasses = {
    small: {
      container: "h-64",
      image: "h-32",
      title: "text-sm font-semibold line-clamp-2",
      meta: "text-xs",
    },
    medium: {
      container: "h-80",
      image: "h-40",
      title: "text-base font-semibold line-clamp-2",
      meta: "text-xs",
    },
    large: {
      container: "h-96",
      image: "h-48",
      title: "text-lg font-semibold line-clamp-2",
      meta: "text-sm",
    },
    stretch: {
      container: "h-full w-full",
      image: "min-h-32",
      title: "text-lg font-semibold line-clamp-2",
      meta: "text-sm",
    }
  };

  switch (darkMode) {
    case true:
      color = "text-[#ffffff]";
      secondaryColor = "text-[#ffffff]";
      borderColor = "bg-[#ffffff]";
      backgroundImage = `bg-[url('${post.excerpt.image}')]`;
      // backgroundImage = `bg-no-repeat bg-cover bg-[url('/src/assets/images/card1.png')]`;
      foregroundImage = `bg-gradient-to-t from-[#001F46] to-transparent`;
      break;
    case false:
      color = "text-[#27272A]";
      secondaryColor = "text-[#757682]";
      borderColor = "bg-[#DADADD]";
      backgroundImage = ""
      foregroundImage = ""
      break;
  }

  const classes = sizeClasses[size];

  return (
    <div className={cn(
      "rounded-lg",
      backgroundImage,
      classes.container,
      className
    )}>
      <div
        className={cn(
          "overflow-hidden cursor-pointer bg-transparent h-full rounded-lg",
          foregroundImage
        )}

        onClick={onClick}
      >
        <div className="p-0 flex flex-col h-full ">
          {/* Image Section */}
          <div className={cn("relative flex-1", classes.image)}>
            {!foregroundImage && (
              <div className={cn("relative overflow-hidden  min-h-36 h-full w-full")}>
                {post.excerpt?.image ? (
                  <ImageLoader
                    src={post.excerpt.image}
                    alt={post.title}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    fallbackText={post.title}
                  />
                ) : (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    <span className="text-gray-400 text-sm">Không có hình ảnh</span>
                  </div>
                )}
              </div>
            )}
            {displayTag && (
              <div className="absolute top-4 right-4 z-50">
                <Badge variant="default">Lãnh đạo</Badge>
              </div>
            )}
          </div>

          {/* Content Section */}
          <div className="flex-0 flex flex-col justify-between p-4 space-y-2">
            <div className="pb-2">
              <h1 className={cn(color, classes.title)}>
                {post.title}
              </h1>
            </div>

            <Separator className={cn(borderColor, "w-full h-[1px] ")} />

            {/* Meta Information */}
            <div className={cn("flex items-center justify-between", classes.meta, secondaryColor)}>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>{formatDate(post.publishedAt)}</span>
              </div>
              <div className="flex items-center gap-0 hover:bg-none">
                <Button onClick={onFavorite} variant="ghost">
                  <Star className="h-4 w-4" />
                </Button>
                <Button onClick={onShare} variant="ghost">
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
