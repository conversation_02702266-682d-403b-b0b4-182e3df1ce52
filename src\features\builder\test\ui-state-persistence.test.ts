/**
 * Test UI State Persistence functionality
 */

import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { BuilderStorage } from "../context/storage";
import { STORAGE_CONFIG } from "../config/constants";
import type {
  BuilderUIState,
  PersistentUIState,
  TemporaryUIState,
} from "../context/types";

// Mock localStorage
const mockLocalStorage = {
  data: {} as Record<string, string>,
  getItem: (key: string) => mockLocalStorage.data[key] || null,
  setItem: (key: string, value: string) => {
    mockLocalStorage.data[key] = value;
  },
  removeItem: (key: string) => delete mockLocalStorage.data[key],
  clear: () => {
    mockLocalStorage.data = {};
  },
};

// Override global localStorage
Object.defineProperty(window, "localStorage", {
  value: mockLocalStorage,
  writable: true,
});

describe("UI State Persistence", () => {
  beforeEach(() => {
    mockLocalStorage.clear();
  });

  afterEach(() => {
    mockLocalStorage.clear();
  });

  const createMockUIState = (): BuilderUIState => ({
    // Persistent state
    selectedNodeId: "test-node-1",
    expandedNodeIds: new Set(["root", "frame-1"]),
    viewMode: "tablet",
    showTreePanel: false,
    showPropertiesPanel: true,

    // Session state
    editorMode: "edit",
    isDirty: false,
    lastSaved: Date.now(),
    copiedNode: null,
    lastCreatedNodeId: null,

    // Temporary state
    temporary: {
      propertiesPanelTab: "advanced",
      propertiesPanelSubTab: "content",
      isAdvancedMode: true,
      focusedInputId: "width-input",
    },
  });

  describe("Persistent UI State", () => {
    it("should save and load persistent UI state correctly", () => {
      const uiState = createMockUIState();

      // Save persistent UI state
      BuilderStorage.savePersistentUIState(uiState);

      // Check localStorage content
      const saved = JSON.parse(
        mockLocalStorage.getItem(STORAGE_CONFIG.KEYS.UI_STATE) || "{}"
      );
      expect(saved.selectedNodeId).toBe("test-node-1");
      expect(saved.expandedNodeIds).toEqual(["root", "frame-1"]);
      expect(saved.viewMode).toBe("tablet");
      expect(saved.showTreePanel).toBe(false);
      expect(saved.showPropertiesPanel).toBe(true);

      // Should not save session-only data
      expect(saved.editorMode).toBeUndefined();
      expect(saved.isDirty).toBeUndefined();
      expect(saved.copiedNode).toBeUndefined();
    });

    it("should restore UI state with proper defaults", () => {
      const uiState = createMockUIState();
      BuilderStorage.savePersistentUIState(uiState);

      const { state } = BuilderStorage.loadCompleteState();

      expect(state.uiState?.selectedNodeId).toBe("test-node-1");
      expect(state.uiState?.expandedNodeIds).toEqual(
        new Set(["root", "frame-1"])
      );
      expect(state.uiState?.viewMode).toBe("tablet");
      expect(state.uiState?.showTreePanel).toBe(false);
      expect(state.uiState?.showPropertiesPanel).toBe(true);

      // Session state should have defaults
      expect(state.uiState?.editorMode).toBe("edit");
      expect(state.uiState?.isDirty).toBe(false);
      expect(state.uiState?.copiedNode).toBeNull();
    });
  });

  describe("Temporary UI State", () => {
    it("should save and load temporary UI state correctly", () => {
      const uiState = createMockUIState();

      // Save temporary UI state
      BuilderStorage.saveTemporaryUIState(uiState);

      // Check localStorage content
      const saved = JSON.parse(
        mockLocalStorage.getItem(STORAGE_CONFIG.KEYS.TEMP_UI_STATE) || "{}"
      );
      expect(saved.propertiesPanelTab).toBe("advanced");
      expect(saved.propertiesPanelSubTab).toBe("content");
      expect(saved.isAdvancedMode).toBe(true);

      // Should not save runtime-only data
      expect(saved.focusedInputId).toBeUndefined();
    });
  });

  describe("Complete State Persistence", () => {
    it("should save and load complete UI state", () => {
      const mockPost = {
        id: 1,
        title: "Test Post",
        content: {
          id: "root",
          label: "Root",
          type: "frame" as const,
          group: "basic" as const,
          children: [],
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
        publishedAt: 0,
        slug: "test-post",
        postType: "ARTICLE" as const,
        status: "DRAFT" as const,
        author: { id: "1", name: "Test", email: "<EMAIL>" },
        parentId: null,
      };

      const state = {
        post: mockPost,
        uiState: createMockUIState(),
      };

      // Save complete state
      BuilderStorage.saveCompleteState(state);

      // Load complete state
      const { state: loadedState } = BuilderStorage.loadCompleteState();

      expect(loadedState.post?.id).toBe(1);
      expect(loadedState.uiState?.selectedNodeId).toBe("test-node-1");
      expect(loadedState.uiState?.temporary?.propertiesPanelTab).toBe(
        "advanced"
      );
    });
  });

  describe("UI State Categories", () => {
    it("should handle F5 refresh scenario", () => {
      const uiState = createMockUIState();

      // User is editing with specific UI state
      uiState.selectedNodeId = "active-node";
      uiState.viewMode = "mobile";
      uiState.temporary!.propertiesPanelTab = "basic";
      uiState.temporary!.isEditingCSS = true;

      // Save as would happen during normal operation
      BuilderStorage.savePersistentUIState(uiState);
      BuilderStorage.saveTemporaryUIState(uiState);

      // F5 refresh - load state
      const { state } = BuilderStorage.loadCompleteState();

      // Should restore user's work context
      expect(state.uiState?.selectedNodeId).toBe("active-node");
      expect(state.uiState?.viewMode).toBe("mobile");
      expect(state.uiState?.temporary?.propertiesPanelTab).toBe("basic");

      // Should reset session state
      expect(state.uiState?.editorMode).toBe("edit");
      expect(state.uiState?.isDirty).toBe(false);

      // Should not restore runtime state
      expect(state.uiState?.temporary?.isEditingCSS).toBeUndefined();
    });
  });

  describe("Backward Compatibility", () => {
    it("should handle legacy storage format", () => {
      // Simulate old storage format
      const legacyData = {
        id: 1,
        title: "Legacy Post",
        content: {
          id: "root",
          label: "Root",
          type: "frame",
          group: "basic",
          children: [],
        },
      };

      mockLocalStorage.setItem(
        STORAGE_CONFIG.KEYS.POST,
        JSON.stringify(legacyData)
      );

      const { state } = BuilderStorage.loadCompleteState();

      expect(state.post?.id).toBe(1);
      expect(state.post?.title).toBe("Legacy Post");
      // UI state should get defaults
      expect(state.uiState).toBeDefined();
    });
  });
});

describe("UI State Type Safety", () => {
  it("should enforce proper type separation", () => {
    const persistentState: PersistentUIState = {
      selectedNodeId: "test",
      expandedNodeIds: new Set(["root"]),
      viewMode: "desktop",
      showTreePanel: true,
      showPropertiesPanel: true,
    };

    const temporaryState: TemporaryUIState = {
      propertiesPanelTab: "basic",
      isAdvancedMode: false,
    };

    // This should compile without errors
    expect(persistentState.selectedNodeId).toBe("test");
    expect(temporaryState.propertiesPanelTab).toBe("basic");

    // TypeScript should prevent mixing concerns
    // persistentState.isDirty = true; // This should cause TS error
    // temporaryState.viewMode = 'mobile'; // This should cause TS error
  });
});
