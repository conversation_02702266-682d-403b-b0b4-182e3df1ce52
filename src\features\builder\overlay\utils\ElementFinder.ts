/**
 * Safe utility để tìm actual render element cho overlay
 */
export class ElementFinder {
  /**
   * Tìm element thực tế để render overlay
   * Tôn trọng node được register - không tự động chuyển sang child
   */
  static findActualElement(wrapperElement: Element, nodeId?: string): Element | null {
    try {
      if (!wrapperElement) return null;

      // Kiểm tra xem element có data-node-id để xác định node type
      const elementNodeId = wrapperElement.getAttribute('data-node-id');
      
      // Nếu wrapper element chính xác là node được chọn, dùng nó
      if (nodeId && elementNodeId === nodeId) {
        return wrapperElement;
      }

      // Nếu không match và có nodeId, tìm trong children
      if (nodeId && elementNodeId !== nodeId) {
        const childWithNodeId = wrapperElement.querySelector(`[data-node-id="${nodeId}"]`);
        if (childWithNodeId) {
          return childWithNodeId;
        }
      }

      // Chỉ ưu tiên child element khi không có nodeId cụ thể
      // hoặc khi element là builder-node wrapper generic
      if (!nodeId && wrapperElement.classList.contains('builder-node')) {
        const firstChild = wrapperElement.firstElementChild;
        
        // Kiểm tra child có phải là element thực tế không
        if (firstChild && !firstChild.classList.contains('builder-node')) {
          return firstChild;
        }
      }

      // Fallback về wrapper
      return wrapperElement;
    } catch {
      return wrapperElement;
    }
  }

  /**
   * Safe getBoundingClientRect với error handling
   */
  static getBoundingRect(element: Element): DOMRect | null {
    try {
      if (!element || !element.getBoundingClientRect) return null;
      return element.getBoundingClientRect();
    } catch {
      return null;
    }
  }

  /**
   * Tính toán position relative với container an toàn
   */
  static calculateRelativePosition(
    element: Element, 
    container: Element
  ): DOMRect | null {
    try {
      const elementRect = this.getBoundingRect(element);
      const containerRect = this.getBoundingRect(container);
      
      if (!elementRect || !containerRect) return null;

      return new DOMRect(
        elementRect.left - containerRect.left,
        elementRect.top - containerRect.top,
        elementRect.width,
        elementRect.height
      );
    } catch {
      return null;
    }
  }
}