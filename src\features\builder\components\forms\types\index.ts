// =============================================================================
// FORM TYPES - TYPE DEFINITIONS
// =============================================================================

import type { TreeNode } from "../../../context/types";

// Form Props
export interface FormProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled: boolean;
}

// Section Props
export interface SectionProps extends FormProps {
  title?: string;
  className?: string;
}

// Form Field Props
export interface FieldProps<T = string> {
  label: string;
  value: T;
  onChange: (value: T) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

// Display Form Props (legacy compatibility)
export interface DisplayFormProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled: boolean;
}

// Form Component Type
export type FormComponent = React.FC<FormProps>;
export type DisplayFormComponent = React.FC<DisplayFormProps>;
