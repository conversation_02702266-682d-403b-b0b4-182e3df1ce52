// Z-index value groups

export interface ZIndexOption {
  value: string;
  label: string;
  description?: string;
}

export interface ZIndexGroup {
  id: string;
  label: string;
  options: ZIndexOption[];
}

// Common z-index values
const Z_INDEX_FIXED_VALUES = [
  { value: 'z-0', label: '0', description: 'Cùng mức' },
  { value: 'z-10', label: '10', description: 'Dropdown' },
  { value: 'z-20', label: '20', description: 'Sticky' },
  { value: 'z-30', label: '30', description: 'Fixed' },
  { value: 'z-40', label: '40', description: 'Overlay' },
  { value: 'z-50', label: '50', description: 'Modal' },
];

// Detailed z-index values for fine control
const Z_INDEX_DETAILED_VALUES = [
  { value: 'z-0', label: '0', description: 'Cùng mức' },
  { value: 'z-1', label: '1', description: '' },
  { value: 'z-2', label: '2', description: '' },
  { value: 'z-3', label: '3', description: '' },
  { value: 'z-4', label: '4', description: '' },
  { value: 'z-5', label: '5', description: '' },
  { value: 'z-10', label: '10', description: 'Dropdown' },
  { value: 'z-20', label: '20', description: 'Sticky' },
  { value: 'z-30', label: '30', description: 'Fixed' },
  { value: 'z-40', label: '40', description: 'Overlay' },
  { value: 'z-50', label: '50', description: 'Modal' },
  { value: 'z-60', label: '60', description: '' },
  { value: 'z-70', label: '70', description: '' },
  { value: 'z-80', label: '80', description: '' },
  { value: 'z-90', label: '90', description: '' },
  { value: 'z-100', label: '100', description: '' },
  { value: 'z-110', label: '110', description: '' },
  { value: 'z-120', label: '120', description: '' },
  { value: 'z-130', label: '130', description: '' },
  { value: 'z-140', label: '140', description: '' },
  { value: 'z-150', label: '150', description: '' },
  { value: 'z-200', label: '200', description: '' },
  { value: 'z-300', label: '300', description: '' },
  { value: 'z-400', label: '400', description: '' },
  { value: 'z-500', label: '500', description: '' },
  { value: 'z-600', label: '600', description: '' },
  { value: 'z-700', label: '700', description: '' },
  { value: 'z-800', label: '800', description: '' },
  { value: 'z-900', label: '900', description: '' },
  { value: 'z-950', label: '950', description: '' },
  { value: 'z-999', label: '999', description: 'Tối đa' },
];

// Negative z-index values (Note: Tailwind CSS v4 uses -z-* format)
const Z_INDEX_NEGATIVE_VALUES = [
  { value: '-z-10', label: '-10', description: 'Dưới nền' },
  { value: '-z-20', label: '-20', description: '' },
  { value: '-z-30', label: '-30', description: '' },
  { value: '-z-40', label: '-40', description: '' },
  { value: '-z-50', label: '-50', description: '' },
];

// Z-index groups with tabs
export const Z_INDEX_GROUPS: ZIndexGroup[] = [
  {
    id: 'common',
    label: 'Thông dụng',
    options: [
      { value: 'none', label: 'Không chọn', description: 'Không có z-index' },
      ...Z_INDEX_FIXED_VALUES
    ]
  },
  {
    id: 'detailed',
    label: 'Chi tiết',
    options: Z_INDEX_DETAILED_VALUES
  },
  {
    id: 'negative',
    label: 'Âm',
    options: Z_INDEX_NEGATIVE_VALUES
  },
  {
    id: 'auto',
    label: 'Tự động',
    options: [{ value: 'z-auto', label: 'Auto', description: 'Tự động' }]
  }
];