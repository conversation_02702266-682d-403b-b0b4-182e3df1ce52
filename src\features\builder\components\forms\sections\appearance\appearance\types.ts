export type RoundedMode = 'none' | 'all' | 'top' | 'right' | 'bottom' | 'left' | 'corners' | 'custom';

export type RoundedSize = 'none' | 'sm' | 'base' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full';

export interface RoundedOption {
  size: string;
  // For corner-specific controls
  tl?: string;
  tr?: string;
  bl?: string;
  br?: string;
}

export interface RoundedState {
  enabled: boolean;
  mode: RoundedMode;
  options: RoundedOption[];
}

export interface AppearanceState {
  opacity: string;
  backgroundColor: string;
  rounded: RoundedState;
}

export type OpacityValue = 'opacity-0' | 'opacity-5' | 'opacity-10' | 'opacity-15' | 'opacity-20' | 
                          'opacity-25' | 'opacity-30' | 'opacity-35' | 'opacity-40' | 'opacity-45' | 
                          'opacity-50' | 'opacity-55' | 'opacity-60' | 'opacity-65' | 'opacity-70' | 
                          'opacity-75' | 'opacity-80' | 'opacity-85' | 'opacity-90' | 'opacity-95' | 
                          'opacity-100';

// Rounded prefix mapping similar to border prefixes
export const ROUNDED_PREFIX_MAP: Record<RoundedMode, string[]> = {
  'none': [],
  'all': ['rounded'],
  'top': ['rounded-t'],
  'right': ['rounded-r'],
  'bottom': ['rounded-b'],
  'left': ['rounded-l'],
  'corners': ['rounded-tl', 'rounded-tr', 'rounded-bl', 'rounded-br'],
  'custom': []
};