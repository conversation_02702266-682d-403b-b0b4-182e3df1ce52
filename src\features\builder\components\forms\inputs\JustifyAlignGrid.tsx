import React from 'react';
import { cn } from '@/lib/utils';
import { Square } from 'lucide-react';

interface JustifyAlignGridProps {
  className?: string;
  onChange?: (className: string) => void;
  disabled?: boolean;
}

// Simple index mapping 0-8 to justify/align combinations
const GRID_MAPPING = [
  { justify: 'start', align: 'start' },   // 0: Trên-tr<PERSON><PERSON>
  { justify: 'center', align: 'start' },  // 1: Trên-giữa
  { justify: 'end', align: 'start' },     // 2: Trên-phải
  { justify: 'start', align: 'center' },  // 3: Gi<PERSON><PERSON>-trái
  { justify: 'center', align: 'center' }, // 4: Giữa-giữa
  { justify: 'end', align: 'center' },    // 5: Giữa-phải
  { justify: 'start', align: 'end' },     // 6: <PERSON><PERSON><PERSON>i-tr<PERSON><PERSON>
  { justify: 'center', align: 'end' },    // 7: Dưới-giữa
  { justify: 'end', align: 'end' },       // 8: Dưới-phải
];

export const JustifyAlignGrid: React.FC<JustifyAlignGridProps> = ({
  className = '',
  onChange,
  disabled = false
}) => {
  
  // Get current index from className
  const getCurrentIndex = (): number => {
    const justifyMatch = className.match(/\bjustify-(start|end|center|between|around|evenly)\b/);
    const alignMatch = className.match(/\bitems-(start|end|center|baseline|stretch)\b/);
    
    const justify = justifyMatch ? justifyMatch[1] : 'start';
    const align = alignMatch ? alignMatch[1] : 'start';
    
    // Find matching index
    for (let i = 0; i < GRID_MAPPING.length; i++) {
      if (GRID_MAPPING[i].justify === justify && GRID_MAPPING[i].align === align) {
        return i;
      }
    }
    return 0; // Default to index 0 (start/start)
  };

  const currentIndex = getCurrentIndex();

  const handleCellClick = (index: number) => {
    if (disabled) return;
    
    const { justify, align } = GRID_MAPPING[index];
    
    // Update className directly
    let updatedClassName = className;
    
    // Remove existing justify and align classes
    updatedClassName = updatedClassName
      .replace(/\bjustify-(start|end|center|between|around|evenly)\b/g, '')
      .replace(/\bitems-(start|end|center|baseline|stretch)\b/g, '')
      .trim();
    
    // Add new classes (skip 'start' values)
    if (justify !== 'start') {
      updatedClassName = `${updatedClassName} justify-${justify}`.trim();
    }
    if (align !== 'start') {
      updatedClassName = `${updatedClassName} items-${align}`.trim();
    }
    
    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\s+/g, ' ').trim();
    
    onChange?.(updatedClassName);
  };

  const isActive = (index: number) => {
    return currentIndex === index;
  };

  return (
    <div className="flex justify-center">
      {/* Grid Container - centered */}
      <div className="grid grid-cols-3 gap-2 p-3 bg-gray-50 rounded-md">
        {GRID_MAPPING.map(({ justify, align }, index) => {
          const active = isActive(index);
          
          return (
            <button
              key={index}
              onClick={() => handleCellClick(index)}
              disabled={disabled}
              className={cn(
                "w-12 h-12 rounded-sm border-2 transition-all duration-200",
                "flex items-center justify-center relative",
                "hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-200",
                "cursor-pointer", // Ensure pointer cursor
                active
                  ? "border-blue-500 bg-blue-100"
                  : "border-gray-300 bg-white hover:bg-gray-50",
                disabled && "opacity-50 cursor-not-allowed hover:border-gray-300 hover:bg-white"
              )}
              title={`Index ${index}: justify-${justify} items-${align}`}
            >
              {/* Visual indicator - square icon instead of dot */}
              <Square
                className={cn(
                  "w-4 h-4 transition-colors pointer-events-none",
                  active ? "text-blue-600 fill-blue-600" : "text-gray-400"
                )}
              />
            </button>
          );
        })}
      </div>
    </div>
  );
};
