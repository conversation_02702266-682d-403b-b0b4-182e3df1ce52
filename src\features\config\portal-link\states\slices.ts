import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import type { PortalLinkConfig, PortalLinkState } from "./type";
import { fetchPortalLinkConfig, updatePortalLinkConfig } from "./api";

// Initial state
const initialState: PortalLinkState = {
  data: null,
  savedData: null,
  loading: false,
  saving: false,
  error: null,
  isDirty: false,
};

// Async thunks
export const fetchPortalLinkAsync = createAsyncThunk(
  "portalLink/fetchPortalLink",
  async () => {
    const response = await fetchPortalLinkConfig();
    return response.data;
  }
);

export const updatePortalLinkAsync = createAsyncThunk(
  "portalLink/updatePortalLink",
  async (data: PortalLinkConfig) => {
    const response = await updatePortalLinkConfig(data);
    return response.data;
  }
);

// Slice
const portalLinkSlice = createSlice({
  name: "portalLink",
  initialState,
  reducers: {
    setPortalLinkData: (state, action) => {
      state.data = action.payload;
      state.isDirty = true;
    },
    setDirty: (state, action) => {
      state.isDirty = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch portal link
      .addCase(fetchPortalLinkAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPortalLinkAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload || null;
        state.savedData = action.payload || null;
        state.isDirty = false;
      })
      .addCase(fetchPortalLinkAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch portal link data";
      })
      // Update portal link
      .addCase(updatePortalLinkAsync.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updatePortalLinkAsync.fulfilled, (state, action) => {
        state.saving = false;
        state.data = action.payload || null;
        state.savedData = action.payload || null;
        state.isDirty = false;
      })
      .addCase(updatePortalLinkAsync.rejected, (state, action) => {
        state.saving = false;
        state.error = action.error.message || "Failed to update portal link data";
      });
  },
});

export const { setPortalLinkData, setDirty, clearError } = portalLinkSlice.actions;
export default portalLinkSlice.reducer;
