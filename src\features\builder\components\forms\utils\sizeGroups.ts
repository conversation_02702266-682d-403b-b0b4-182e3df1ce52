// Size value groups organized by Tailwind CSS documentation structure

export interface SizeOption {
  value: string;
  label: string;
  description?: string;
}

export interface SizeGroup {
  id: string;
  label: string;
  options: SizeOption[];
}

// Special width values from Tailwind docs
const WIDTH_SPECIAL_VALUES = [
  { value: 'auto', label: 'Tự động', description: 'auto' },
  { value: 'w-full', label: 'Đầy đủ', description: '100%' },
  { value: 'w-min', label: 'Tối thiểu', description: 'min-content' },
  { value: 'w-max', label: 'Tối đa', description: 'max-content' },
  { value: 'w-fit', label: 'Khớp', description: 'fit-content' },
];

// Container width values (named sizes)
const WIDTH_NAMED_VALUES = [
  { value: 'w-3xs', label: '3xs', description: '16rem' },
  { value: 'w-2xs', label: '2xs', description: '18rem' },
  { value: 'w-xs', label: 'xs', description: '20rem' },
  { value: 'w-sm', label: 'sm', description: '24rem' },
  { value: 'w-md', label: 'md', description: '28rem' },
  { value: 'w-lg', label: 'lg', description: '32rem' },
  { value: 'w-xl', label: 'xl', description: '36rem' },
  { value: 'w-2xl', label: '2xl', description: '42rem' },
  { value: 'w-3xl', label: '3xl', description: '48rem' },
  { value: 'w-4xl', label: '4xl', description: '56rem' },
  { value: 'w-5xl', label: '5xl', description: '64rem' },
  { value: 'w-6xl', label: '6xl', description: '72rem' },
  { value: 'w-7xl', label: '7xl', description: '80rem' },
];

// Fixed width values - Tailwind spacing scale (rem * 16 = px)
const WIDTH_FIXED_VALUES = [
  { value: 'w-0', label: '0' },
  { value: 'w-px', label: 'px', description: '1px' },
  { value: 'w-0.5', label: '0.5', description: '2px' },
  { value: 'w-1', label: '1', description: '4px' },
  { value: 'w-1.5', label: '1.5', description: '6px' },
  { value: 'w-2', label: '2', description: '8px' },
  { value: 'w-2.5', label: '2.5', description: '10px' },
  { value: 'w-3', label: '3', description: '12px' },
  { value: 'w-3.5', label: '3.5', description: '14px' },
  { value: 'w-4', label: '4', description: '16px' },
  { value: 'w-5', label: '5', description: '20px' },
  { value: 'w-6', label: '6', description: '24px' },
  { value: 'w-7', label: '7', description: '28px' },
  { value: 'w-8', label: '8', description: '32px' },
  { value: 'w-9', label: '9', description: '36px' },
  { value: 'w-10', label: '10', description: '40px' },
  { value: 'w-11', label: '11', description: '44px' },
  { value: 'w-12', label: '12', description: '48px' },
  { value: 'w-14', label: '14', description: '56px' },
  { value: 'w-16', label: '16', description: '64px' },
  { value: 'w-20', label: '20', description: '80px' },
  { value: 'w-24', label: '24', description: '96px' },
  { value: 'w-28', label: '28', description: '112px' },
  { value: 'w-32', label: '32', description: '128px' },
  { value: 'w-36', label: '36', description: '144px' },
  { value: 'w-40', label: '40', description: '160px' },
  { value: 'w-44', label: '44', description: '176px' },
  { value: 'w-48', label: '48', description: '192px' },
  { value: 'w-52', label: '52', description: '208px' },
  { value: 'w-56', label: '56', description: '224px' },
  { value: 'w-60', label: '60', description: '240px' },
  { value: 'w-64', label: '64', description: '256px' },
  { value: 'w-72', label: '72', description: '288px' },
  { value: 'w-80', label: '80', description: '320px' },
  { value: 'w-96', label: '96', description: '384px' },
];

// Fraction width values
const WIDTH_FRACTION_VALUES = [
  { value: 'w-1/2', label: '1/2', description: '50%' },
  { value: 'w-1/3', label: '1/3', description: '33.333%' },
  { value: 'w-2/3', label: '2/3', description: '66.667%' },
  { value: 'w-1/4', label: '1/4', description: '25%' },
  { value: 'w-3/4', label: '3/4', description: '75%' },
  { value: 'w-1/5', label: '1/5', description: '20%' },
  { value: 'w-2/5', label: '2/5', description: '40%' },
  { value: 'w-3/5', label: '3/5', description: '60%' },
  { value: 'w-4/5', label: '4/5', description: '80%' },
  { value: 'w-1/6', label: '1/6', description: '16.667%' },
  { value: 'w-5/6', label: '5/6', description: '83.333%' },
  { value: 'w-1/12', label: '1/12', description: '8.333%' },
  { value: 'w-2/12', label: '2/12', description: '16.667%' },
  { value: 'w-3/12', label: '3/12', description: '25%' },
  { value: 'w-4/12', label: '4/12', description: '33.333%' },
  { value: 'w-5/12', label: '5/12', description: '41.667%' },
  { value: 'w-6/12', label: '6/12', description: '50%' },
  { value: 'w-7/12', label: '7/12', description: '58.333%' },
  { value: 'w-8/12', label: '8/12', description: '66.667%' },
  { value: 'w-9/12', label: '9/12', description: '75%' },
  { value: 'w-10/12', label: '10/12', description: '83.333%' },
  { value: 'w-11/12', label: '11/12', description: '91.667%' },
];

// Viewport width values
const WIDTH_VIEWPORT_VALUES = [
  { value: 'w-screen', label: 'screen', description: '100vw' },
  { value: 'w-svw', label: 'svw', description: '100svw' },
  { value: 'w-lvw', label: 'lvw', description: '100lvw' },
  { value: 'w-dvw', label: 'dvw', description: '100dvw' },
];

// Width groups for the popover
export const WIDTH_GROUPS: SizeGroup[] = [
  {
    id: 'special',
    label: 'Đặc biệt',
    options: WIDTH_SPECIAL_VALUES
  },
  {
    id: 'named',
    label: 'Định sẵn',
    options: WIDTH_NAMED_VALUES
  },
  {
    id: 'fixed',
    label: 'Cố định',
    options: WIDTH_FIXED_VALUES
  },
  {
    id: 'fraction',
    label: 'Tỉ lệ',
    options: WIDTH_FRACTION_VALUES
  },
  {
    id: 'viewport',
    label: 'Màn hình',
    options: WIDTH_VIEWPORT_VALUES
  }
];

// Height special values
const HEIGHT_SPECIAL_VALUES = [
  { value: 'auto', label: 'Tự động', description: 'auto' },
  { value: 'h-full', label: 'Đầy đủ', description: '100%' },
  { value: 'h-min', label: 'Tối thiểu', description: 'min-content' },
  { value: 'h-max', label: 'Tối đa', description: 'max-content' },
  { value: 'h-fit', label: 'Khớp', description: 'fit-content' },
  { value: 'h-lh', label: 'Dòng chữ', description: '1 line-height' },
];

// Height viewport values - including both vh and vw units
const HEIGHT_VIEWPORT_VALUES = [
  { value: 'h-screen', label: 'screen', description: '100vh' },
  { value: 'h-svh', label: 'svh', description: '100svh' },
  { value: 'h-lvh', label: 'lvh', description: '100lvh' },
  { value: 'h-dvh', label: 'dvh', description: '100dvh' },
  { value: 'h-svw', label: 'svw', description: '100svw' },
  { value: 'h-lvw', label: 'lvw', description: '100lvw' },
  { value: 'h-dvw', label: 'dvw', description: '100dvw' },
];

// Height groups - NO named values (xs, sm, md...) for height!
export const HEIGHT_GROUPS: SizeGroup[] = [
  {
    id: 'special',
    label: 'Đặc biệt',
    options: HEIGHT_SPECIAL_VALUES
  },
  {
    id: 'fixed',
    label: 'Cố định',
    options: WIDTH_FIXED_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'h-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'fraction',
    label: 'Tỉ lệ',
    options: WIDTH_FRACTION_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'h-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'viewport',
    label: 'Màn hình',
    options: HEIGHT_VIEWPORT_VALUES
  }
];

// Min-width special values
const MIN_WIDTH_SPECIAL_VALUES = [
  { value: 'none', label: 'Không chọn', description: 'Không giới hạn' },
  { value: 'min-w-0', label: '0' },
  { value: 'min-w-auto', label: 'Tự động', description: 'auto' },
  { value: 'min-w-full', label: 'Đầy đủ', description: '100%' },
  { value: 'min-w-min', label: 'Tối thiểu', description: 'min-content' },
  { value: 'min-w-max', label: 'Tối đa', description: 'max-content' },
  { value: 'min-w-fit', label: 'Khớp', description: 'fit-content' },
];

// Min-width viewport values
const MIN_WIDTH_VIEWPORT_VALUES = [
  { value: 'min-w-screen', label: 'screen', description: '100vw' },
  { value: 'min-w-svw', label: 'svw', description: '100svw' },
  { value: 'min-w-lvw', label: 'lvw', description: '100lvw' },
  { value: 'min-w-dvw', label: 'dvw', description: '100dvw' },
  { value: 'min-w-svh', label: 'svh', description: '100svh' },
  { value: 'min-w-lvh', label: 'lvh', description: '100lvh' },
  { value: 'min-w-dvh', label: 'dvh', description: '100dvh' },
];

// Min-width groups - has container sizes and fractions!
export const MIN_WIDTH_GROUPS: SizeGroup[] = [
  {
    id: 'special',
    label: 'Đặc biệt',
    options: MIN_WIDTH_SPECIAL_VALUES
  },
  {
    id: 'named',
    label: 'Định sẵn',
    options: WIDTH_NAMED_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'min-w-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'fixed',
    label: 'Cố định',
    options: WIDTH_FIXED_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'min-w-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'fraction',
    label: 'Tỉ lệ',
    options: WIDTH_FRACTION_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'min-w-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'viewport',
    label: 'Màn hình',
    options: MIN_WIDTH_VIEWPORT_VALUES
  }
];

// Max-width named sizes (container sizes)
const MAX_WIDTH_NAMED_VALUES = [
  { value: 'max-w-3xs', label: '3xs', description: '16rem' },
  { value: 'max-w-2xs', label: '2xs', description: '18rem' },
  { value: 'max-w-xs', label: 'xs', description: '20rem' },
  { value: 'max-w-sm', label: 'sm', description: '24rem' },
  { value: 'max-w-md', label: 'md', description: '28rem' },
  { value: 'max-w-lg', label: 'lg', description: '32rem' },
  { value: 'max-w-xl', label: 'xl', description: '36rem' },
  { value: 'max-w-2xl', label: '2xl', description: '42rem' },
  { value: 'max-w-3xl', label: '3xl', description: '48rem' },
  { value: 'max-w-4xl', label: '4xl', description: '56rem' },
  { value: 'max-w-5xl', label: '5xl', description: '64rem' },
  { value: 'max-w-6xl', label: '6xl', description: '72rem' },
  { value: 'max-w-7xl', label: '7xl', description: '80rem' },
];

// Max-width special values
const MAX_WIDTH_SPECIAL_VALUES = [
  { value: 'none', label: 'Không chọn', description: 'Không giới hạn' },
  { value: 'max-w-none', label: 'None', description: 'none' },
  { value: 'max-w-0', label: '0' },
  { value: 'max-w-full', label: 'Đầy đủ', description: '100%' },
  { value: 'max-w-min', label: 'Tối thiểu', description: 'min-content' },
  { value: 'max-w-max', label: 'Tối đa', description: 'max-content' },
  { value: 'max-w-fit', label: 'Khớp', description: 'fit-content' },
];

// Max-width viewport values
const MAX_WIDTH_VIEWPORT_VALUES = [
  { value: 'max-w-screen', label: 'screen', description: '100vw' },
  { value: 'max-w-svw', label: 'svw', description: '100svw' },
  { value: 'max-w-lvw', label: 'lvw', description: '100lvw' },
  { value: 'max-w-dvw', label: 'dvw', description: '100dvw' },
  { value: 'max-w-svh', label: 'svh', description: '100svh' },
  { value: 'max-w-lvh', label: 'lvh', description: '100lvh' },
  { value: 'max-w-dvh', label: 'dvh', description: '100dvh' },
];

// Max-width groups
export const MAX_WIDTH_GROUPS: SizeGroup[] = [
  {
    id: 'special',
    label: 'Đặc biệt',
    options: MAX_WIDTH_SPECIAL_VALUES
  },
  {
    id: 'named',
    label: 'Định sẵn',
    options: MAX_WIDTH_NAMED_VALUES
  },
  {
    id: 'fixed',
    label: 'Cố định',
    options: WIDTH_FIXED_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'max-w-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'fraction',
    label: 'Tỉ lệ',
    options: WIDTH_FRACTION_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'max-w-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'viewport',
    label: 'Màn hình',
    options: MAX_WIDTH_VIEWPORT_VALUES
  }
];

// Min-height special values
const MIN_HEIGHT_SPECIAL_VALUES = [
  { value: 'none', label: 'Không chọn', description: 'Không giới hạn' },
  { value: 'min-h-auto', label: 'Tự động', description: 'auto' },
  { value: 'min-h-full', label: 'Đầy đủ', description: '100%' },
  { value: 'min-h-min', label: 'Tối thiểu', description: 'min-content' },
  { value: 'min-h-max', label: 'Tối đa', description: 'max-content' },
  { value: 'min-h-fit', label: 'Khớp', description: 'fit-content' },
  { value: 'min-h-lh', label: 'Dòng chữ', description: '1 line-height' },
];

// Min-height viewport values
const MIN_HEIGHT_VIEWPORT_VALUES = [
  { value: 'min-h-screen', label: 'screen', description: '100vh' },
  { value: 'min-h-svh', label: 'svh', description: '100svh' },
  { value: 'min-h-lvh', label: 'lvh', description: '100lvh' },
  { value: 'min-h-dvh', label: 'dvh', description: '100dvh' },
  { value: 'min-h-svw', label: 'svw', description: '100svw' },
  { value: 'min-h-lvw', label: 'lvw', description: '100lvw' },
  { value: 'min-h-dvw', label: 'dvw', description: '100dvw' },
];

// Min-height groups - includes fractions unlike min-width
export const MIN_HEIGHT_GROUPS: SizeGroup[] = [
  {
    id: 'special',
    label: 'Đặc biệt',
    options: MIN_HEIGHT_SPECIAL_VALUES
  },
  {
    id: 'fixed',
    label: 'Cố định',
    options: WIDTH_FIXED_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'min-h-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'fraction',
    label: 'Tỉ lệ',
    options: WIDTH_FRACTION_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'min-h-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'viewport',
    label: 'Màn hình',
    options: MIN_HEIGHT_VIEWPORT_VALUES
  }
];

// Max-height special values
const MAX_HEIGHT_SPECIAL_VALUES = [
  { value: 'none', label: 'Không chọn', description: 'Không giới hạn' },
  { value: 'max-h-none', label: 'None', description: 'none' },
  { value: 'max-h-full', label: 'Đầy đủ', description: '100%' },
  { value: 'max-h-min', label: 'Tối thiểu', description: 'min-content' },
  { value: 'max-h-max', label: 'Tối đa', description: 'max-content' },
  { value: 'max-h-fit', label: 'Khớp', description: 'fit-content' },
  { value: 'max-h-lh', label: 'Dòng chữ', description: '1 line-height' },
];

// Max-height viewport values
const MAX_HEIGHT_VIEWPORT_VALUES = [
  { value: 'max-h-screen', label: 'screen', description: '100vh' },
  { value: 'max-h-svh', label: 'svh', description: '100svh' },
  { value: 'max-h-lvh', label: 'lvh', description: '100lvh' },
  { value: 'max-h-dvh', label: 'dvh', description: '100dvh' },
  { value: 'max-h-svw', label: 'svw', description: '100svw' },
  { value: 'max-h-lvw', label: 'lvw', description: '100lvw' },
  { value: 'max-h-dvw', label: 'dvw', description: '100dvw' },
];

// Max-height groups - includes fractions
export const MAX_HEIGHT_GROUPS: SizeGroup[] = [
  {
    id: 'special',
    label: 'Đặc biệt',
    options: MAX_HEIGHT_SPECIAL_VALUES
  },
  {
    id: 'fixed',
    label: 'Cố định',
    options: WIDTH_FIXED_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'max-h-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'fraction',
    label: 'Tỉ lệ',
    options: WIDTH_FRACTION_VALUES.map(opt => ({
      value: opt.value.replace('w-', 'max-h-'),
      label: opt.label,
      description: opt.description
    }))
  },
  {
    id: 'viewport',
    label: 'Màn hình',
    options: MAX_HEIGHT_VIEWPORT_VALUES
  }
];