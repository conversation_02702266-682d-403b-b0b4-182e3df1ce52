import React, { useMemo, useCallback } from 'react';
import { TreeNode } from '../../../../context/types';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { safeMatch, combineClassNames } from '../../utils/classNameUtils';

interface TransformSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const SCALE_OPTIONS = [
  { value: 'none', label: 'Không' },
  { value: 'scale-0', label: '0 (Ẩn)' },
  { value: 'scale-50', label: '50%' },
  { value: 'scale-75', label: '75%' },
  { value: 'scale-90', label: '90%' },
  { value: 'scale-95', label: '95%' },
  { value: 'scale-100', label: '100% (Mặc định)' },
  { value: 'scale-105', label: '105%' },
  { value: 'scale-110', label: '110%' },
  { value: 'scale-125', label: '125%' },
  { value: 'scale-150', label: '150%' },
  { value: 'scale-200', label: '200%' }
];

const ROTATE_OPTIONS = [
  { value: 'none', label: 'Không' },
  { value: 'rotate-0', label: '0°' },
  { value: 'rotate-1', label: '1°' },
  { value: 'rotate-2', label: '2°' },
  { value: 'rotate-3', label: '3°' },
  { value: 'rotate-6', label: '6°' },
  { value: 'rotate-12', label: '12°' },
  { value: 'rotate-45', label: '45°' },
  { value: 'rotate-90', label: '90°' },
  { value: 'rotate-180', label: '180°' },
  { value: '-rotate-1', label: '-1°' },
  { value: '-rotate-2', label: '-2°' },
  { value: '-rotate-3', label: '-3°' },
  { value: '-rotate-6', label: '-6°' },
  { value: '-rotate-12', label: '-12°' },
  { value: '-rotate-45', label: '-45°' },
  { value: '-rotate-90', label: '-90°' },
  { value: '-rotate-180', label: '-180°' }
];

const TRANSLATE_X_OPTIONS = [
  { value: 'none', label: 'Không' },
  { value: 'translate-x-0', label: '0px' },
  { value: 'translate-x-px', label: '1px' },
  { value: 'translate-x-0.5', label: '2px' },
  { value: 'translate-x-1', label: '4px' },
  { value: 'translate-x-1.5', label: '6px' },
  { value: 'translate-x-2', label: '8px' },
  { value: 'translate-x-2.5', label: '10px' },
  { value: 'translate-x-3', label: '12px' },
  { value: 'translate-x-4', label: '16px' },
  { value: 'translate-x-5', label: '20px' },
  { value: 'translate-x-6', label: '24px' },
  { value: 'translate-x-8', label: '32px' },
  { value: 'translate-x-10', label: '40px' },
  { value: 'translate-x-12', label: '48px' },
  { value: 'translate-x-16', label: '64px' },
  { value: 'translate-x-20', label: '80px' },
  { value: 'translate-x-24', label: '96px' },
  { value: 'translate-x-32', label: '128px' },
  { value: 'translate-x-40', label: '160px' },
  { value: 'translate-x-48', label: '192px' },
  { value: 'translate-x-56', label: '224px' },
  { value: 'translate-x-64', label: '256px' },
  { value: 'translate-x-1/2', label: '50%' },
  { value: 'translate-x-1/3', label: '33.33%' },
  { value: 'translate-x-2/3', label: '66.67%' },
  { value: 'translate-x-1/4', label: '25%' },
  { value: 'translate-x-2/4', label: '50%' },
  { value: 'translate-x-3/4', label: '75%' },
  { value: 'translate-x-full', label: '100%' },
  { value: '-translate-x-px', label: '-1px' },
  { value: '-translate-x-0.5', label: '-2px' },
  { value: '-translate-x-1', label: '-4px' },
  { value: '-translate-x-1.5', label: '-6px' },
  { value: '-translate-x-2', label: '-8px' },
  { value: '-translate-x-2.5', label: '-10px' },
  { value: '-translate-x-3', label: '-12px' },
  { value: '-translate-x-4', label: '-16px' },
  { value: '-translate-x-5', label: '-20px' },
  { value: '-translate-x-6', label: '-24px' },
  { value: '-translate-x-8', label: '-32px' },
  { value: '-translate-x-10', label: '-40px' },
  { value: '-translate-x-12', label: '-48px' },
  { value: '-translate-x-16', label: '-64px' },
  { value: '-translate-x-20', label: '-80px' },
  { value: '-translate-x-24', label: '-96px' },
  { value: '-translate-x-32', label: '-128px' },
  { value: '-translate-x-40', label: '-160px' },
  { value: '-translate-x-48', label: '-192px' },
  { value: '-translate-x-56', label: '-224px' },
  { value: '-translate-x-64', label: '-256px' },
  { value: '-translate-x-1/2', label: '-50%' },
  { value: '-translate-x-1/3', label: '-33.33%' },
  { value: '-translate-x-2/3', label: '-66.67%' },
  { value: '-translate-x-1/4', label: '-25%' },
  { value: '-translate-x-2/4', label: '-50%' },
  { value: '-translate-x-3/4', label: '-75%' },
  { value: '-translate-x-full', label: '-100%' }
];

const TRANSLATE_Y_OPTIONS = [
  { value: 'none', label: 'Không' },
  { value: 'translate-y-0', label: '0px' },
  { value: 'translate-y-px', label: '1px' },
  { value: 'translate-y-0.5', label: '2px' },
  { value: 'translate-y-1', label: '4px' },
  { value: 'translate-y-1.5', label: '6px' },
  { value: 'translate-y-2', label: '8px' },
  { value: 'translate-y-2.5', label: '10px' },
  { value: 'translate-y-3', label: '12px' },
  { value: 'translate-y-4', label: '16px' },
  { value: 'translate-y-5', label: '20px' },
  { value: 'translate-y-6', label: '24px' },
  { value: 'translate-y-8', label: '32px' },
  { value: 'translate-y-10', label: '40px' },
  { value: 'translate-y-12', label: '48px' },
  { value: 'translate-y-16', label: '64px' },
  { value: 'translate-y-20', label: '80px' },
  { value: 'translate-y-24', label: '96px' },
  { value: 'translate-y-32', label: '128px' },
  { value: 'translate-y-40', label: '160px' },
  { value: 'translate-y-48', label: '192px' },
  { value: 'translate-y-56', label: '224px' },
  { value: 'translate-y-64', label: '256px' },
  { value: 'translate-y-1/2', label: '50%' },
  { value: 'translate-y-1/3', label: '33.33%' },
  { value: 'translate-y-2/3', label: '66.67%' },
  { value: 'translate-y-1/4', label: '25%' },
  { value: 'translate-y-2/4', label: '50%' },
  { value: 'translate-y-3/4', label: '75%' },
  { value: 'translate-y-full', label: '100%' },
  { value: '-translate-y-px', label: '-1px' },
  { value: '-translate-y-0.5', label: '-2px' },
  { value: '-translate-y-1', label: '-4px' },
  { value: '-translate-y-1.5', label: '-6px' },
  { value: '-translate-y-2', label: '-8px' },
  { value: '-translate-y-2.5', label: '-10px' },
  { value: '-translate-y-3', label: '-12px' },
  { value: '-translate-y-4', label: '-16px' },
  { value: '-translate-y-5', label: '-20px' },
  { value: '-translate-y-6', label: '-24px' },
  { value: '-translate-y-8', label: '-32px' },
  { value: '-translate-y-10', label: '-40px' },
  { value: '-translate-y-12', label: '-48px' },
  { value: '-translate-y-16', label: '-64px' },
  { value: '-translate-y-20', label: '-80px' },
  { value: '-translate-y-24', label: '-96px' },
  { value: '-translate-y-32', label: '-128px' },
  { value: '-translate-y-40', label: '-160px' },
  { value: '-translate-y-48', label: '-192px' },
  { value: '-translate-y-56', label: '-224px' },
  { value: '-translate-y-64', label: '-256px' },
  { value: '-translate-y-1/2', label: '-50%' },
  { value: '-translate-y-1/3', label: '-33.33%' },
  { value: '-translate-y-2/3', label: '-66.67%' },
  { value: '-translate-y-1/4', label: '-25%' },
  { value: '-translate-y-2/4', label: '-50%' },
  { value: '-translate-y-3/4', label: '-75%' },
  { value: '-translate-y-full', label: '-100%' }
];

const SKEW_X_OPTIONS = [
  { value: 'none', label: 'Không' },
  { value: 'skew-x-0', label: '0°' },
  { value: 'skew-x-1', label: '1°' },
  { value: 'skew-x-2', label: '2°' },
  { value: 'skew-x-3', label: '3°' },
  { value: 'skew-x-6', label: '6°' },
  { value: 'skew-x-12', label: '12°' },
  { value: '-skew-x-1', label: '-1°' },
  { value: '-skew-x-2', label: '-2°' },
  { value: '-skew-x-3', label: '-3°' },
  { value: '-skew-x-6', label: '-6°' },
  { value: '-skew-x-12', label: '-12°' }
];

const SKEW_Y_OPTIONS = [
  { value: 'none', label: 'Không' },
  { value: 'skew-y-0', label: '0°' },
  { value: 'skew-y-1', label: '1°' },
  { value: 'skew-y-2', label: '2°' },
  { value: 'skew-y-3', label: '3°' },
  { value: 'skew-y-6', label: '6°' },
  { value: 'skew-y-12', label: '12°' },
  { value: '-skew-y-1', label: '-1°' },
  { value: '-skew-y-2', label: '-2°' },
  { value: '-skew-y-3', label: '-3°' },
  { value: '-skew-y-6', label: '-6°' },
  { value: '-skew-y-12', label: '-12°' }
];

const TRANSFORM_ORIGIN_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'origin-center', label: 'Center' },
  { value: 'origin-top', label: 'Top' },
  { value: 'origin-top-right', label: 'Top Right' },
  { value: 'origin-right', label: 'Right' },
  { value: 'origin-bottom-right', label: 'Bottom Right' },
  { value: 'origin-bottom', label: 'Bottom' },
  { value: 'origin-bottom-left', label: 'Bottom Left' },
  { value: 'origin-left', label: 'Left' },
  { value: 'origin-top-left', label: 'Top Left' }
];

const removeTransformClasses = (className: string): string => {
  return className
    .split(' ')
    .filter(cls => 
      !cls.startsWith('scale') &&
      !cls.startsWith('rotate') &&
      !cls.startsWith('-rotate') &&
      !cls.startsWith('translate') &&
      !cls.startsWith('-translate') &&
      !cls.startsWith('skew') &&
      !cls.startsWith('-skew') &&
      !cls.startsWith('origin')
    )
    .join(' ')
    .trim();
};

const TransformSectionComponent: React.FC<TransformSectionProps> = ({ node, onChange, disabled }) => {
  const currentClassName = node.style?.className || '';
  
  const currentState = useMemo(() => {
    const scale = safeMatch(currentClassName, /\bscale-\d+\b/, 'none');
    const rotate = safeMatch(currentClassName, /\b-?rotate-\d+\b/, 'none');
    const translateX = safeMatch(currentClassName, /\b-?translate-x-\S+\b/, 'none');
    const translateY = safeMatch(currentClassName, /\b-?translate-y-\S+\b/, 'none');
    const skewX = safeMatch(currentClassName, /\b-?skew-x-\d+\b/, 'none');
    const skewY = safeMatch(currentClassName, /\b-?skew-y-\d+\b/, 'none');
    const origin = safeMatch(currentClassName, /\borigin-\S+\b/, 'default');

    return { scale, rotate, translateX, translateY, skewX, skewY, origin };
  }, [currentClassName]);

  const cleanClassName = useMemo(() => {
    return removeTransformClasses(currentClassName);
  }, [currentClassName]);

  const updateClassName = (newClassName: string) => {
    onChange({
      style: {
        ...node.style,
        className: newClassName
      }
    });
  };

  const handleChange = useCallback((type: string, value: string) => {
    const newState = { ...currentState, [type]: value };
    
    const classes = [
      cleanClassName,
      newState.scale !== 'none' ? newState.scale : '',
      newState.rotate !== 'none' ? newState.rotate : '',
      newState.translateX !== 'none' ? newState.translateX : '',
      newState.translateY !== 'none' ? newState.translateY : '',
      newState.skewX !== 'none' ? newState.skewX : '',
      newState.skewY !== 'none' ? newState.skewY : '',
      newState.origin !== 'default' ? newState.origin : ''
    ].filter(Boolean);
    
    updateClassName(combineClassNames(...classes));
  }, [cleanClassName, currentState]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        {/* Left Column */}
        <div className="space-y-3">
          <div className="space-y-2">
            <Label htmlFor="transform-scale" className="text-xs">Tỷ lệ</Label>
            <Select 
              value={currentState.scale} 
              onValueChange={(value) => handleChange('scale', value)}
              disabled={disabled}
            >
              <SelectTrigger id="transform-scale">
                <SelectValue placeholder="100%" />
              </SelectTrigger>
              <SelectContent>
                {SCALE_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="transform-translate-x" className="text-xs">Dịch ngang</Label>
            <Select 
              value={currentState.translateX} 
              onValueChange={(value) => handleChange('translateX', value)}
              disabled={disabled}
            >
              <SelectTrigger id="transform-translate-x">
                <SelectValue placeholder="0px" />
              </SelectTrigger>
              <SelectContent>
                {TRANSLATE_X_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="transform-skew-x" className="text-xs">Nghiêng ngang</Label>
            <Select 
              value={currentState.skewX} 
              onValueChange={(value) => handleChange('skewX', value)}
              disabled={disabled}
            >
              <SelectTrigger id="transform-skew-x">
                <SelectValue placeholder="0°" />
              </SelectTrigger>
              <SelectContent>
                {SKEW_X_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-3">
          <div className="space-y-2">
            <Label htmlFor="transform-rotate" className="text-xs">Xoay</Label>
            <Select 
              value={currentState.rotate} 
              onValueChange={(value) => handleChange('rotate', value)}
              disabled={disabled}
            >
              <SelectTrigger id="transform-rotate">
                <SelectValue placeholder="0°" />
              </SelectTrigger>
              <SelectContent>
                {ROTATE_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="transform-translate-y" className="text-xs">Dịch dọc</Label>
            <Select 
              value={currentState.translateY} 
              onValueChange={(value) => handleChange('translateY', value)}
              disabled={disabled}
            >
              <SelectTrigger id="transform-translate-y">
                <SelectValue placeholder="0px" />
              </SelectTrigger>
              <SelectContent>
                {TRANSLATE_Y_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="transform-skew-y" className="text-xs">Nghiêng dọc</Label>
            <Select 
              value={currentState.skewY} 
              onValueChange={(value) => handleChange('skewY', value)}
              disabled={disabled}
            >
              <SelectTrigger id="transform-skew-y">
                <SelectValue placeholder="0°" />
              </SelectTrigger>
              <SelectContent>
                {SKEW_Y_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Transform Origin - Full Width */}
      <div className="space-y-2">
        <Label htmlFor="transform-origin" className="text-xs">Gốc biến đổi</Label>
        <Select 
          value={currentState.origin} 
          onValueChange={(value) => handleChange('origin', value)}
          disabled={disabled}
        >
          <SelectTrigger id="transform-origin">
            <SelectValue placeholder="Center" />
          </SelectTrigger>
          <SelectContent>
            {TRANSFORM_ORIGIN_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export const TransformSection = React.memo(TransformSectionComponent);
