/**
 * GPU-Optimized CSS for 60fps Animations
 * 
 * This file contains CSS classes optimized for hardware acceleration
 * to achieve smooth 60fps animations throughout the builder interface.
 */

/* Base GPU acceleration for commonly animated elements */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimized drag and drop animations */
.drag-preview {
  transform: translateZ(0) scale(0.95);
  will-change: transform, opacity;
  backface-visibility: hidden;
  transition: transform 120ms cubic-bezier(0.4, 0, 0.2, 1);
}

.drag-preview-active {
  transform: translateZ(0) scale(1.02);
}

/* Optimized drop indicators */
.drop-indicator {
  transform: translateZ(0);
  will-change: opacity, transform;
  backface-visibility: hidden;
}

.drop-indicator-pulse {
  animation: gpu-pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes gpu-pulse {
  0%, 100% {
    opacity: 1;
    transform: translateZ(0) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translateZ(0) scale(1.05);
  }
}

/* Optimized tree node interactions */
.tree-node {
  transform: translateZ(0);
  will-change: background-color, border-color, transform;
  backface-visibility: hidden;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1),
              border-color 150ms cubic-bezier(0.4, 0, 0.2, 1),
              transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.tree-node:hover {
  transform: translateZ(0) translateX(2px);
}

.tree-node-selected {
  transform: translateZ(0) translateX(4px);
}

.tree-node-dragging {
  transform: translateZ(0) scale(0.98);
  opacity: 0.7;
}

/* Optimized button interactions */
.gpu-button {
  transform: translateZ(0);
  will-change: transform, box-shadow;
  backface-visibility: hidden;
  transition: transform 120ms cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 120ms cubic-bezier(0.4, 0, 0.2, 1);
}

.gpu-button:hover {
  transform: translateZ(0) translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.gpu-button:active {
  transform: translateZ(0) translateY(0px) scale(0.98);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Optimized expand/collapse animations */
.expand-collapse {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  transition: transform 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.expand-collapse-expanded {
  transform: translateZ(0) rotate(90deg);
}

/* Optimized loading animations */
.gpu-spinner {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  animation: gpu-spin 1s linear infinite;
}

@keyframes gpu-spin {
  from {
    transform: translateZ(0) rotate(0deg);
  }
  to {
    transform: translateZ(0) rotate(360deg);
  }
}

/* Optimized fade transitions */
.gpu-fade-in {
  transform: translateZ(0);
  will-change: opacity, transform;
  backface-visibility: hidden;
  animation: gpu-fade-in 200ms cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes gpu-fade-in {
  from {
    opacity: 0;
    transform: translateZ(0) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateZ(0) translateY(0);
  }
}

.gpu-fade-out {
  transform: translateZ(0);
  will-change: opacity, transform;
  backface-visibility: hidden;
  animation: gpu-fade-out 150ms cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes gpu-fade-out {
  from {
    opacity: 1;
    transform: translateZ(0) translateY(0);
  }
  to {
    opacity: 0;
    transform: translateZ(0) translateY(-10px);
  }
}

/* Optimized scale animations for popovers and dialogs */
.gpu-scale-in {
  transform: translateZ(0) scale(0.95);
  will-change: transform, opacity;
  backface-visibility: hidden;
  animation: gpu-scale-in 150ms cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes gpu-scale-in {
  from {
    opacity: 0;
    transform: translateZ(0) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateZ(0) scale(1);
  }
}

.gpu-scale-out {
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
  animation: gpu-scale-out 100ms cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes gpu-scale-out {
  from {
    opacity: 1;
    transform: translateZ(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateZ(0) scale(0.95);
  }
}

/* Optimized border animations for form controls */
.gpu-border-focus {
  transform: translateZ(0);
  will-change: border-color, box-shadow;
  backface-visibility: hidden;
  transition: border-color 150ms cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Performance utilities */
.force-gpu {
  transform: translateZ(0);
  will-change: auto;
  backface-visibility: hidden;
}

.disable-gpu {
  transform: none;
  will-change: auto;
  backface-visibility: visible;
}

/* Container queries optimization */
@container (min-width: 768px) {
  .gpu-responsive {
    transform: translateZ(0);
    will-change: transform;
  }
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .gpu-accelerated,
  .drag-preview,
  .tree-node,
  .gpu-button,
  .expand-collapse {
    transition: none;
    animation: none;
  }
  
  .drop-indicator-pulse,
  .gpu-spinner {
    animation: none;
  }
}