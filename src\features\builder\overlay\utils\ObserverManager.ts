/**
 * Centralized observer manager đ<PERSON> tr<PERSON>h tạo quá nhiều observers
 */
export class ObserverManager {
  private static instance: ObserverManager;
  private resizeObserver: ResizeObserver;
  private mutationObserver: MutationObserver;
  private callbacks = new Map<string, () => void>();
  private observedElements = new Set<Element>();

  private constructor() {
    // Single ResizeObserver cho tất cả elements
    this.resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const element = entry.target;
        const nodeId = element.getAttribute('data-node-id');
        if (nodeId) {
          const callback = this.callbacks.get(nodeId);
          callback?.();
        }
      }
    });

    // Single MutationObserver cho tất cả elements
    this.mutationObserver = new MutationObserver((mutations) => {
      const updatedNodes = new Set<string>();
      
      for (const mutation of mutations) {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'class' || mutation.attributeName === 'style')) {
          const element = mutation.target as Element;
          const nodeId = element.getAttribute('data-node-id');
          if (nodeId && !updatedNodes.has(nodeId)) {
            updatedNodes.add(nodeId);
            const callback = this.callbacks.get(nodeId);
            callback?.();
          }
        }
      }
    });
  }

  static getInstance(): ObserverManager {
    if (!ObserverManager.instance) {
      ObserverManager.instance = new ObserverManager();
    }
    return ObserverManager.instance;
  }

  observe(nodeId: string, element: Element, callback: () => void) {
    try {
      this.callbacks.set(nodeId, callback);
      
      if (!this.observedElements.has(element)) {
        this.resizeObserver.observe(element);
        this.mutationObserver.observe(element, {
          attributes: true,
          attributeFilter: ['class', 'style']
        });
        this.observedElements.add(element);
      }
    } catch {
      // Silently fail - element might not support observers
    }
  }

  unobserve(nodeId: string, element: Element) {
    try {
      this.callbacks.delete(nodeId);
      
      // Only unobserve if no other nodes need this element
      const hasOtherCallbacks = Array.from(this.callbacks.values()).length > 0;
      if (!hasOtherCallbacks && this.observedElements.has(element)) {
        this.resizeObserver.unobserve(element);
        this.observedElements.delete(element);
      }
    } catch {
      // Silently fail
    }
  }

  cleanup() {
    this.resizeObserver.disconnect();
    this.mutationObserver.disconnect();
    this.callbacks.clear();
    this.observedElements.clear();
  }
}