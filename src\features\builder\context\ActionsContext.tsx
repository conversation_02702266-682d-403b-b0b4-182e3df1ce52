import React, { createContext, useContext, useCallback, useMemo } from 'react';
import { Post, TreeNode, TreeNodeType } from './types';
import { DropPosition } from '../types/drag-drop';
import { calculateMoveFromPosition } from '../utils/move-calculator';
import { initializeCountersFromPost } from '../utils/node-naming';
import { BuilderAction, ACTION_TYPES } from './actions';
import { HistoryManager } from './history';

// Actions-focused context for CRUD operations
interface ActionsContextValue {
  // Post operations
  setPost: (post: Post) => void;
  updatePost: (updates: Partial<Post>) => void;
  savePost: () => Promise<void>;

  // Node operations
  addNode: (parentId: string, nodeType: TreeNodeType, index?: number) => void;
  updateNode: (nodeId: string, updates: Partial<TreeNode>) => void;
  deleteNode: (nodeId: string) => void;
  moveNode: (nodeId: string, targetNodeId: string, positionOrIndex: DropPosition | number) => void;

  // Copy/Paste operations
  copyNode: (nodeId: string) => void;
  pasteNode: () => void;
  hasCopiedNode: () => boolean;

  // History operations
  undo: () => void;
  redo: () => void;
  canUndo: () => boolean;
  canRedo: () => boolean;
}

const ActionsContext = createContext<ActionsContextValue | null>(null);

interface ActionsProviderProps {
  children: React.ReactNode;
  post: Post | null;
  copiedNode: TreeNode | null;
  dispatch: (action: BuilderAction) => void;
  dispatchWithoutHistory: (action: BuilderAction) => void;
  onSave?: (post: Post) => void | Promise<void>;
  historyManager: HistoryManager | null;
}

export const ActionsProvider: React.FC<ActionsProviderProps> = ({
  children,
  post,
  copiedNode,
  dispatch,
  dispatchWithoutHistory,
  onSave,
  historyManager
}) => {
  // Post operations
  const setPost = useCallback((post: Post) => {
    // Initialize counters from existing post content
    initializeCountersFromPost(post.content);
    dispatch({ type: ACTION_TYPES.SET_POST, payload: post });
  }, [dispatch]);

  const updatePost = useCallback((updates: Partial<Post>) => {
    dispatch({ type: ACTION_TYPES.UPDATE_POST, payload: updates });
  }, [dispatch]);

  const savePost = useCallback(async () => {
    if (!post || !onSave) return;

    try {
      console.log('Saving post:', post);

      await onSave(post);
      dispatch({ type: ACTION_TYPES.SET_DIRTY, payload: false });
      dispatch({ type: ACTION_TYPES.SET_LAST_SAVED, payload: Date.now() });
    } catch (error) {
      console.error('Failed to save post:', error);
      throw error;
    }
  }, [post, onSave, dispatch]);

  // Node operations
  const addNode = useCallback((parentId: string, nodeType: TreeNodeType, index?: number) => {
    dispatch({ type: ACTION_TYPES.ADD_NODE, payload: { parentId, nodeType, index } });
  }, [dispatch]);

  const updateNode = useCallback((nodeId: string, updates: Partial<TreeNode>) => {
    dispatch({ type: ACTION_TYPES.UPDATE_NODE, payload: { nodeId, updates } });
  }, [dispatch]);

  const deleteNode = useCallback((nodeId: string) => {
    dispatch({ type: ACTION_TYPES.DELETE_NODE, payload: nodeId });
  }, [dispatch]);

  const moveNode = useCallback((nodeId: string, targetNodeId: string, positionOrIndex: DropPosition | number) => {
    if (typeof positionOrIndex === 'number') {
      // Legacy index-based move
      dispatch({ type: ACTION_TYPES.MOVE_NODE, payload: { nodeId, newParentId: targetNodeId, index: positionOrIndex } });
    } else {
      // Position-based move - calculate actual parent and index
      if (!post) {
        throw new Error('No post available for move calculation');
      }

      const calculatedMove = calculateMoveFromPosition(
        post.content,
        nodeId,
        targetNodeId,
        positionOrIndex
      );

      dispatch({
        type: ACTION_TYPES.MOVE_NODE, payload: {
          nodeId: calculatedMove.sourceNodeId,
          newParentId: calculatedMove.targetParentId,
          index: calculatedMove.targetIndex
        }
      });
    }
  }, [dispatch, post]);

  // Copy/Paste operations
  const copyNode = useCallback((nodeId: string) => {
    dispatch({ type: ACTION_TYPES.COPY_NODE, payload: nodeId });
  }, [dispatch]);

  const pasteNode = useCallback(() => {
    dispatch({ type: ACTION_TYPES.PASTE_NODE });
  }, [dispatch]);

  const hasCopiedNode = useCallback(() => {
    return copiedNode !== null;
  }, [copiedNode]);

  // History operations
  const undo = useCallback(() => {
    if (!historyManager) return;

    const previousState = historyManager.undo();
    if (previousState) {
      dispatchWithoutHistory({ type: ACTION_TYPES.RESTORE_STATE, payload: previousState });
    }
  }, [historyManager, dispatchWithoutHistory]);

  const redo = useCallback(() => {
    if (!historyManager) return;

    const nextState = historyManager.redo();
    if (nextState) {
      dispatchWithoutHistory({ type: ACTION_TYPES.RESTORE_STATE, payload: nextState });
    }
  }, [historyManager, dispatchWithoutHistory]);

  const canUndo = useCallback(() => {
    return historyManager?.canUndo() || false;
  }, [historyManager]);

  const canRedo = useCallback(() => {
    return historyManager?.canRedo() || false;
  }, [historyManager]);

  const contextValue = useMemo<ActionsContextValue>(() => ({
    setPost,
    updatePost,
    savePost,
    addNode,
    updateNode,
    deleteNode,
    moveNode,
    copyNode,
    pasteNode,
    hasCopiedNode,
    undo,
    redo,
    canUndo,
    canRedo
  }), [
    setPost,
    updatePost,
    savePost,
    addNode,
    updateNode,
    deleteNode,
    moveNode,
    copyNode,
    pasteNode,
    hasCopiedNode,
    undo,
    redo,
    canUndo,
    canRedo
  ]);

  return (
    <ActionsContext.Provider value={contextValue}>
      {children}
    </ActionsContext.Provider>
  );
};

export const useActions = (): ActionsContextValue => {
  const context = useContext(ActionsContext);
  if (!context) {
    throw new Error('useActions must be used within an ActionsProvider');
  }
  return context;
};