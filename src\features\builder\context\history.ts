import {
  BuilderState,
  HistoryState,
  HistoryAction,
  UIHistoryAction,
  TreeNode,
  PersistentUIState,
  TemporaryUIState,
} from "./types";
import { BuilderAction, ACTION_TYPES } from "./actions";
import { HISTORY_CONFIG, UI_PERSISTENCE_CONFIG } from "../config/constants";
import { cloneNode } from "../utils/tree";

/**
 * History management with event sourcing and snapshots
 */

export class HistoryManager {
  private state: HistoryState;
  private pendingTextChanges: Map<
    string,
    { timer: number; changes: string[] }
  > = new Map();

  constructor(initialState: BuilderState) {
    this.state = {
      baseState: this.cloneState(initialState),
      actions: [],
      currentIndex: -1,
      snapshots: new Map(),
    };
  }

  // Clone state deeply
  private cloneState(state: BuilderState): BuilderState {
    return {
      post: state.post
        ? {
            ...state.post,
            content: cloneNode(state.post.content),
          }
        : null,
      uiState: {
        ...state.uiState,
        expandedNodeIds: new Set(state.uiState.expandedNodeIds),
      },
    };
  }

  // Create UI history action
  pushUIChange(
    category: "persistent" | "temporary",
    changes: Partial<PersistentUIState> | Partial<TemporaryUIState>,
    newState: BuilderState,
    description?: string
  ): void {
    const uiAction: UIHistoryAction = {
      type: "UI_CHANGE",
      payload: { category, changes },
      timestamp: Date.now(),
      description:
        description || `UI change: ${Object.keys(changes).join(", ")}`,
    };

    this.push(uiAction as unknown as BuilderAction, newState, description);
  }

  // Push new action to history
  push(
    action: BuilderAction,
    newState: BuilderState,
    description?: string
  ): void {
    // Remove any actions after current index (for redo)
    if (this.currentIndex < this.actions.length - 1) {
      this.actions.splice(this.currentIndex + 1);

      // Remove obsolete snapshots
      const snapshotKeys = Array.from(this.state.snapshots.keys());
      snapshotKeys.forEach((key) => {
        if (key > this.currentIndex) {
          this.state.snapshots.delete(key);
        }
      });
    }

    // Group text changes
    if (this.shouldGroupTextChange(action)) {
      this.handleTextGrouping(action, newState, description);
      return;
    }

    // Skip UI changes that shouldn't be tracked
    if (action.type === "SET_UI_STATE" && !this.shouldTrackUIChange(action)) {
      return;
    }

    // Create history action
    const historyAction: HistoryAction = {
      type: action.type,
      payload: "payload" in action ? action.payload : undefined,
      timestamp: Date.now(),
      description,
    };

    // Add action
    this.actions.push(historyAction);
    this.currentIndex++;

    // Always create snapshot for undo/redo capability
    // But use smart snapshotting to avoid performance issues
    this.createSnapshot(newState);

    // Trim history if exceeds max
    if (this.actions.length > HISTORY_CONFIG.MAX_ACTIONS) {
      this.trimHistory();
    }
  }

  // Check if text changes should be grouped
  private shouldGroupTextChange(action: BuilderAction): boolean {
    if (action.type === "UPDATE_NODE") {
      const payload = action.payload as {
        nodeId: string;
        updates: Partial<TreeNode>;
      };
      return payload.updates?.properties?.content !== undefined;
    }
    return false;
  }

  // Check if UI change should be tracked in history
  private shouldTrackUIChange(action: BuilderAction): boolean {
    if (action.type === "SET_UI_STATE") {
      const payload = action.payload as Partial<
        PersistentUIState | TemporaryUIState
      >;
      const changedKeys = Object.keys(payload);

      // Check if any changed key is in trackable list
      return changedKeys.some((key) =>
        (
          UI_PERSISTENCE_CONFIG.TRACKABLE_UI_CHANGES as readonly string[]
        ).includes(key)
      );
    }
    return false;
  }

  // Handle text change grouping
  private handleTextGrouping(
    action: BuilderAction,
    _newState: BuilderState,
    description?: string
  ): void {
    if (action.type !== ACTION_TYPES.UPDATE_NODE) return;
    const payload = action.payload;
    const nodeId = payload.nodeId;
    const existing = this.pendingTextChanges.get(nodeId);

    if (existing) {
      clearTimeout(existing.timer);
      existing.changes.push(JSON.stringify(action.payload));
    } else {
      this.pendingTextChanges.set(nodeId, {
        changes: [JSON.stringify(action.payload)],
        timer: 0,
      });
    }

    const timer = window.setTimeout(() => {
      const pending = this.pendingTextChanges.get(nodeId);
      if (pending) {
        // Create grouped action
        const historyAction: HistoryAction = {
          type: action.type,
          payload: action.payload,
          timestamp: Date.now(),
          description: description || "Text edit",
        };

        this.actions.push(historyAction);
        this.currentIndex++;

        this.pendingTextChanges.delete(nodeId);
      }
    }, HISTORY_CONFIG.TEXT_GROUPING_DELAY);

    this.pendingTextChanges.get(nodeId)!.timer = timer;
  }

  // Trim old history
  private trimHistory(): void {
    const excess = this.actions.length - HISTORY_CONFIG.MAX_ACTIONS;
    if (excess <= 0) return;

    // Remove old actions
    this.actions.splice(0, excess);
    this.currentIndex -= excess;

    // Update base state to the oldest snapshot or create new one
    const oldestSnapshotIndex = Math.min(
      ...Array.from(this.state.snapshots.keys())
    );
    if (oldestSnapshotIndex >= excess) {
      // Adjust snapshot indices
      const newSnapshots = new Map<number, BuilderState>();
      this.state.snapshots.forEach((snapshot, index) => {
        newSnapshots.set(index - excess, snapshot);
      });
      this.state.snapshots = newSnapshots;
    } else {
      // Reconstruct base state by replaying actions
      try {
        const firstSnapshotIndex = Math.min(...this.state.snapshots.keys());
        if (firstSnapshotIndex < excess) {
          this.state.baseState =
            this.state.snapshots.get(firstSnapshotIndex) ||
            this.state.baseState;
          this.state.actions = this.state.actions.slice(excess);
          this.currentIndex = Math.max(0, this.currentIndex - excess);

          // Adjust snapshot indices
          const newSnapshots = new Map<number, BuilderState>();
          this.state.snapshots.forEach((snapshot, index) => {
            if (index >= excess) {
              newSnapshots.set(index - excess, snapshot);
            }
          });
          this.state.snapshots = newSnapshots;
        }
      } catch {
        // Fallback: keep only recent actions without reconstruction
        this.state.actions = this.state.actions.slice(
          -HISTORY_CONFIG.MAX_ACTIONS / 2
        );
        this.currentIndex = Math.min(
          this.currentIndex,
          this.state.actions.length - 1
        );
        this.state.snapshots.clear();
      }
    }
  }

  // Get current state
  getCurrentState(): HistoryState {
    return this.state;
  }

  // Get actions
  get actions(): HistoryAction[] {
    return this.state.actions;
  }

  // Get current index
  get currentIndex(): number {
    return this.state.currentIndex;
  }

  // Set current index
  set currentIndex(index: number) {
    this.state.currentIndex = index;
  }

  // Can undo
  canUndo(): boolean {
    return this.currentIndex >= 0;
  }

  // Can redo
  canRedo(): boolean {
    return this.currentIndex < this.actions.length - 1;
  }

  // Get action at index
  getAction(index: number): HistoryAction | null {
    return this.actions[index] || null;
  }

  // Find nearest snapshot
  findNearestSnapshot(
    targetIndex: number
  ): { index: number; state: BuilderState } | null {
    let nearestIndex = -1;
    let nearestState: BuilderState | null = null;

    this.state.snapshots.forEach((state, index) => {
      if (index <= targetIndex && index > nearestIndex) {
        nearestIndex = index;
        nearestState = state;
      }
    });

    if (nearestState) {
      return { index: nearestIndex, state: nearestState };
    }

    return null;
  }

  // Undo operation - return the target state
  undo(): BuilderState | null {
    if (!this.canUndo()) return null;

    this.currentIndex--;

    // Find the state for this index
    return this.getStateAtIndex(this.currentIndex);
  }

  // Redo operation
  redo(): BuilderState | null {
    if (!this.canRedo()) return null;

    this.currentIndex++;

    // Find the state for this index
    return this.getStateAtIndex(this.currentIndex);
  }

  // Get state at specific index (for undo/redo)
  private getStateAtIndex(targetIndex: number): BuilderState {
    // Check if we have exact snapshot
    const exactSnapshot = this.state.snapshots.get(targetIndex);
    if (exactSnapshot) {
      return this.cloneState(exactSnapshot);
    }

    // Find nearest snapshot
    const nearestSnapshot = this.findNearestSnapshot(targetIndex);

    if (nearestSnapshot) {
      return this.cloneState(nearestSnapshot.state);
    }

    // Fallback to base state
    return this.cloneState(this.state.baseState);
  }

  // Store current state as snapshot (for undo/redo points)
  createSnapshot(currentState: BuilderState): void {
    // For now, create lightweight snapshots by only storing essential data
    // TODO: Implement proper action reversal for better performance

    const lightweightState: BuilderState = {
      post: currentState.post
        ? {
            ...currentState.post,
            content: this.cloneLightweight(currentState.post.content),
          }
        : null,
      uiState: {
        ...currentState.uiState,
        expandedNodeIds: new Set(currentState.uiState.expandedNodeIds),
      },
    };

    this.state.snapshots.set(this.currentIndex, lightweightState);
  }

  // Lightweight clone - only clone structure, not deep properties
  private cloneLightweight(node: TreeNode): TreeNode {
    return {
      ...node,
      children: node.children.map((child) => this.cloneLightweight(child)),
    };
  }

  // Export history for storage
  export(): Partial<HistoryState> {
    return {
      baseState: this.state.baseState,
      actions: this.state.actions,
      currentIndex: this.state.currentIndex,
      // Don't export snapshots to save space
    };
  }

  // Import history from storage
  import(data: Partial<HistoryState>): void {
    if (data.baseState) {
      this.state.baseState = data.baseState;
    }
    if (data.actions) {
      this.state.actions = data.actions;
    }
    if (data.currentIndex !== undefined) {
      this.state.currentIndex = data.currentIndex;
    }
    // Snapshots will be recreated as needed
    this.state.snapshots = new Map();
  }

  // Clear all history
  clear(): void {
    // Cancel pending text changes
    this.pendingTextChanges.forEach((pending) => {
      clearTimeout(pending.timer);
    });
    this.pendingTextChanges.clear();

    // Reset state
    this.state.actions = [];
    this.state.currentIndex = -1;
    this.state.snapshots.clear();
  }
}
