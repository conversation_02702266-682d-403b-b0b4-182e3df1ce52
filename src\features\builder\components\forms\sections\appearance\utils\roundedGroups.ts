export interface RoundedOption {
  value: string;
  label: string;
  description?: string;
}

export interface RoundedGroup {
  id: string;
  label: string;
  options: RoundedOption[];
}

export const ROUNDED_GROUPS: RoundedGroup[] = [
  {
    id: 'basic',
    label: 'All',
    options: [
      { value: 'rounded-none', label: 'Không có', description: '0px' },
      { value: 'rounded-sm', label: 'sm', description: '2px' },
      { value: 'rounded', label: 'base', description: '4px' },
      { value: 'rounded-md', label: 'md', description: '6px' },
      { value: 'rounded-lg', label: 'lg', description: '8px' },
      { value: 'rounded-xl', label: 'xl', description: '12px' },
      { value: 'rounded-2xl', label: '2xl', description: '16px' },
      { value: 'rounded-3xl', label: '3xl', description: '24px' },
      { value: 'rounded-full', label: 'full', description: '9999px' },
    ]
  },
  {
    id: 'top',
    label: 'Top',
    options: [
      { value: 'rounded-t-none', label: 'Không có', description: '0px' },
      { value: 'rounded-t-sm', label: 'sm', description: '2px' },
      { value: 'rounded-t', label: 'base', description: '4px' },
      { value: 'rounded-t-md', label: 'md', description: '6px' },
      { value: 'rounded-t-lg', label: 'lg', description: '8px' },
      { value: 'rounded-t-xl', label: 'xl', description: '12px' },
      { value: 'rounded-t-2xl', label: '2xl', description: '16px' },
      { value: 'rounded-t-3xl', label: '3xl', description: '24px' },
      { value: 'rounded-t-full', label: 'full', description: '9999px' },
    ]
  },
  {
    id: 'right',
    label: 'Right',
    options: [
      { value: 'rounded-r-none', label: 'Không có', description: '0px' },
      { value: 'rounded-r-sm', label: 'sm', description: '2px' },
      { value: 'rounded-r', label: 'base', description: '4px' },
      { value: 'rounded-r-md', label: 'md', description: '6px' },
      { value: 'rounded-r-lg', label: 'lg', description: '8px' },
      { value: 'rounded-r-xl', label: 'xl', description: '12px' },
      { value: 'rounded-r-2xl', label: '2xl', description: '16px' },
      { value: 'rounded-r-3xl', label: '3xl', description: '24px' },
      { value: 'rounded-r-full', label: 'full', description: '9999px' },
    ]
  },
  {
    id: 'bottom',
    label: 'Bottom',
    options: [
      { value: 'rounded-b-none', label: 'Không có', description: '0px' },
      { value: 'rounded-b-sm', label: 'sm', description: '2px' },
      { value: 'rounded-b', label: 'base', description: '4px' },
      { value: 'rounded-b-md', label: 'md', description: '6px' },
      { value: 'rounded-b-lg', label: 'lg', description: '8px' },
      { value: 'rounded-b-xl', label: 'xl', description: '12px' },
      { value: 'rounded-b-2xl', label: '2xl', description: '16px' },
      { value: 'rounded-b-3xl', label: '3xl', description: '24px' },
      { value: 'rounded-b-full', label: 'full', description: '9999px' },
    ]
  },
  {
    id: 'left',
    label: 'Left',
    options: [
      { value: 'rounded-l-none', label: 'Không có', description: '0px' },
      { value: 'rounded-l-sm', label: 'sm', description: '2px' },
      { value: 'rounded-l', label: 'base', description: '4px' },
      { value: 'rounded-l-md', label: 'md', description: '6px' },
      { value: 'rounded-l-lg', label: 'lg', description: '8px' },
      { value: 'rounded-l-xl', label: 'xl', description: '12px' },
      { value: 'rounded-l-2xl', label: '2xl', description: '16px' },
      { value: 'rounded-l-3xl', label: '3xl', description: '24px' },
      { value: 'rounded-l-full', label: 'full', description: '9999px' },
    ]
  },
  {
    id: 'corners',
    label: 'Corners',
    options: [
      { value: 'rounded-tl-none', label: 'tl-Không có', description: 'Top-left 0px' },
      { value: 'rounded-tl-sm', label: 'tl-sm', description: 'Top-left 2px' },
      { value: 'rounded-tl', label: 'tl-base', description: 'Top-left 4px' },
      { value: 'rounded-tl-md', label: 'tl-md', description: 'Top-left 6px' },
      { value: 'rounded-tl-lg', label: 'tl-lg', description: 'Top-left 8px' },
      { value: 'rounded-tl-xl', label: 'tl-xl', description: 'Top-left 12px' },
      { value: 'rounded-tr-none', label: 'tr-Không có', description: 'Top-right 0px' },
      { value: 'rounded-tr-sm', label: 'tr-sm', description: 'Top-right 2px' },
      { value: 'rounded-tr', label: 'tr-base', description: 'Top-right 4px' },
      { value: 'rounded-tr-md', label: 'tr-md', description: 'Top-right 6px' },
      { value: 'rounded-tr-lg', label: 'tr-lg', description: 'Top-right 8px' },
      { value: 'rounded-tr-xl', label: 'tr-xl', description: 'Top-right 12px' },
      { value: 'rounded-br-none', label: 'br-Không có', description: 'Bottom-right 0px' },
      { value: 'rounded-br-sm', label: 'br-sm', description: 'Bottom-right 2px' },
      { value: 'rounded-br', label: 'br-base', description: 'Bottom-right 4px' },
      { value: 'rounded-br-md', label: 'br-md', description: 'Bottom-right 6px' },
      { value: 'rounded-br-lg', label: 'br-lg', description: 'Bottom-right 8px' },
      { value: 'rounded-br-xl', label: 'br-xl', description: 'Bottom-right 12px' },
      { value: 'rounded-bl-none', label: 'bl-Không có', description: 'Bottom-left 0px' },
      { value: 'rounded-bl-sm', label: 'bl-sm', description: 'Bottom-left 2px' },
      { value: 'rounded-bl', label: 'bl-base', description: 'Bottom-left 4px' },
      { value: 'rounded-bl-md', label: 'bl-md', description: 'Bottom-left 6px' },
      { value: 'rounded-bl-lg', label: 'bl-lg', description: 'Bottom-left 8px' },
      { value: 'rounded-bl-xl', label: 'bl-xl', description: 'Bottom-left 12px' },
    ]
  }
];