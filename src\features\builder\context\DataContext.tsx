import React, { createContext, useContext, useCallback, useMemo } from 'react';
import { Post, TreeNode } from './types';
import { findNodeById, getNodePath } from '../utils/tree';
import { nodeValidation } from './validation';

// Data-focused context for post and node operations
interface DataContextValue {
  post: Post | null;
  
  // Node utilities
  findNodeById: (nodeId: string) => TreeNode | null;
  getSelectedNode: () => TreeNode | null;
  getNodePath: (nodeId: string) => TreeNode[];
  canAddChildToNode: (nodeId: string) => boolean;
}

const DataContext = createContext<DataContextValue | null>(null);

interface DataProviderProps {
  children: React.ReactNode;
  post: Post | null;
  selectedNodeId?: string | null;
}

export const DataProvider: React.FC<DataProviderProps> = ({ children, post, selectedNodeId }) => {
  // Memoized utilities that only depend on post
  const findNodeByIdUtil = useCallback((nodeId: string): TreeNode | null => {
    if (!post) return null;
    return findNodeById(post.content, nodeId);
  }, [post]);

  const getSelectedNodeUtil = useCallback((): TreeNode | null => {
    if (!selectedNodeId || !post) return null;
    return findNodeById(post.content, selectedNodeId);
  }, [selectedNodeId, post]);

  const getNodePathUtil = useCallback((nodeId: string): TreeNode[] => {
    if (!post) return [];
    return getNodePath(post.content, nodeId);
  }, [post]);

  const canAddChildToNode = useCallback((nodeId: string): boolean => {
    if (!post) return false;
    const node = findNodeById(post.content, nodeId);
    return node ? nodeValidation.canHaveChildren(node.type) : false;
  }, [post]);

  const contextValue = useMemo<DataContextValue>(() => ({
    post,
    findNodeById: findNodeByIdUtil,
    getSelectedNode: getSelectedNodeUtil,
    getNodePath: getNodePathUtil,
    canAddChildToNode
  }), [post, findNodeByIdUtil, getSelectedNodeUtil, getNodePathUtil, canAddChildToNode]);

  return (
    <DataContext.Provider value={contextValue}>
      {children}
    </DataContext.Provider>
  );
};

export const useData = (): DataContextValue => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};