import React, { useState, useEffect } from 'react';
import { NodeRendererProps } from '../NodeFactory';
import { compareBasicNodeProps } from '@/lib/memo-utils';

const DateTimeCardNodeComponent: React.FC<NodeRendererProps & React.HTMLAttributes<HTMLDivElement>> = ({ 
  node,
  ...htmlProps
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);
  
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div 
      className={node.style?.className}
      style={node.style?.css}
      {...htmlProps}
    >
      <div className="p-6 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-xl shadow-lg">
        <div className="text-center space-y-2">
          <div className="text-3xl font-bold font-mono">
            {formatTime(currentTime)}
          </div>
          <div className="text-sm opacity-90">
            {formatDate(currentTime)}
          </div>
        </div>
      </div>
    </div>
  );
};

// Memoized widget node - only re-render when node props change, not time
export const DateTimeCardNode = React.memo(
  DateTimeCardNodeComponent,
  compareBasicNodeProps
);