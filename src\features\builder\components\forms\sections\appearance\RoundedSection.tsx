import React, { useMemo, useCallback } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { TreeNode } from "../../../../context/types";
import { AppearancePopover } from "../../inputs/AppearancePopover";
import { ROUNDED_GROUPS } from "../../utils/roundedGroups";
import {
  parseRoundedState,
  roundedStateToClassName,
  switchRoundedMode,
} from "./appearance/appearanceHelper";
import { removeRoundedClasses } from "../../utils/classNameUtils";
import type { RoundedMode, RoundedState } from "./appearance/types";
import { ROUNDED_PREFIX_MAP } from "./appearance/types";
import { CornersControl } from "./appearance/CornersControl";

interface RoundedSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const RoundedSectionComponent: React.FC<RoundedSectionProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  // Parse current state using helper - this is the ONLY source of truth (MEMOIZED)
  const currentState = useMemo(
    () => parseRoundedState(currentClassName) as RoundedState,
    [currentClassName]
  );

  // Extract only rounded classes for custom mode textarea (MEMOIZED CORRECTLY)
  const roundedClasses = useMemo(() => {
    return currentClassName
      .split(/\s+/)
      .filter((c) => c.startsWith("rounded"))
      .join(" ");
  }, [currentClassName]);

  // Update node className
  const updateClassName = (newClassName: string) => {
    onChange({
      style: {
        ...node.style,
        className: newClassName,
      },
    });
  };

  // Handle rounded mode change (MEMOIZED)
  const handleRoundedModeChange = useCallback(
    (mode: RoundedMode) => {
      const newRounded = switchRoundedMode(currentState, mode);

      const cleanClassName = removeRoundedClasses(currentClassName);
      const roundedClasses = roundedStateToClassName(newRounded);
      updateClassName(`${cleanClassName} ${roundedClasses}`.trim());
    },
    [currentState, currentClassName]
  );

  // Handle rounded value change (for single value modes) (MEMOIZED)
  const handleRoundedValueChange = useCallback(
    (value: string) => {
      // Extract size from value by removing the correct prefix for current mode
      const prefixes = ROUNDED_PREFIX_MAP[currentState.mode as RoundedMode];
      if (prefixes.length === 0) return;

      const prefix = prefixes[0];
      let size = "base";

      if (value === prefix) {
        size = "base";
      } else if (value.startsWith(`${prefix}-`)) {
        size = value.substring(prefix.length + 1);
      }

      const newRounded = {
        ...currentState,
        options: [{ size }],
      };

      const cleanClassName = removeRoundedClasses(currentClassName);
      const roundedClasses = roundedStateToClassName(newRounded);
      updateClassName(`${cleanClassName} ${roundedClasses}`.trim());
    },
    [currentState, currentClassName]
  );

  // Handle corners change (for corners mode) (MEMOIZED)
  const handleCornersChange = useCallback(
    (corners: { tl?: string; tr?: string; bl?: string; br?: string }) => {
      // For corners mode, we need 4 options, one for each corner (TL TR BL BR order)
      const newOptions = [
        { size: corners.tl || "sm" }, // top-left
        { size: corners.tr || "sm" }, // top-right
        { size: corners.bl || "sm" }, // bottom-left
        { size: corners.br || "sm" }, // bottom-right
      ];

      const newRounded = {
        ...currentState,
        options: newOptions,
      };

      const cleanClassName = removeRoundedClasses(currentClassName);
      const roundedClasses = roundedStateToClassName(newRounded);
      updateClassName(`${cleanClassName} ${roundedClasses}`.trim());
    },
    [currentState, currentClassName]
  );

  // Get rounded groups based on mode (MEMOIZED)
  const getRoundedGroups = useMemo(() => {
    switch (currentState.mode) {
      case "all":
        return [ROUNDED_GROUPS.find((g) => g.id === "basic")!];
      case "top":
        return [ROUNDED_GROUPS.find((g) => g.id === "top")!];
      case "right":
        return [ROUNDED_GROUPS.find((g) => g.id === "right")!];
      case "bottom":
        return [ROUNDED_GROUPS.find((g) => g.id === "bottom")!];
      case "left":
        return [ROUNDED_GROUPS.find((g) => g.id === "left")!];
      default:
        return [];
    }
  }, [currentState.mode]);

  // Get current rounded value for display (MEMOIZED)
  const getCurrentRoundedValue = useMemo(() => {
    if (currentState.options.length === 0) return "";
    const option = currentState.options[0];
    const size = option.size || "base";

    // Get the correct prefix for current mode
    const prefixes = ROUNDED_PREFIX_MAP[currentState.mode as RoundedMode];
    if (prefixes.length === 0) return "";

    const prefix = prefixes[0]; // Use first prefix for single value modes

    if (size === "base") {
      return prefix; // Just "rounded", "rounded-t", etc.
    } else if (size === "none") {
      return `${prefix}-none`;
    } else {
      return `${prefix}-${size}`;
    }
  }, [currentState.options, currentState.mode]);

  return (
    <div className="space-y-2">
      {/* Mode selector */}
      <Select
        value={currentState.mode}
        onValueChange={handleRoundedModeChange}
        disabled={disabled}
      >
        <SelectTrigger className="w-full h-7">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="none">Không có</SelectItem>
          <SelectItem value="all">Toàn bộ</SelectItem>
          <SelectItem value="top">Phía trên</SelectItem>
          <SelectItem value="right">Bên phải</SelectItem>
          <SelectItem value="bottom">Phía dưới</SelectItem>
          <SelectItem value="left">Bên trái</SelectItem>
          <SelectItem value="corners">Góc riêng</SelectItem>
        </SelectContent>
      </Select>

      {/* Custom mode display */}
      {currentState.mode === "custom" && (
        <div className="space-y-2">
          <p className="text-xs text-muted-foreground">
            Các class bo viền phức tạp không thể chỉnh sửa bằng giao diện.
            Chuyển sang tab "Nâng cao" để chỉnh sửa.
          </p>
          <Textarea
            value={roundedClasses}
            placeholder={
              roundedClasses ? "rounded-lg rounded-tl-xl" : "Không có nội dung"
            }
            className="font-mono text-xs bg-gray-50"
            rows={2}
            readOnly
            disabled
          />
        </div>
      )}

      {/* Value selector for single value modes */}
      {currentState.mode !== "none" &&
        currentState.mode !== "corners" &&
        currentState.mode !== "custom" && (
          <AppearancePopover
            value={getCurrentRoundedValue}
            onValueChange={handleRoundedValueChange}
            groups={getRoundedGroups}
            placeholder="sm"
            disabled={disabled}
            className="w-full"
          />
        )}

      {/* Corners control for corners mode */}
      {currentState.mode === "corners" && (
        <CornersControl
          value={{
            size: "sm", // Default size
            tl: currentState.options[0]?.size || "sm",
            tr: currentState.options[1]?.size || "sm",
            bl: currentState.options[2]?.size || "sm",
            br: currentState.options[3]?.size || "sm",
          }}
          onValueChange={handleCornersChange}
          disabled={disabled}
        />
      )}
    </div>
  );
};

export const RoundedSection = React.memo(RoundedSectionComponent);
