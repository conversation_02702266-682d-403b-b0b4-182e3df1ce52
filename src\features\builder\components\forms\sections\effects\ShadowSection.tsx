import React, { useMemo, useCallback } from "react";
import { TreeNode } from "../../../../context/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ShadowState,
  ShadowType,
  ShadowSize,
  SHADOW_SIZE_OPTIONS,
  SHADOW_TYPE_OPTIONS,
  SHADOW_OPACITY_OPTIONS,
  DEFAULT_SHADOW_COLOR,
  DEFAULT_SHADOW_OPACITY,
} from "./shadow/types";
import { parseShadowState, updateShadowClasses } from "./shadow/shadowHelper";
import { ColorPicker } from "../../inputs/ColorPicker";
import { cn } from "@/lib/utils";

interface ShadowSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

export const ShadowSection: React.FC<ShadowSectionProps> = ({
  node,
  onChange,
  disabled = false,
}) => {
  const currentClassName = node.style?.className || "";

  const currentShadowState = useMemo(() => {
    return parseShadowState(currentClassName);
  }, [currentClassName]);

  const updateClassName = useCallback(
    (newClassName: string) => {
      onChange({
        style: {
          ...node.style,
          className: newClassName,
        },
      });
    },
    [node.style, onChange]
  );

  const updateShadowState = useCallback(
    (newShadowState: Partial<ShadowState>) => {
      const updatedState = { ...currentShadowState, ...newShadowState };
      const newClassName = updateShadowClasses(currentClassName, updatedState);
      updateClassName(newClassName);
    },
    [currentShadowState, currentClassName, updateClassName]
  );

  const handleTypeChange = useCallback(
    (type: ShadowType) => {
      updateShadowState({ type });
    },
    [updateShadowState]
  );

  const handleSizeChange = useCallback(
    (size: ShadowSize) => {
      updateShadowState({ size });
    },
    [updateShadowState]
  );

  const handleColorChange = useCallback(
    (color: string) => {
      updateShadowState({ color });
    },
    [updateShadowState]
  );

  const handleOpacityChange = useCallback(
    (opacity: string) => {
      updateShadowState({ opacity });
    },
    [updateShadowState]
  );

  const showControls = currentShadowState.type !== "none";

  return (
    <div className="space-y-3">
      <div className="flex gap-2">
        <Select
          value={currentShadowState.type}
          onValueChange={handleTypeChange}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Loại bóng" />
          </SelectTrigger>
          <SelectContent>
            {SHADOW_TYPE_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {showControls && (
          <Select
            value={currentShadowState.size}
            onValueChange={handleSizeChange}
            disabled={disabled}
          >
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Kích thước" />
            </SelectTrigger>
            <SelectContent>
              {SHADOW_SIZE_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {showControls && (
        <div className="flex gap-2">
          <ColorPicker
            value={currentShadowState.color || DEFAULT_SHADOW_COLOR}
            onChange={handleColorChange}
            disabled={disabled}
            className="h-9 w-12 p-0 flex items-center justify-center"
            renderValue={(color) => (
              <div
                className={cn(
                  "w-5 h-5 rounded-full border border-gray-300",
                  color ? `bg-${color}` : "bg-gray-200"
                )}
                style={
                  color === "black"
                    ? { backgroundColor: "black" }
                    : color === "white"
                    ? { backgroundColor: "white" }
                    : undefined
                }
              />
            )}
          />
          <Select
            value={currentShadowState.opacity || DEFAULT_SHADOW_OPACITY}
            onValueChange={handleOpacityChange}
            disabled={disabled}
          >
            <SelectTrigger className="flex-1">
              <SelectValue
                placeholder={`${
                  currentShadowState.opacity || DEFAULT_SHADOW_OPACITY
                }%`}
              />
            </SelectTrigger>
            <SelectContent>
              {SHADOW_OPACITY_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.value}%
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
    </div>
  );
};
