import React from 'react';
import { DropPosition } from '../../types/drag-drop';
import { SmartDropPosition, getDropIndicatorStyles } from '../../utils/drop-position';

interface DropIndicatorProps {
  targetId: string;
  position: DropPosition;
  visualPosition?: SmartDropPosition['visualPosition'];
}

/**
 * DropIndicator Component - Part of Dual Indicators System
 * 
 * Renders drop position indicators that help users understand where
 * their dragged element will be placed. Shows 2 indicators at once
 * with different opacities based on mouse proximity.
 */
export const DropIndicator: React.FC<DropIndicatorProps> = ({ targetId, position, visualPosition }) => {
  
  const targetElement = document.querySelector(`[data-node-id="${targetId}"]`);
  if (!targetElement) {
    return null;
  }
  
  const rect = targetElement.getBoundingClientRect();
  
  // If no visual position provided, show nothing
  if (!visualPosition) {
    return null;
  }
  
  // Get styles for indicators
  const indicatorStyles = getDropIndicatorStyles(visualPosition, rect, position);
  
  // Base style for all indicators
  const baseStyle: React.CSSProperties = {
    zIndex: 9998,
    pointerEvents: 'none',
  };
  
  // Render single active indicator
  return (
    <>
      {indicatorStyles.map((style, index) => (
        <div
          key={index}
          style={{
            ...baseStyle,
            ...style,
          }}
        >
          {visualPosition === 'center' ? (
            <div className="w-full h-full border-2 border-dashed border-purple-500 rounded-lg bg-purple-50 bg-opacity-30 drop-indicator-pulse gpu-accelerated" />
          ) : (
            <div 
              className={`${
                visualPosition === 'horizontal' ? 'w-full h-full' : 'h-full w-full'
              } bg-blue-500 rounded-full shadow-lg drop-indicator-pulse gpu-accelerated`}
            />
          )}
        </div>
      ))}
    </>
  );
};