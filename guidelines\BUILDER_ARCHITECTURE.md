# Builder Architecture Guidelines

## 🏗️ **Architecture Overview**

### Core Principles
- **Factory Pattern**: Node, Form, và component factories cho extensibility
- **Single Source of Truth**: TreeNode.style.className và TreeNode.style.css
- **History Support**: <PERSON><PERSON><PERSON> thay đổi qua onChange để có undo/redo
- **Component Composition**: Tách biệt logic và UI, reusable components
- **Overlay System**: Figma-like selection, hover, và drag-drop indicators
- **Inline Editing**: Direct content manipulation cho better UX

---

## 📁 **Directory Structure & Organization**

```
src/features/builder/
├── components/
│   ├── Layout/                 # Main layout components
│   │   ├── PropertiesPanel.tsx # Main properties panel
│   │   └── ...
│   ├── PropertiesPanel/        # Specialized components
│   │   ├── CSSPropertiesEditor.tsx
│   │   └── input/
│   │       └── SizePopover.tsx
│   └── nodes/                  # Node rendering components
│       ├── basic/              # Basic nodes (Frame, Text, Image, Video)
│       ├── templates/          # Template nodes (ImageWithCaption)
│       └── widgets/            # Widget nodes (DateTimeCard)
├── forms/
│   ├── registry/               # Factory patterns
│   │   ├── DisplayFormFactory.tsx
│   │   └── FormFactory.tsx
│   └── sections/               # Reusable form sections
│       ├── ShadowSection.tsx   # Box & drop shadows
│       ├── FilterSection.tsx   # CSS filters
│       ├── AppearanceSection.tsx # Opacity, bg, rounded
│       └── ...
├── overlay/                    # Overlay system
│   ├── components/             # Overlay UI components
│   ├── context/                # Overlay state management
│   └── utils/                  # Element finder, observers
├── hooks/                      # Custom hooks
│   └── useInlineTextEditor.ts  # Inline editing functionality
├── context/                    # State management
└── types/                      # Type definitions
```

---

## 🔧 **Component Development Rules**

### 1. **State Management**
- ❌ **NEVER**: Dùng local state cho data có thể undo/redo
- ✅ **ALWAYS**: Đọc từ TreeNode, update qua onChange
- ✅ **Local state only**: UI state (editing mode, expanded/collapsed)

```typescript
// ❌ Wrong - Local state for data
const [properties, setProperties] = useState(existingProperties);

// ✅ Correct - Always read from node
const properties = useMemo(() => parseFromNode(node), [node]);
```

### 2. **Factory Pattern Implementation**
- ✅ Mỗi node type có form riêng trong DisplayFormFactory
- ✅ Shared AdvancedDisplayForm cho tất cả nodes
- ✅ Registry pattern cho extensibility

```typescript
// DisplayFormFactory structure
const displayFormRegistry: DisplayFormRegistry = {
  frame: FrameDisplayForm,
  text: TextDisplayForm,
  // Easy to extend
};
```

### 3. **Form Section Components**
- ✅ Tách thành component riêng (như CSSPropertiesEditor)
- ✅ Props interface: `{ node, onChange, disabled }`
- ✅ Tự quản lý UI state, không quản lý data state

### 4. **File Size & Code Organization**
- ❌ **NEVER**: Để file mã nguồn quá dài (>100-150 dòng)
- ✅ **ALWAYS**: Tách component nhỏ thành file riêng khi cần
- ✅ **Benefit**: Dễ kiểm soát, debug, và maintain code
- ✅ **Rule**: Một component phức tạp = một file riêng

```typescript
// ❌ Wrong - File quá dài, khó debug
// SomeComplexForm.tsx (200+ lines)

// ✅ Correct - Tách thành components nhỏ
// SomeComplexForm.tsx (50 lines) 
// components/SectionA.tsx (40 lines)
// components/SectionB.tsx (30 lines)
```

---

## 🎯 **Properties Panel Architecture**

### Main Structure
```
PropertiesPanel
├── Tab: Thông tin (root node only)
└── Tab: Chi tiết
    ├── Toggle: Cơ bản ↔ Nâng cao
    ├── Sub-tabs: Hiển thị | Nội dung
    └── Forms:
        ├── Basic: DisplayFormFactory → per-node forms
        └── Advanced: Tailwind Classes + CSS Properties
```

### Form Organization
- **FrameDisplayForm**: SizeSection + Layout + Spacing
- **TextDisplayForm**: Typography + Colors + Content editing
- **ImageDisplayForm**: Dimensions + Borders + Filters
- **AdvancedDisplayForm**: Shared cho tất cả - Tailwind + CSS

### New Advanced Sections
- **ShadowSection**: Box shadows & drop shadows với color/opacity
- **FilterSection**: Blur, brightness, contrast, saturate, hue-rotate
- **AppearanceSection**: Opacity, background color, rounded corners
- **BorderSection**: Advanced border controls với individual sides
- **TransformSection**: Scale, rotate, translate controls

---

## 🔄 **Data Flow Patterns**

### 1. **Size Section Integration**
```typescript
// forms/sections/SizeSection.tsx - Main implementation
// Uses SizePopover for UI, handles TreeNode directly

// DisplayFormFactory integration
const FrameDisplayForm = ({ node, onChange, disabled }) => (
  <SizeSection node={node} onChange={onChange} disabled={disabled} />
);
```

### 2. **CSS Properties Pattern**
```typescript
// Separate component for complex UI
<CSSPropertiesEditor 
  node={node} 
  onChange={onChange} 
  disabled={disabled} 
/>

// Always read from node.style.css
// Local state only for editing UI (isEditing, newProperty)
// Save immediately to node via onChange
```

### 3. **History Integration**
- Mọi onChange → updateNode → history system
- Component không cần biết về history
- Undo/redo tự động sync UI

---

## 🚀 **Extension Guidelines**

### Adding New Form Section
1. Tạo component riêng trong `forms/sections/`
2. Props: `{ node, onChange, disabled }`
3. Parse data từ `node.style.className` hoặc `node.style.css`
4. Update qua `onChange({ style: { ...node.style, newData } })`
5. Add vào appropriate DisplayForm
6. Tạo helper functions cho complex CSS parsing/generation

### Adding New Node Type
1. Add type vào `TreeNodeType`
2. Tạo DisplayForm cho node type
3. Register trong `displayFormRegistry`
4. Tạo NodeComponent trong `components/nodes/[category]/`
5. Update `nodeRegistry`, `iconMap`, và `defaultNodeMap`
6. Add to node creation dialog categories

### Adding New Input Component
1. Tạo trong `components/PropertiesPanel/input/`
2. Reusable interface
3. No data state, chỉ UI state
4. Use trong form sections

### Adding New CSS Property Management
1. Create helper trong `forms/sections/[property]/helper.ts`
2. Implement parse/generate/remove/update functions
3. Use memoization cho performance
4. Handle edge cases và custom values

---

## ⚠️ **Common Pitfalls & Solutions**

### 1. **State Sync Issues**
```typescript
// ❌ Problem: Local state không sync với undo/redo
useEffect(() => {
  setLocalData(nodeData);
}, [nodeData]); // Missing dependency

// ✅ Solution: Always compute from node
const data = useMemo(() => parseFromNode(node), [node.style]);
```

### 2. **Factory Pattern Violations**
```typescript
// ❌ Wrong: Switch statements scattered
switch (node.type) {
  case 'frame': return <FrameForm />;
}

// ✅ Correct: Centralized factory
const DisplayForm = getDisplayForm(node.type);
return <DisplayForm {...props} />;
```

### 3. **Component Coupling**
```typescript
// ❌ Wrong: Direct imports between components
import { SomeSpecificComponent } from '../other/path';

// ✅ Correct: Factory or prop injection
const Component = getComponent(type);
```

---

## 📋 **Development Checklist**

### Before Adding New Feature:
- [ ] Identify reusable patterns
- [ ] Check factory registries for extension points
- [ ] Plan data flow (avoid local state for data)
- [ ] Consider undo/redo implications
- [ ] Plan component composition

### Code Review Checklist:
- [ ] No local state for data that should persist
- [ ] onChange used for all data updates
- [ ] Component tách biệt UI và business logic
- [ ] Factory pattern được follow
- [ ] TypeScript strict compliance
- [ ] No circular dependencies

---

## 🎨 **Overlay System Architecture**

### Core Components
- **OverlayLayer**: Portal-based container for all overlays
- **SelectionOverlay**: Blue/orange borders with resize handles
- **HoverOverlay**: Light blue highlight on mouse hover
- **DropTargetOverlay**: Green drop zones during drag operations

### Key Features
1. **Figma-like Selection**:
   - Corner handles (8px) at all corners
   - Edge handles for elements > 40px
   - Color-coded: Blue (edit), Orange (move)
   
2. **Performance Optimized**:
   - Single ResizeObserver/MutationObserver via ObserverManager
   - Debounced updates (16ms) for 60fps
   - GPU-accelerated positioning

3. **Smart Element Detection**:
   - ElementFinder utility for intelligent node selection
   - Prioritizes actual content over wrappers
   - Special handling for images

### Integration Pattern
```typescript
// Sync builder state with overlay
<OverlaySync />

// Register nodes for overlay tracking
const { register } = useNodeRegistration(node.id);
<div ref={register}>...</div>
```

---

## ✏️ **Inline Editing System**

### useInlineTextEditor Hook
Provides seamless inline text editing:

```typescript
const {
  isEditing,
  elementRef,
  startEditing,    // Enter edit mode
  stopEditing,     // Save & exit
  cancelEditing,   // Restore original
  handleKeyDown,   // Keyboard shortcuts
  handleBlur       // Save on focus loss
} = useInlineTextEditor({
  content: node.properties?.content,
  onSave: (newContent) => updateNode(...)
});
```

### Features
- **Double-click to edit** text nodes
- **Keyboard shortcuts**: Enter (save), Escape (cancel)
- **Visual feedback**: Blue ring during editing
- **Auto-select** text on edit start
- **Graceful cancellation** with original content restore

---

## 🎯 **Future Architecture Plans**

### Planned Extensions:
1. **Layout Section**: Flexbox/Grid controls
2. **Spacing Section**: Margin/Padding visual editor
3. **Typography Section**: Font controls
4. **Animation Section**: Transition/transform controls
5. **Theme System**: Design tokens integration
6. **Advanced Node Templates**: More pre-built components
7. **Responsive Design Tools**: Breakpoint management

### Scalability Considerations:
- Plugin architecture cho custom nodes
- Theme/design system integration
- Performance optimization cho large trees
- Export/import functionality
- Collaboration features
- Multi-language support expansion

---

## 💡 **Key Reminders**

1. **"Single Source of Truth"** - TreeNode is the authority
2. **"Factory Over Switch"** - Use registries for extensibility  
3. **"Composition Over Inheritance"** - Small, focused components
4. **"History Aware"** - onChange for everything that should persist
5. **"UI State vs Data State"** - Separate clearly

**Remember**: Mỗi component mới phải fit vào existing architecture, không tạo patterns mới trừ khi absolutely necessary.

---

## 🛠️ **Project Setup & Configuration**

### Package Manager
- **NPM Only**: Project đã chuyển từ yarn sang npm
- **IMPORTANT**: KHÔNG xóa node_modules khi gặp bug, hãy báo cáo vấn đề
- **Scripts**: 
  - `npm install` - Cài đặt dependencies
  - `npm run dev` - Chạy development server
  - `npm run build` - Build production
  - `npm run test` - Chạy tests

### File Management
- **Guidelines Only**: Mọi documentation phải trong `/guidelines`
- **No Temporary MD Files**: Không tạo file .md tạm thời cho testing
- **Clean Workspace**: Xóa file tạm ngay sau khi hoàn thành task

---

## 🎯 **UI State Persistence System**

### State Categories
```typescript
// 1. PersistentUIState - Survives across sessions
interface PersistentUIState {
  selectedNodeId: string;
  expandedNodeIds: Set<string>;
  viewMode: ViewMode;
  editorMode: EditorMode;
  // ...
}

// 2. SessionUIState - Within session only  
interface SessionUIState {
  lastCreatedNodeId: string | null;
  // ...
}

// 3. TemporaryUIState - Tab states
interface TemporaryUIState {
  propertiesPanelMainTab?: 'post' | 'block';
  propertiesPanelSubTab?: 'display' | 'content';
  propertiesPanelAdvanced?: boolean;
  propertiesPanelDisplaySection?: string; // Nested sections: 'size', 'layout', etc.
}
```

### Smart Tab Behavior
```typescript
// Auto-switch logic for Properties Panel
const shouldAutoSwitch = isNewlyCreatedNode(nodeId) || !hasManuallySetTab;

// Rules:
// - NEW nodes → Auto-switch to relevant tab
// - EXISTING nodes → Preserve user's tab choice
// - ROOT node → Force "Thông tin" tab, disable "Chi tiết"
// - F5 refresh → Restore all tab states
// - Nested sections → Also persist across refresh
```

### Key Implementation Points
1. **SET_POST_WITH_PRESERVED_UI**: Action để load posts mà không reset UI state
2. **lastCreatedNodeId**: Track newly created nodes cho smart behavior
3. **Auto-save**: UI states tự động save vào localStorage
4. **useEffect Dependencies**: Cẩn thận với dependencies để tránh infinite loops
5. **Dirty Flag Integration**: UI state changes set `isDirty: true` để trigger "chưa lưu"
6. **History Tracking**: UI changes có descriptions cho undo/redo: "Change UI settings"

### Common Patterns
```typescript
// Avoid infinite loops in useEffect
React.useEffect(() => {
  if (shouldUpdate && newValue !== currentValue) {
    setState(newValue);
  }
}, [dependency1, dependency2]); // NOT setState or functions

// Preserve selection when switching editor modes
case ACTION_TYPES.SET_EDITOR_MODE: {
  return {
    ...state,
    uiState: {
      ...state.uiState,
      editorMode: action.payload
      // Keep current selection for better UX
    }
  };
}

// Mode-aware selection handling
const selectNode = useCallback((nodeId: string | null) => {
  // In move mode, allow null selection for canceling move
  // In other modes, default to root to maintain UX consistency
  const targetNodeId = (editorMode === 'move' && nodeId === null) 
    ? null 
    : (nodeId || 'root');
  dispatch(actions.selectNode(targetNodeId));
}, [dispatch, editorMode]);

// Single drop indicator for clean UX
const DropIndicator = ({ style }) => (
  <div className="bg-blue-500 rounded-full shadow-lg animate-pulse" />
);

// Shows only active drop position, no visual clutter
```

---

## ⚠️ **Critical Rules & Warnings**

### 1. **NPM Package Management**
- ✅ Use `npm install`, `npm run dev`, etc.
- ❌ NEVER delete node_modules để "fix" bugs
- ❌ NEVER switch back to yarn
- ⚠️ **Known Issue**: WSL/Windows compatibility issue
  - Problem: Build/compilation commands có thể fail trên WSL environment
  - Solution: KHÔNG chạy build commands (`npm run build`, compile scripts)
  - Development: Chỉ use `npm run dev` cho development server
  - Testing: Focus on code logic testing, không cần build verification

### 2. **File Organization**  
- ✅ Documentation trong `/guidelines` only
- ❌ NO temporary .md files ngoài guidelines
- ❌ NO scattered documentation files

### 3. **UI State Management**
- ✅ Use context methods: `setPropertiesPanelMainTab()`, etc.
- ✅ Check for circular dependencies in useEffect
- ❌ NO local state for persistent UI preferences

### 4. **Testing & Debugging**
- ✅ Test với F5 refresh để verify state persistence
- ✅ Test new node creation vs existing node selection
- ✅ Monitor console cho React warnings về re-renders
- ✅ **Enhanced**: Multi-layer temporary state persistence implemented
- ✅ **Fixed**: Nested section persistence (Kích thước/Bố cục sections)
- ✅ **UI Improvements**: Consistent blue color for active tabs
- ✅ **UX Enhancement**: Root node only shows "Thông tin" tab, "Chi tiết" disabled
- ✅ **History Integration**: UI state changes now trigger dirty flag and undo/redo
- ✅ **Move Mode Fix**: Mode-aware selection handling for proper move workflow
- ✅ **UX Improvement**: Single drop indicator (clean, focused positioning)
- ✅ **UX Improvement**: Various UI enhancements completed