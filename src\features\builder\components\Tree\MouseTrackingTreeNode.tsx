import React, { useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronRight, ChevronDown } from 'lucide-react';
import { TreeNode as TreeNodeType } from '../../context/types';
import { getNodeTypeIcon } from '../../utils/translations';

interface TreeNodeProps {
  node: TreeNodeType;
  level: number;
  isSelected: boolean;
  isExpanded: boolean;
  currentHoverInfo: {
    nodeId: string;
    position: 'top' | 'middle' | 'bottom';
  } | null;
  parentHighlight: boolean;
  onSelect: () => void;
  onToggleExpand: () => void;
  onMousePositionChange: (nodeId: string, position: 'top' | 'middle' | 'bottom' | null) => void;
  onDragStart: (nodeId: string) => void;
  onDragEnd: (targetNodeId: string, position: 'top' | 'middle' | 'bottom') => void;
  dragState?: {
    isDragging: boolean;
    draggedNodeId: string | null;
  };
  children?: React.ReactNode;
}

const MouseTrackingTreeNodeComponent: React.FC<TreeNodeProps> = ({
  node,
  level,
  isSelected,
  isExpanded,
  currentHoverInfo,
  parentHighlight,
  onSelect,
  onToggleExpand,
  onMousePositionChange,
  onDragStart,
  onDragEnd,
  dragState,
  children
}) => {
  const labelRef = useRef<HTMLDivElement>(null);
  const hasChildren = node.children && node.children.length > 0;
  const [mouseDownPos, setMouseDownPos] = useState<{ x: number; y: number } | null>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    // Don't allow dragging root node
    if (node.id === 'root') {
      // For root, just handle selection
      onSelect();
      return;
    }
    
    // Only handle left click
    if (e.button !== 0) return;
    
    // Don't start drag if clicking on buttons
    const target = e.target as HTMLElement;
    if (target.closest('button')) {
      return; // Let button handle its own event
    }
    
    // Store mouse position for drag threshold detection
    setMouseDownPos({ x: e.clientX, y: e.clientY });
    
    // DON'T select immediately - wait to see if it's a drag or click
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    // Handle drag drop
    if (currentHoverInfo && currentHoverInfo.nodeId === node.id && dragState?.isDragging) {
      e.stopPropagation(); // Prevent parent handlers
      onDragEnd(currentHoverInfo.nodeId, currentHoverInfo.position);
      setMouseDownPos(null);
      return;
    }
    
    // Handle selection (click without drag)
    if (mouseDownPos && !dragState?.isDragging) {
      onSelect();
      setMouseDownPos(null);
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!labelRef.current) return;
    
    // Check if we should start dragging (mouse moved enough from initial position)
    if (mouseDownPos && !dragState?.isDragging) {
      const deltaX = Math.abs(e.clientX - mouseDownPos.x);
      const deltaY = Math.abs(e.clientY - mouseDownPos.y);
      const DRAG_THRESHOLD = 5; // pixels
      
      if (deltaX > DRAG_THRESHOLD || deltaY > DRAG_THRESHOLD) {
        // Start dragging
        setMouseDownPos(null);
        onDragStart(node.id);
        return;
      }
    }
    
    // Only track mouse position when actively dragging
    if (!dragState?.isDragging) return;
    
    // Don't show highlights on the dragging node itself
    if (dragState.draggedNodeId === node.id) return;
    
    const rect = labelRef.current.getBoundingClientRect();
    const relativeY = e.clientY - rect.top;
    const percentage = relativeY / rect.height;
    
    let position: 'top' | 'middle' | 'bottom';
    
    // Special case for root - always middle
    if (node.id === 'root') {
      position = 'middle';
    } else {
      // Check if this node can accept children (only frames can)
      const canAcceptChildren = node.type === 'frame';
      
      if (percentage <= 0.25) {
        position = 'top';
      } else if (percentage >= 0.75) {
        position = 'bottom';
      } else {
        // Only allow middle position if node can accept children
        position = canAcceptChildren ? 'middle' : (percentage < 0.5 ? 'top' : 'bottom');
      }
    }
    
    onMousePositionChange(node.id, position);
  };

  const handleMouseLeave = () => {
    // Reset mouse down position if leaving without drag
    if (mouseDownPos) {
      setMouseDownPos(null);
    }
    onMousePositionChange(node.id, null);
  };

  return (
    <div 
      className="relative"
      data-node-id={node.id}
    >
      {/* Label area */}
      <div 
        ref={labelRef}
        className="relative"
        data-label-id={node.id}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      >
        {/* Border indicators - only show when dragging */}
        {currentHoverInfo?.nodeId === node.id && 
         currentHoverInfo.position === 'top' && 
         dragState?.isDragging && 
         dragState.draggedNodeId !== node.id && (
          <div className="absolute top-0 left-0 right-0 h-0.5 bg-blue-500 z-20 pointer-events-none" />
        )}
        {currentHoverInfo?.nodeId === node.id && 
         currentHoverInfo.position === 'bottom' && 
         dragState?.isDragging && 
         dragState.draggedNodeId !== node.id && (
          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 z-20 pointer-events-none" />
        )}
        
        <div
          className={cn(
            // Base styling with GPU acceleration
            "flex items-center gap-1 px-2 py-1 cursor-pointer group select-none border-2 border-transparent tree-node gpu-accelerated",
            // Hover effect - only when not selected or dragging
            !isSelected && !dragState?.draggedNodeId && "hover:bg-gray-50",
            // Selection highlight (blue) - replace border, don't add
            isSelected && !dragState?.isDragging && "bg-blue-100 border-blue-500 tree-node-selected",
            // Dragging highlight (orange/dim) - replace border, don't add
            dragState?.draggedNodeId === node.id && "bg-orange-100 border-orange-400 tree-node-dragging",
            // Purple border for middle position on current node (only if it can accept children)
            currentHoverInfo?.nodeId === node.id && 
            currentHoverInfo.position === 'middle' && 
            dragState?.draggedNodeId !== node.id && 
            node.type === 'frame' && 
            "border-purple-500",
            // Purple border for parent highlight
            parentHighlight && dragState?.draggedNodeId !== node.id && "border-purple-500"
          )}
          style={{ 
            paddingLeft: `${level * 20 + 8}px`,
            cursor: node.id === 'root' ? 'default' : 'grab'
          }}
        >
          {/* Expand/Collapse */}
          {hasChildren && (
            <button
              onMouseDown={(e) => e.stopPropagation()}
              onClick={(e) => {
                e.stopPropagation();
                onToggleExpand();
              }}
              className="p-0.5 hover:bg-gray-200 rounded z-10 relative gpu-button"
            >
              {isExpanded ? (
                <ChevronDown className="w-3 h-3 expand-collapse expand-collapse-expanded" />
              ) : (
                <ChevronRight className="w-3 h-3 expand-collapse" />
              )}
            </button>
          )}
          {!hasChildren && <div className="w-4" />}

          {/* Content Area */}
          <div className="flex items-center gap-2 flex-1">
            {/* Node Type Icon */}
            {(() => {
              const IconComponent = getNodeTypeIcon(node.type);
              return <IconComponent className="w-4 h-4 text-gray-600 flex-shrink-0" />;
            })()}
            
            {/* Node Label */}
            <span className="text-sm text-gray-900 flex-1 truncate">{node.label}</span>
          </div>
        </div>
      </div>

      {/* Children area */}
      {hasChildren && isExpanded && (
        <div className="ml-4">
          {children}
        </div>
      )}
    </div>
  );
};

export const MouseTrackingTreeNode = MouseTrackingTreeNodeComponent;