import { BuilderState, TreeNode } from './types';
import { BuilderAction, ACTION_TYPES } from './actions';
import { 
  findNodeById, 
  findParentNode, 
  deleteNodeFromTree,
  createNodeCopy,
  addNodeToTree
} from '../utils/tree';
import { createDefaultNode, generateNodeId } from '../utils/node-factory';
import { generateNextNodeName } from '../utils/node-naming';
import { nodeValidation } from './validation';
import { createImmerState } from '../../../lib/immer-utils';

export const builderReducer = (state: BuilderState, action: BuilderAction): BuilderState => {
  switch (action.type) {
    case ACTION_TYPES.SET_POST: {
      // Legacy behavior: reset to root (for new posts)
      return {
        ...state,
        post: action.payload,
        uiState: {
          ...state.uiState,
          isDirty: false,
          selectedNodeId: 'root'
        }
      };
    }

    case ACTION_TYPES.SET_POST_WITH_PRESERVED_UI: {
      // New behavior: preserve existing UI state (for loaded posts)
      return {
        ...state,
        post: action.payload,
        uiState: {
          ...state.uiState,
          isDirty: false
          // selectedNodeId preserved from current state
        }
      };
    }

    case ACTION_TYPES.UPDATE_POST: {
      if (!state.post) return state;
      
      return {
        ...state,
        post: {
          ...state.post,
          ...action.payload,
          updatedAt: Date.now()
        },
        uiState: {
          ...state.uiState,
          isDirty: true
        }
      };
    }

    case ACTION_TYPES.SELECT_NODE: {
      const nodeId = action.payload;
      
      // If selecting a node, auto-expand parent nodes
      if (nodeId && state.post) {
        const currentExpanded = state.uiState.expandedNodeIds instanceof Set 
          ? state.uiState.expandedNodeIds 
          : new Set(Array.isArray(state.uiState.expandedNodeIds) ? state.uiState.expandedNodeIds : []);
        
        const newExpanded = new Set(currentExpanded);
        
        // Find all parent nodes and expand them
        const expandParents = (root: TreeNode, targetId: string, parents: string[] = []): boolean => {
          if (root.id === targetId) {
            // Add all parents to expanded set
            parents.forEach(parentId => newExpanded.add(parentId));
            return true;
          }
          
          for (const child of root.children) {
            if (expandParents(child, targetId, [...parents, root.id])) {
              return true;
            }
          }
          
          return false;
        };
        
        expandParents(state.post.content, nodeId);
        
        return {
          ...state,
          uiState: {
            ...state.uiState,
            selectedNodeId: nodeId,
            expandedNodeIds: newExpanded,
            lastCreatedNodeId: null // Clear creation tracking when selecting different node
          }
        };
      }
      
      return {
        ...state,
        uiState: {
          ...state.uiState,
          selectedNodeId: action.payload,
          lastCreatedNodeId: null // Clear creation tracking when selecting any node
        }
      };
    }

    case ACTION_TYPES.TOGGLE_NODE_EXPANSION: {
      const nodeId = action.payload;
      // Safely handle expandedNodeIds that might be serialized as object
      const currentExpanded = state.uiState.expandedNodeIds instanceof Set 
        ? state.uiState.expandedNodeIds 
        : new Set(Array.isArray(state.uiState.expandedNodeIds) ? state.uiState.expandedNodeIds : []);
      
      const expandedNodeIds = new Set(currentExpanded);
      
      if (expandedNodeIds.has(nodeId)) {
        expandedNodeIds.delete(nodeId);
      } else {
        expandedNodeIds.add(nodeId);
      }
      
      return {
        ...state,
        uiState: {
          ...state.uiState,
          expandedNodeIds
        }
      };
    }

    case ACTION_TYPES.ADD_NODE: {
      if (!state.post) return state;
      
      const { parentId, nodeType, index } = action.payload;
      const parent = findNodeById(state.post.content, parentId);
      
      if (!parent) {
        console.error(`Parent node ${parentId} not found`);
        return state;
      }
      
      if (!nodeValidation.canAddChild(parent, nodeType)) {
        console.error(`Cannot add ${nodeType} as child of ${parent.type}`);
        return state;
      }
      
      // Create new node with defaults
      const newNode: TreeNode = {
        id: generateNodeId(nodeType),
        label: generateNextNodeName(nodeType),
        type: nodeType,
        group: 'basic',
        children: [],
        ...createDefaultNode(nodeType)
      };
      
      // Clone the post to avoid mutations using Immer
      const newPost = createImmerState(state.post, (draft) => {
        const targetParent = findNodeById(draft.content, parentId);
        
        if (targetParent) {
          if (index !== undefined && index >= 0 && index <= targetParent.children.length) {
            targetParent.children.splice(index, 0, newNode);
          } else {
            targetParent.children.push(newNode);
          }
        }
      });
      
      // Auto-expand parent node
      const currentExpanded = state.uiState.expandedNodeIds instanceof Set 
        ? state.uiState.expandedNodeIds 
        : new Set(Array.isArray(state.uiState.expandedNodeIds) ? state.uiState.expandedNodeIds : []);
      
      const expandedNodeIds = new Set(currentExpanded);
      expandedNodeIds.add(parentId);
      
      return {
        ...state,
        post: {
          ...newPost,
          updatedAt: Date.now()
        },
        uiState: {
          ...state.uiState,
          isDirty: true,
          selectedNodeId: newNode.id,
          expandedNodeIds,
          lastCreatedNodeId: newNode.id // Track newly created node
        }
      };
      
      return state;
    }

    case ACTION_TYPES.UPDATE_NODE: {
      if (!state.post) return state;
      
      const { nodeId, updates } = action.payload;
      
      const node = findNodeById(state.post.content, nodeId);
      
      if (!node) {
        console.error(`Node ${nodeId} not found`);
        return state;
      }
      
      // Clone the post to avoid mutations using Immer
      const newPost = createImmerState(state.post, (draft) => {
        const targetNode = findNodeById(draft.content, nodeId);
        
        if (targetNode) {
          // Apply updates
          Object.assign(targetNode, updates);
        }
      });
      
      return {
        ...state,
        post: {
          ...newPost,
          updatedAt: Date.now()
        },
        uiState: {
          ...state.uiState,
          isDirty: true
        }
      };
      
      return state;
    }

    case ACTION_TYPES.DELETE_NODE: {
      if (!state.post) return state;
      
      const nodeId = action.payload;
      const node = findNodeById(state.post.content, nodeId);
      
      if (!node || !nodeValidation.canDelete(node)) {
        console.error(`Cannot delete node ${nodeId}`);
        return state;
      }
      
      // Clone the post to avoid mutations using Immer
      const newPost = createImmerState(state.post, (draft) => {
        deleteNodeFromTree(draft.content, nodeId);
      });
      
      if (findNodeById(state.post.content, nodeId) && !findNodeById(newPost.content, nodeId)) {
        return {
          ...state,
          post: {
            ...newPost,
            updatedAt: Date.now()
          },
          uiState: {
            ...state.uiState,
            isDirty: true,
            selectedNodeId: state.uiState.selectedNodeId === nodeId ? 'root' : state.uiState.selectedNodeId
          }
        };
      }
      
      return state;
    }

    case ACTION_TYPES.MOVE_NODE: {
      if (!state.post) return state;
      
      const { nodeId, newParentId, index } = action.payload;
      
      if (nodeId === 'root') {
        console.error('Cannot move root node');
        return state;
      }
      
      const node = findNodeById(state.post.content, nodeId);
      const newParent = findNodeById(state.post.content, newParentId);
      
      if (!node || !newParent) {
        console.error('Node or parent not found');
        return state;
      }
      
      const canMoveResult = nodeValidation.canMove(node, newParent);
      
      if (!canMoveResult) {
        console.error('Invalid move operation - validation failed');
        return state;
      }
      
      // Use Immer for MOVE_NODE operation
      const newPost = createImmerState(state.post, (draft) => {
        // Find and remove node from current parent
        const currentParent = findParentNode(draft.content, nodeId);
        if (currentParent) {
          const nodeIndex = currentParent.children.findIndex(child => child.id === nodeId);
          if (nodeIndex > -1) {
            const [movedNode] = currentParent.children.splice(nodeIndex, 1);
            
            // Add to new parent
            const targetParent = findNodeById(draft.content, newParentId);
            if (targetParent) {
              if (index >= 0 && index <= targetParent.children.length) {
                targetParent.children.splice(index, 0, movedNode);
              } else {
                targetParent.children.push(movedNode);
              }
            }
          }
        }
      });
      
      // Auto-expand new parent
      const currentExpanded = state.uiState.expandedNodeIds instanceof Set 
        ? state.uiState.expandedNodeIds 
        : new Set(Array.isArray(state.uiState.expandedNodeIds) ? state.uiState.expandedNodeIds : []);
      
      const expandedNodeIds = new Set(currentExpanded);
      expandedNodeIds.add(newParentId);
      
      return {
        ...state,
        post: {
          ...newPost,
          updatedAt: Date.now()
        },
        uiState: {
          ...state.uiState,
          isDirty: true,
          expandedNodeIds
        }
      };
    }

    case ACTION_TYPES.SET_VIEW_MODE: {
      return {
        ...state,
        uiState: {
          ...state.uiState,
          viewMode: action.payload,
          isDirty: true // ✅ Mark as dirty to trigger auto-save
        }
      };
    }

    case ACTION_TYPES.SET_EDITOR_MODE: {
      return {
        ...state,
        uiState: {
          ...state.uiState,
          editorMode: action.payload
          // Keep current selection when switching modes for better UX
        }
      };
    }

    case ACTION_TYPES.SET_DIRTY: {
      return {
        ...state,
        uiState: {
          ...state.uiState,
          isDirty: action.payload
        }
      };
    }

    case ACTION_TYPES.SET_EXPANDED_NODES: {
      return {
        ...state,
        uiState: {
          ...state.uiState,
          expandedNodeIds: action.payload
        }
      };
    }

    case ACTION_TYPES.SET_LAST_SAVED: {
      return {
        ...state,
        uiState: {
          ...state.uiState,
          lastSaved: action.payload
        }
      };
    }

    case ACTION_TYPES.RESTORE_STATE: {
      const restoredState = action.payload;
      
      // If there's a selected node, ensure it's visible by expanding parents
      if (restoredState.uiState.selectedNodeId && restoredState.post) {
        const currentExpanded = restoredState.uiState.expandedNodeIds instanceof Set 
          ? restoredState.uiState.expandedNodeIds 
          : new Set(Array.isArray(restoredState.uiState.expandedNodeIds) 
            ? restoredState.uiState.expandedNodeIds 
            : []);
        
        const newExpanded = new Set(currentExpanded);
        
        // Find all parent nodes and expand them
        const expandParents = (root: TreeNode, targetId: string, parents: string[] = []): boolean => {
          if (root.id === targetId) {
            // Add all parents to expanded set
            parents.forEach(parentId => newExpanded.add(parentId));
            return true;
          }
          
          for (const child of root.children) {
            if (expandParents(child, targetId, [...parents, root.id])) {
              return true;
            }
          }
          
          return false;
        };
        
        expandParents(restoredState.post.content, restoredState.uiState.selectedNodeId);
        
        return {
          ...restoredState,
          uiState: {
            ...restoredState.uiState,
            expandedNodeIds: newExpanded
          }
        };
      }
      
      return restoredState;
    }

    case ACTION_TYPES.TOGGLE_TREE_PANEL: {
      return {
        ...state,
        uiState: {
          ...state.uiState,
          showTreePanel: !state.uiState.showTreePanel
        }
      };
    }

    case ACTION_TYPES.TOGGLE_PROPERTIES_PANEL: {
      return {
        ...state,
        uiState: {
          ...state.uiState,
          showPropertiesPanel: !state.uiState.showPropertiesPanel
        }
      };
    }
    
    case ACTION_TYPES.COPY_NODE: {
      const nodeId = action.payload as string;
      
      if (!state.post) {
        return state;
      }
      
      const nodeToCopy = findNodeById(state.post.content, nodeId);
      if (!nodeToCopy) {
        return state;
      }
      
      // Deep clone the node to avoid reference issues
      const copiedNode = createImmerState(nodeToCopy, () => {
        // Immer handles deep cloning automatically
      });
      
      return {
        ...state,
        uiState: {
          ...state.uiState,
          copiedNode
        }
      };
    }
    
    case ACTION_TYPES.PASTE_NODE: {
      if (!state.post || !state.uiState.copiedNode) {
        return state;
      }
      
      const targetId = state.uiState.selectedNodeId || 'root';
      
      const targetNode = findNodeById(state.post.content, targetId);
      
      if (!targetNode) {
        return state;
      }
      
      // Determine where to paste
      let parentId: string;
      let index: number;
      
      if (targetNode.type === 'frame') {
        // Paste inside frame
        parentId = targetId;
        index = targetNode.children?.length || 0;
      } else {
        // Paste as sibling
        const parent = findParentNode(state.post.content, targetId);
        if (!parent) {
          return state;
        }
        
        parentId = parent.id;
        const targetIndex = parent.children?.findIndex(c => c.id === targetId) ?? -1;
        index = targetIndex + 1;
      }
      
      // Create new node with unique IDs
      const newNode = createNodeCopy(state.uiState.copiedNode, state.post.content);
      
      // Add the node
      const updatedContent = addNodeToTree(
        state.post.content,
        parentId,
        newNode,
        index
      );
      
      if (!updatedContent) {
        return state;
      }
      
      return {
        ...state,
        post: {
          ...state.post,
          content: updatedContent
        },
        uiState: {
          ...state.uiState,
          selectedNodeId: newNode.id,
          isDirty: true
        }
      };
    }

    case ACTION_TYPES.SET_UI_STATE: {
      return {
        ...state,
        uiState: {
          ...state.uiState,
          ...action.payload
        }
      };
    }

    case ACTION_TYPES.SET_PERSISTENT_UI_STATE: {
      return {
        ...state,
        uiState: {
          ...state.uiState,
          isDirty: true, // Mark as dirty when UI state changes
          ...action.payload
        }
      };
    }

    case ACTION_TYPES.SET_TEMPORARY_UI_STATE: {
      return {
        ...state,
        uiState: {
          ...state.uiState,
          isDirty: true, // Mark as dirty when UI state changes
          temporary: {
            ...state.uiState.temporary,
            ...action.payload
          }
        }
      };
    }

    default:
      return state;
  }
};