import { createSelector } from "@reduxjs/toolkit";
import type { RootState } from "@/store/rootReducer";

// Base selectors
export const selectContactState = (state: RootState) => state.contactState;

export const selectContactData = createSelector(
  [selectContactState],
  (contactState) => contactState.data
);

export const selectContactSavedData = createSelector(
  [selectContactState],
  (contactState) => contactState.savedData
);

export const selectContactLoading = createSelector(
  [selectContactState],
  (contactState) => contactState.loading
);

export const selectContactSaving = createSelector(
  [selectContactState],
  (contactState) => contactState.saving
);

export const selectContactError = createSelector(
  [selectContactState],
  (contactState) => contactState.error
);

export const selectContactIsDirty = createSelector(
  [selectContactState],
  (contactState) => contactState.isDirty
);
