import { EditorMode } from '../context/types';

export interface OverlayElement {
  nodeId: string;
  rect: DOMRect;
  type: 'selection' | 'hover' | 'dropTarget';
  visible: boolean;
}

export interface OverlayState {
  selectedNodeId: string | null;
  hoveredNodeId: string | null;
  dropTargetNodeId: string | null;
  editorMode: EditorMode;
}

export interface OverlayContextValue {
  state: OverlayState;
  updateSelection: (nodeId: string | null) => void;
  updateHover: (nodeId: string | null) => void;
  updateDropTarget: (nodeId: string | null) => void;
  updateEditorMode: (mode: EditorMode) => void;
  registerNode: (nodeId: string, element: HTMLElement) => void;
  unregisterNode: (nodeId: string) => void;
  getNodeElement: (nodeId: string) => HTMLElement | undefined;
}