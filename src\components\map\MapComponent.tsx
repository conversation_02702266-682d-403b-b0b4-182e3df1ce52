import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { MapPin } from "lucide-react";
import { renderToStaticMarkup } from "react-dom/server";

const position: [number, number] = [12.25282153754468, 109.19573836878692];
const iconHtml = renderToStaticMarkup(
  <MapPin size={32} strokeWidth={2} color="#d32f2f" fill="#ffcccb" />
);

const customIcon = L.divIcon({
  html: iconHtml,
  className: "",
  iconAnchor: [16, 0],
});

const MapComponent = () => (
  <div className="w-full z-0 border-2 border-primary/20 rounded-xl overflow-hidden">
    <MapContainer center={position} zoom={15} className="w-full h-96">
      <TileLayer
        attribution="© Sở nội vụ tỉnh <PERSON><PERSON>"
        url="https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png"
      />
      <Marker position={position} icon={customIcon}>
        <Popup>05 Pastuer, phường Xương Huân, TP. Nha Trang</Popup>
      </Marker>
    </MapContainer>
  </div>
);

export default MapComponent;
