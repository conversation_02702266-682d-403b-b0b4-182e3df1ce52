import { useCallback, useEffect, useState, useRef } from 'react';
import { useBuilderCombined } from '../context/BuilderContext';
import { findParentNode } from '../utils/tree';
import { nodeValidation } from '../context/validation';
import { DropPosition } from '../types/drag-drop';
import { calculateSmartDropPosition, SmartDropPosition } from '../utils/drop-position';
import { calculateMoveIndex } from '../utils/move-position';
import { DRAG_DROP_CONFIG } from '../config/drag-drop';

interface MoveIndicator {
  targetId: string;
  position: DropPosition;
  visualPosition?: SmartDropPosition['visualPosition'];
}

export const useCanvasMove = () => {
  const {
    post,
    uiState,
    selectNode,
    moveNode,
    findNodeById: findNode
  } = useBuilderCombined();
  
  const [moveIndicator, setMoveIndicator] = useState<MoveIndicator | null>(null);
  const isMoving = uiState.editorMode === 'move' && !!uiState.selectedNodeId;
  
  // Refs for performance
  const lastThrottleTime = useRef(0);

  // Handle click in move mode
  const handleNodeClick = useCallback((nodeId: string) => {
    if (uiState.editorMode !== 'move') return;
    
    // If we have a selected node and click again
    if (uiState.selectedNodeId) {
      // If clicking on empty area or no valid target, just cancel the move
      if (!nodeId || !moveIndicator) {
        selectNode(null);
        setMoveIndicator(null);
        return;
      }
      
      // Execute the move based on position
      const { targetId, position } = moveIndicator;
      
      if (position === 'inside') {
        // Move inside the target node
        moveNode(uiState.selectedNodeId, targetId, 0);
      } else {
        // Move as sibling
        const targetParent = post?.content ? findParentNode(post.content, targetId) : null;
        if (targetParent) {
          // Use utility to calculate correct index
          const newIndex = calculateMoveIndex(
            uiState.selectedNodeId,
            targetId,
            position,
            targetParent
          );
          moveNode(uiState.selectedNodeId, targetParent.id, newIndex);
        }
      }
      
      // Clear selection and indicator after successful move
      selectNode(null);
      setMoveIndicator(null);
      return;
    }
    
    // Select node for moving (not root)
    if (nodeId && nodeId !== 'root') {
      selectNode(nodeId);
    }
  }, [uiState.editorMode, uiState.selectedNodeId, selectNode, moveNode, moveIndicator, post?.content]);

  // Handle mouse move to show indicator (throttled)
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isMoving || !uiState.selectedNodeId || !post) return;
    
    // Throttle updates
    const now = Date.now();
    const timeSinceLastUpdate = now - lastThrottleTime.current;
    if (timeSinceLastUpdate < DRAG_DROP_CONFIG.THROTTLE_DELAY) return;
    lastThrottleTime.current = now;
    
    // Find element under cursor (excluding the moving node itself)
    const movingElement = document.querySelector(`[data-node-id="${uiState.selectedNodeId}"]`);
    if (movingElement) {
      (movingElement as HTMLElement).style.pointerEvents = 'none';
    }
    
    const elementUnderCursor = document.elementFromPoint(e.clientX, e.clientY);
    
    if (movingElement) {
      (movingElement as HTMLElement).style.pointerEvents = '';
    }
    
    // Find the node element
    const nodeElement = elementUnderCursor?.closest('[data-node-id]') as HTMLElement;
    
    if (nodeElement) {
      const targetNodeId = nodeElement.dataset.nodeId;
      if (!targetNodeId || targetNodeId === uiState.selectedNodeId) {
        setMoveIndicator(null);
        return;
      }
      
      const movingNode = findNode(uiState.selectedNodeId);
      const targetNode = findNode(targetNodeId);
      
      if (!movingNode || !targetNode) {
        setMoveIndicator(null);
        return;
      }
      
      // Get parent for smart positioning
      const targetParent = findParentNode(post.content, targetNodeId);
      
      // Use smart drop position calculation
      const smartPosition = calculateSmartDropPosition(
        targetNode,
        targetParent,
        e.clientX,
        e.clientY,
        nodeElement
      );
      
      if (!smartPosition) {
        setMoveIndicator(null);
        return;
      }
      
      // Validate the move
      if (smartPosition.position === 'inside') {
        if (!nodeValidation.canMove(movingNode, targetNode)) {
          setMoveIndicator(null);
          return;
        }
      } else {
        if (!targetParent || !nodeValidation.canMove(movingNode, targetParent)) {
          setMoveIndicator(null);
          return;
        }
      }
      
      // Set indicator with visual position
      setMoveIndicator({
        targetId: targetNodeId,
        position: smartPosition.position,
        visualPosition: smartPosition.visualPosition
      });
    } else {
      // No valid target
      setMoveIndicator(null);
    }
  }, [isMoving, uiState.selectedNodeId, post, findNode]);

  // Handle ESC key to cancel move
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape' && isMoving) {
      selectNode(null);
      setMoveIndicator(null);
    }
  }, [isMoving, selectNode]);

  // Set up event listeners
  useEffect(() => {
    if (isMoving) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('keydown', handleKeyDown);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isMoving, handleMouseMove, handleKeyDown]);

  // Clean up when switching modes or when no node is selected
  useEffect(() => {
    if (uiState.editorMode !== 'move') {
      setMoveIndicator(null);
    }
  }, [uiState.editorMode]);
  
  // Clean up indicator when no node is selected in move mode
  useEffect(() => {
    if (uiState.editorMode === 'move' && !uiState.selectedNodeId) {
      setMoveIndicator(null);
    }
  }, [uiState.editorMode, uiState.selectedNodeId]);

  return {
    isMoving,
    movingNodeId: uiState.selectedNodeId,
    moveIndicator,
    handleNodeClick
  };
};