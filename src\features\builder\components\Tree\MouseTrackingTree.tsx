import React, { useCallback } from 'react';
import { TreeNode as TreeNodeType } from '../../context/types';
import { MouseTrackingTreeNode } from './MouseTrackingTreeNode';
import { useUI } from '../../context/UIContext';

interface TreeProps {
  nodes: TreeNodeType[];
  level?: number;
  onMouseDebugInfo: (info: { nodeId: string; position: string } | null) => void;
  currentHoverInfo: { nodeId: string; position: 'top' | 'middle' | 'bottom' } | null;
  parentId?: string | null;
  onDragStart?: (nodeId: string) => void;
  onDragEnd?: (targetNodeId: string, position: 'top' | 'middle' | 'bottom') => void;
  dragState?: {
    isDragging: boolean;
    draggedNodeId: string | null;
  };
}

export const MouseTrackingTree: React.FC<TreeProps> = ({ 
  nodes, 
  level = 0, 
  onMouseDebugInfo, 
  currentHoverInfo,
  onDragStart,
  onDragEnd,
  dragState
}) => {
  const {
    uiState,
    selectNode,
    toggleNodeExpansion,
  } = useUI();

  const handleMousePositionChange = useCallback((nodeId: string, position: 'top' | 'middle' | 'bottom' | null) => {
    if (position) {
      onMouseDebugInfo({ nodeId, position });
    } else {
      onMouseDebugInfo(null);
    }
  }, [onMouseDebugInfo]);

  const renderNode = useCallback((node: TreeNodeType) => {
    const isSelected = uiState?.selectedNodeId === node.id;
    
    // Defensive check for expandedNodeIds - it might not be a Set after undo/redo
    const expandedNodeIds = uiState?.expandedNodeIds;
    let isExpanded = false;
    
    if (expandedNodeIds instanceof Set) {
      isExpanded = expandedNodeIds.has(node.id);
    } else if (Array.isArray(expandedNodeIds)) {
      isExpanded = (expandedNodeIds as string[]).includes(node.id);
    }
    
    // Check if this node should have parent highlight
    const shouldHighlightAsParent = currentHoverInfo && 
      currentHoverInfo.position !== 'middle' &&
      node.children.some(child => child.id === currentHoverInfo.nodeId) || false;

    return (
      <MouseTrackingTreeNode
        key={node.id}
        node={node}
        level={level}
        isSelected={isSelected}
        isExpanded={isExpanded}
        currentHoverInfo={currentHoverInfo}
        parentHighlight={shouldHighlightAsParent}
        onSelect={() => selectNode(node.id)}
        onToggleExpand={() => toggleNodeExpansion(node.id)}
        onMousePositionChange={handleMousePositionChange}
        onDragStart={onDragStart || (() => {})}
        onDragEnd={onDragEnd || (() => {})}
        dragState={dragState}
      >
        {node.children.length > 0 && isExpanded && (
          <MouseTrackingTree 
            nodes={node.children} 
            level={level + 1} 
            onMouseDebugInfo={onMouseDebugInfo}
            currentHoverInfo={currentHoverInfo}
            parentId={node.id}
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
            dragState={dragState}
          />
        )}
      </MouseTrackingTreeNode>
    );
  }, [
    level,
    uiState,
    selectNode,
    toggleNodeExpansion,
    handleMousePositionChange,
    onMouseDebugInfo,
    currentHoverInfo,
    dragState,
    onDragStart,
    onDragEnd,
  ]);

  return <>{nodes.map(renderNode)}</>;
};