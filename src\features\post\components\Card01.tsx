import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { ImageLoader } from "@/components/image/ImageLoader";
import SectionTitle from "@/features/post/components/SectionTitle";
import { Post } from "@/features/post/states/types";
import { useNavigate } from "react-router-dom";

interface PostProps {
  posts: Post[];
}

export const Card01: React.FC<PostProps> = ({ posts }) => {
  const navigate = useNavigate();

  const [currentIndex, setCurrentIndex] = useState(0);

  if (!posts || posts.length === 0) return null;

  const currentPost = posts[currentIndex];

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : posts.length - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % posts.length);
  };

  return (
    <div className="p-4 space-y-8">
      <SectionTitle title="GIỚI THIỆU" />

      <div className="flex flex-col lg:flex-row rounded-2xl overflow-hidden bg-white max-w-6xl mx-auto gap-2">
        {/* Hình ảnh */}
        <div className="w-full lg:w-1/2 h-64 lg:h-auto overflow-hidden rounded-lg">
          <ImageLoader
            src={currentPost?.excerpt?.image}
            alt={currentPost.title}
            fallbackText="Không có hình ảnh"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Nội dung */}
        <div className="flex flex-col justify-between p-6 text-accent-foreground w-full lg:w-1/2">
          <div>
            <h2 className="text-2xl font-bold mb-4">{currentPost.title}</h2>
            <p className="text-sm line-clamp-6">
              {currentPost?.excerpt?.description}
            </p>
          </div>

          <div className="mt-6 space-y-4">
            <Button
              variant="outline"
              className="rounded-full border-2 border-primary text-primary bg-primary-foreground hover:bg-primary/10"
              onClick={() => navigate(`/posts/${currentPost.slug}`)}
            >
              Xem thêm
            </Button>
            <div className="flex items-center gap-2">
              <button
                onClick={handlePrev}
                className="border border-primary rounded-full p-2 text-primary hover:bg-primary/10"
              >
                <ChevronLeft className="w-3 h-3" />
              </button>
              <button
                onClick={handleNext}
                className="border border-primary rounded-full p-2 text-primary hover:bg-primary/10"
              >
                <ChevronRight className="w-3 h-3" />
              </button>
            </div>

            <div className="flex items-center gap-1">
              {posts.map((_, idx) => (
                <div
                  key={idx}
                  className={`h-1 rounded-full ${idx === currentIndex ? "w-6 bg-blue-600" : "w-4 bg-gray-400"
                    }`}
                />
              ))}
            </div>
          </div>
          {/* Điều hướng và thanh pagination */}
          <div className="flex flex-col gap-4"></div>
        </div>
      </div>
    </div>
  );
};
