import React from 'react';
import { cn } from '@/lib/utils';

// Tab Item Interface
export interface TabItem {
  id: string;
  label: string;
  disabled?: boolean;
  content: React.ReactNode;
}

// Custom Tabs Props
export interface CustomTabsProps {
  items: TabItem[];
  activeId: string;
  onTabChange: (id: string) => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary';
  className?: string;
}

// Individual Tab Button Props
interface TabButtonProps {
  id: string;
  label: string;
  active: boolean;
  disabled?: boolean;
  onClick: (id: string) => void;
  size: 'sm' | 'md' | 'lg';
  variant: 'primary' | 'secondary';
  position: 'first' | 'middle' | 'last' | 'single';
}

// Tab Button Component
const TabButton: React.FC<TabButtonProps> = ({
  id,
  label,
  active,
  disabled,
  onClick,
  size,
  variant,
  position
}) => {
  const handleClick = () => {
    if (!disabled) {
      onClick(id);
    }
  };

  // Size variations
  const sizeStyles = {
    sm: 'py-1.5 px-2 text-xs',
    md: 'py-2 px-3 text-sm',
    lg: 'py-2.5 px-4 text-base'
  };

  // Variant styles
  const variantStyles = {
    primary: {
      active: 'bg-green-500 text-white shadow-sm',
      inactive: 'text-gray-600 hover:text-gray-900 hover:bg-gray-200 bg-gray-50',
      disabled: 'text-gray-400 cursor-not-allowed bg-gray-100 opacity-60'
    },
    secondary: {
      active: 'bg-white text-gray-900 shadow-sm',
      inactive: 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 bg-gray-50',
      disabled: 'text-gray-400 cursor-not-allowed bg-gray-100 opacity-60'
    }
  };

  // Border radius based on position
  const positionStyles = {
    first: 'rounded-l-sm',
    middle: '',
    last: 'rounded-r-sm',
    single: 'rounded-sm'
  };

  const getButtonStyle = () => {
    if (disabled) return variantStyles[variant].disabled;
    if (active) return variantStyles[variant].active;
    return variantStyles[variant].inactive;
  };

  return (
    <button
      onClick={handleClick}
      disabled={disabled}
      className={cn(
        'flex-1 font-medium transition-all duration-150 relative',
        sizeStyles[size],
        getButtonStyle(),
        positionStyles[position]
      )}
    >
      {label}
      {disabled && (
        <div className="absolute inset-0 bg-gray-200 bg-opacity-50 rounded-sm pointer-events-none" />
      )}
    </button>
  );
};

// Main Tabs Component
export const CustomTabs: React.FC<CustomTabsProps> = ({
  items,
  activeId,
  onTabChange,
  size = 'md',
  variant = 'primary',
  className
}) => {
  const activeItem = items.find(item => item.id === activeId);
  
  const getTabPosition = (index: number, total: number): 'first' | 'middle' | 'last' | 'single' => {
    if (total === 1) return 'single';
    if (index === 0) return 'first';
    if (index === total - 1) return 'last';
    return 'middle';
  };

  // Background color based on variant
  const containerBg = variant === 'primary' ? 'bg-gray-100' : 'bg-gray-50';

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Tab Headers */}
      <div className="flex-shrink-0">
        <div className={cn('flex rounded-md p-0.5 gap-0.5', containerBg)}>
          {items.map((item, index) => (
            <TabButton
              key={item.id}
              id={item.id}
              label={item.label}
              active={activeId === item.id}
              disabled={item.disabled}
              onClick={onTabChange}
              size={size}
              variant={variant}
              position={getTabPosition(index, items.length)}
            />
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto">
        {activeItem && !activeItem.disabled && (
          <div className="h-full">
            {activeItem.content}
          </div>
        )}
      </div>
    </div>
  );
};

// Hook for managing tab state
export const useTabs = (defaultActiveId: string) => {
  const [activeId, setActiveId] = React.useState(defaultActiveId);

  const handleTabChange = React.useCallback((id: string) => {
    setActiveId(id);
  }, []);

  return {
    activeId,
    setActiveId,
    handleTabChange
  };
};