import i18n from "i18next";
import { initReactI18next } from "react-i18next";

import vi_common from "./locales/vi/common.json";
import en_common from "./locales/en/common.json";
import en_builder from "./locales/en/builder.json";

export const defaultNS = "common";

const savedLanguage = localStorage.getItem("language") || "vi";

i18n.use(initReactI18next).init({
  lng: savedLanguage,
  fallbackLng: "en",
  defaultNS,
  interpolation: {
    escapeValue: false,
  },
  resources: {
    vi: {
      common: vi_common,
    },
    en: {
      common: en_common,
      builder: en_builder,
    },
  },
});

export default i18n;
