import React from "react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TreeNode } from "../../../../context/types";
import { ZIndexPopover } from "../../inputs/ZIndexPopover";
import { CoordinatePopover } from "../../inputs/CoordinatePopover";
import { Z_INDEX_GROUPS } from "../../utils/zIndexGroups";
import {
  TOP_GROUPS,
  RIGHT_GROUPS,
  BOTTOM_GROUPS,
  LEFT_GROUPS,
} from "../../utils/coordinateGroups";

interface PositionSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

type PositionType = "static" | "fixed" | "absolute" | "relative" | "sticky";

export const PositionSection: React.FC<PositionSectionProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  // Detect current position from className
  const getPosition = (): PositionType => {
    if (currentClassName.includes("fixed")) return "fixed";
    if (currentClassName.includes("absolute")) return "absolute";
    if (currentClassName.includes("relative")) return "relative";
    if (currentClassName.includes("sticky")) return "sticky";
    return "static";
  };

  const currentPosition = getPosition();

  // Extract current z-index value (including negative)
  const extractZIndex = (): string => {
    const match = currentClassName.match(/(?:^|\s)(-?z-[^\s]+)/);
    return match ? match[1] : "none";
  };

  const currentZIndex = extractZIndex();

  // Extract coordinate values
  const extractCoordinate = (
    position: "top" | "right" | "bottom" | "left"
  ): string => {
    const prefix = position.charAt(0);
    const pattern = new RegExp(`(?:^|\\s)(-?${prefix}-[^\\s]+)`);
    const match = currentClassName.match(pattern);
    return match ? match[1] : "none";
  };

  const currentTop = extractCoordinate("top");
  const currentRight = extractCoordinate("right");
  const currentBottom = extractCoordinate("bottom");
  const currentLeft = extractCoordinate("left");

  // Update coordinate value
  const updateCoordinate = (
    newValue: string,
    position: "top" | "right" | "bottom" | "left"
  ) => {
    let updatedClassName = currentClassName;
    const prefix = position.charAt(0);

    // Remove existing coordinate class
    const removePattern = new RegExp(`(?:^|\\s)-?${prefix}-[^\\s]+`, "g");
    updatedClassName = updatedClassName.replace(removePattern, "").trim();

    // Add new class if not 'none'
    if (newValue && newValue !== "none") {
      updatedClassName = `${updatedClassName} ${newValue}`.trim();
    }

    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\\s+/g, " ").trim();

    onChange({
      style: {
        ...node.style,
        className: updatedClassName,
      },
    });
  };

  // Handle position change
  const handlePositionChange = (position: PositionType) => {
    let updatedClassName = currentClassName;

    // Remove all existing position classes
    updatedClassName = updatedClassName
      .replace(/(?:^|\s)static(?:\s|$)/g, " ")
      .replace(/(?:^|\s)fixed(?:\s|$)/g, " ")
      .replace(/(?:^|\s)absolute(?:\s|$)/g, " ")
      .replace(/(?:^|\s)relative(?:\s|$)/g, " ")
      .replace(/(?:^|\s)sticky(?:\s|$)/g, " ")
      .trim();

    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\s+/g, " ").trim();

    // Add new position class (except static which is default)
    if (position !== "static") {
      updatedClassName = `${updatedClassName} ${position}`.trim();
    }

    onChange({
      style: {
        ...node.style,
        className: updatedClassName,
      },
    });
  };

  // Update z-index value
  const updateZIndex = (newValue: string) => {
    let updatedClassName = currentClassName;

    // Remove existing z-index class (including negative)
    updatedClassName = updatedClassName
      .replace(/(?:^|\s)-?z-[^\s]+/g, "")
      .trim();

    // Add new class if not 'none'
    if (newValue && newValue !== "none") {
      updatedClassName = `${updatedClassName} ${newValue}`.trim();
    }

    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\s+/g, " ").trim();

    onChange({
      style: {
        ...node.style,
        className: updatedClassName,
      },
    });
  };

  return (
    <div className="space-y-4">
      {/* Position Type Selector */}
      <div className="space-y-2">
        <Label htmlFor="position-type" className="text-xs text-gray-600">
          Kiểu vị trí
        </Label>
        <Select
          value={currentPosition}
          onValueChange={handlePositionChange}
          disabled={disabled}
        >
          <SelectTrigger id="position-type" className="w-full">
            <SelectValue placeholder="Chọn kiểu vị trí">
              {currentPosition === "static" && "Tĩnh (static)"}
              {currentPosition === "relative" && "Tương đối (relative)"}
              {currentPosition === "absolute" && "Tuyệt đối (absolute)"}
              {currentPosition === "fixed" && "Cố định (fixed)"}
              {currentPosition === "sticky" && "Dính (sticky)"}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="static">
              <div className="flex flex-col items-start">
                <span>Tĩnh (static)</span>
                <span className="text-xs text-gray-500">
                  Mặc định - theo luồng tài liệu
                </span>
              </div>
            </SelectItem>
            <SelectItem value="relative">
              <div className="flex flex-col items-start">
                <span>Tương đối (relative)</span>
                <span className="text-xs text-gray-500">
                  Dịch chuyển từ vị trí gốc
                </span>
              </div>
            </SelectItem>
            <SelectItem value="absolute">
              <div className="flex flex-col items-start">
                <span>Tuyệt đối (absolute)</span>
                <span className="text-xs text-gray-500">
                  Định vị theo phần tử cha
                </span>
              </div>
            </SelectItem>
            <SelectItem value="fixed">
              <div className="flex flex-col items-start">
                <span>Cố định (fixed)</span>
                <span className="text-xs text-gray-500">
                  Cố định trên viewport
                </span>
              </div>
            </SelectItem>
            <SelectItem value="sticky">
              <div className="flex flex-col items-start">
                <span>Dính (sticky)</span>
                <span className="text-xs text-gray-500">
                  Kết hợp relative và fixed
                </span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Z-Index Control */}
      <div className="space-y-2">
        <Label htmlFor="z-index" className="text-xs text-gray-600">
          Thứ tự lớp (z-index)
        </Label>
        <ZIndexPopover
          id="z-index"
          value={currentZIndex}
          onValueChange={updateZIndex}
          placeholder="Chọn thứ tự lớp"
          disabled={disabled}
          groups={Z_INDEX_GROUPS}
        />
        <p className="text-xs text-gray-500">
          Giá trị cao hơn sẽ hiển thị phía trên
        </p>
      </div>

      {/* Position hints based on type */}
      {currentPosition !== "static" && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <p className="text-xs text-blue-800">
            {currentPosition === "relative" &&
              "Sử dụng top, right, bottom, left để dịch chuyển từ vị trí gốc."}
            {currentPosition === "absolute" &&
              "Sử dụng top, right, bottom, left để định vị. Phần tử cha cần có position khác static."}
            {currentPosition === "fixed" &&
              "Sử dụng top, right, bottom, left để định vị trên viewport."}
            {currentPosition === "sticky" &&
              "Sử dụng top, right, bottom, left để xác định ngưỡng dính."}
          </p>
        </div>
      )}

      {/* Coordinate Controls - only show for non-static positions */}
      {currentPosition !== "static" && (
        <div className="space-y-3">
          <Label
            className="text-xs text-gray-600 font-semibold"
            role="heading"
            aria-level={4}
          >
            Tọa độ
          </Label>

          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-2">
              <Label htmlFor="coordinate-top" className="text-xs text-gray-600">
                Trên (top)
              </Label>
              <CoordinatePopover
                id="coordinate-top"
                value={currentTop}
                onValueChange={(value) => updateCoordinate(value, "top")}
                placeholder="Chọn khoảng cách"
                disabled={disabled}
                groups={TOP_GROUPS}
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="coordinate-right"
                className="text-xs text-gray-600"
              >
                Phải (right)
              </Label>
              <CoordinatePopover
                id="coordinate-right"
                value={currentRight}
                onValueChange={(value) => updateCoordinate(value, "right")}
                placeholder="Chọn khoảng cách"
                disabled={disabled}
                groups={RIGHT_GROUPS}
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="coordinate-bottom"
                className="text-xs text-gray-600"
              >
                Dưới (bottom)
              </Label>
              <CoordinatePopover
                id="coordinate-bottom"
                value={currentBottom}
                onValueChange={(value) => updateCoordinate(value, "bottom")}
                placeholder="Chọn khoảng cách"
                disabled={disabled}
                groups={BOTTOM_GROUPS}
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="coordinate-left"
                className="text-xs text-gray-600"
              >
                Trái (left)
              </Label>
              <CoordinatePopover
                id="coordinate-left"
                value={currentLeft}
                onValueChange={(value) => updateCoordinate(value, "left")}
                placeholder="Chọn khoảng cách"
                disabled={disabled}
                groups={LEFT_GROUPS}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
