/**
 * Shared utilities for className manipulation across all sections
 * Centralized to reduce code duplication and ensure consistency
 */

/**
 * Creates a function that filters classes based on a predicate
 */
export const createClassNameFilter = (filterFn: (className: string) => boolean) => {
  return (className: string): string => {
    try {
      return className
        .split(/\s+/)
        .filter(filterFn)
        .join(' ')
        .trim();
    } catch (error) {
      console.warn('classNameUtils: Error filtering classes:', error);
      return className; // Return original on error
    }
  };
};

/**
 * Creates a function that removes classes starting with given prefixes
 */
export const removeClassesByPrefix = (prefixes: string[]) => 
  createClassNameFilter(c => !prefixes.some(prefix => c.startsWith(prefix)));

/**
 * Creates a function that keeps only classes starting with given prefixes
 */
export const keepClassesByPrefix = (prefixes: string[]) =>
  createClassNameFilter(c => prefixes.some(prefix => c.startsWith(prefix)));

/**
 * Safely combines class names, filtering out empty strings
 */
export const combineClassNames = (...classNames: (string | undefined | null)[]): string => {
  return classNames
    .filter(Boolean)
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();
};

/**
 * Common class removal functions
 */
export const removeBorderClasses = removeClassesByPrefix(['border']);
export const removeRoundedClasses = removeClassesByPrefix(['rounded']);
export const removeOpacityAndBgClasses = removeClassesByPrefix(['opacity-', 'bg-']);
export const removeShadowClasses = removeClassesByPrefix(['shadow', 'drop-shadow']);

/**
 * Common class extraction functions  
 */
export const getBorderClasses = keepClassesByPrefix(['border']);
export const getRoundedClasses = keepClassesByPrefix(['rounded']);

/**
 * Error-safe regex matching
 */
export const safeMatch = (text: string, regex: RegExp, fallback: string = ''): string => {
  try {
    const match = text.match(regex);
    return match ? match[0] : fallback;
  } catch (error) {
    console.warn('classNameUtils: Error in regex match:', error);
    return fallback;
  }
};