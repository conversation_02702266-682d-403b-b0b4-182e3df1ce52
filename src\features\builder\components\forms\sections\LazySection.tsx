import React, { Suspense, lazy } from "react";
import { TreeNode } from "../../../context/types";

interface LazySectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled: boolean;
  fallback?: React.ReactNode;
}

// Lazy loading for heavy sections
export const LazyAdvancedBackgroundSection = lazy(() =>
  import("./appearance/AdvancedBackgroundSection").then((module) => ({
    default: module.AdvancedBackgroundSection,
  }))
);

export const LazyTransformSection = lazy(() =>
  import("./advanced/TransformSection").then((module) => ({
    default: module.TransformSection,
  }))
);

export const LazyFilterSection = lazy(() =>
  import("./effects/FilterSection").then((module) => ({
    default: module.FilterSection,
  }))
);

// Wrapper component with error boundary
export const LazySection: React.FC<
  LazySectionProps & {
    component: React.LazyExoticComponent<React.ComponentType<LazySectionProps>>;
  }
> = ({
  component: Component,
  fallback = (
    <div className="p-4 text-center text-sm text-gray-500">Đang tải...</div>
  ),
  ...props
}) => {
  return (
    <Suspense fallback={fallback}>
      <Component {...props} />
    </Suspense>
  );
};
