# KHA Demo - Web Builder Platform

Một nền tảng xây dựng giao diện web mạnh mẽ với hệ thống node-based, hỗ trợ thiết kế UI trực quan và tương tác.

## 🚀 Quick Start

### Development Commands

**✅ NPM ONLY**: Project đã chuyển từ Yarn sang npm để tránh xung đột.

```bash
# Development
npm install      # Install dependencies  
npm run dev      # Khởi động Vite dev server với HMR
npm run build    # Build production (TypeScript check + Vite build)
npm run preview  # Preview production build
npm run lint     # Kiểm tra code với ESLint

# Testing  
npm run test         # Chạy Vitest trong watch mode
npm run test:ui      # Mở Vitest UI để test interactive
npm run test:coverage # Tạo test coverage report

# Package Management
npm install <pkg>           # Add dependency
npm install -D <pkg>        # Add dev dependency  
npm uninstall <pkg>         # Remove dependency
```

### ⚠️ **Critical Development Rules**
- **NEVER delete node_modules** để fix bugs - hãy báo cáo vấn đề
- **Known Issue**: Rollup dependency error trên WSL2/Linux environment
  - Error: `Cannot find module @rollup/rollup-linux-x64-gnu`
  - Solution: Báo cáo cho dev lead để fix dependency issue
- **Package Manager**: Chỉ sử dụng npm, không yarn

### Technology Stack
- **React 19** with TypeScript (strict mode enabled)
- **Vite 6** for bundling and development  
- **Tailwind CSS v4** with shadcn/ui component library
- **Redux Toolkit** + **React Query** for state management
- **React Router v7** for routing
- **i18next** for internationalization (Vietnamese and English)

## 📚 Documentation & Guidelines

**⚠️ QUAN TRỌNG**: Tất cả tài liệu hướng dẫn được tổ chức trong thư mục `/guidelines`

### 📖 Architecture Guidelines
- **[`guidelines/BUILDER_ARCHITECTURE.md`](./guidelines/BUILDER_ARCHITECTURE.md)** - Kiến trúc tổng thể, patterns và best practices

### 🔧 Development Guidelines  
Trước khi phát triển tính năng mới, **BẮT BUỘC** đọc:
```bash
# Đọc architecture guidelines
cat guidelines/BUILDER_ARCHITECTURE.md
```

## 🏗️ Builder Architecture Overview

### Core Components
- **WebBuilder**: Main builder interface tại `/features/builder/pages/WebBuilder.tsx`
- **Node System**: 3 loại nodes (Basic, Template, Widget)
- **Properties Panel**: Form system với Factory pattern
- **State Management**: Redux + History system (undo/redo)

### Node Development Guide

#### 1. Tạo Node Component
Tạo component trong thư mục phù hợp:
- **Basic**: `/features/builder/components/nodes/basic/`
- **Template**: `/features/builder/components/nodes/templates/`  
- **Widget**: `/features/builder/components/nodes/widgets/`

#### 2. Khai báo NodeType
Cập nhật file `/features/builder/context/types.ts`
```typescript
export type TreeNodeType = 
  | 'frame' | 'text' | 'image' | 'video'  // Basic
  | 'imageWithCaption'                     // Template  
  | 'dateTimeCard'                         // Widget
  | 'your-new-node';                       // Add new type
```

#### 3. Registry Configuration
Cập nhật `/features/builder/components/nodes/index.ts`:
- **nodeRegistry**: Component mapping
- **iconMap**: Icon cho từng node type
- **defaultNodeMap**: Cấu trúc mặc định

#### 4. Form Integration
Cập nhật `/features/builder/forms/registry/DisplayFormFactory.tsx`:
```typescript
const displayFormRegistry: DisplayFormRegistry = {
  // Existing forms...
  'your-new-node': YourNewDisplayForm,
};
```

## 📋 Best Practices

### ✅ DO
- Sử dụng Factory patterns cho extensibility
- Đọc data từ TreeNode, update qua onChange
- Tách biệt UI state và data state
- Follow existing architecture patterns
- Tạo documentation trong `/guidelines`

### ❌ DON'T  
- Tạo local state cho data có thể undo/redo
- Hard-code node types (dùng registry)
- Tạo file `.md` ở root (dùng `/guidelines`)
- Phá vỡ existing patterns
- **Xóa node_modules** để fix bugs (hãy báo cáo thay vì xóa)
- **Dùng yarn** (project đã chuyển sang npm only)

## 🎯 Project Structure

```
src/
├── features/builder/          # Main builder feature
│   ├── components/           # UI components
│   │   ├── Layout/          # Layout components
│   │   ├── nodes/           # Node components  
│   │   └── PropertiesPanel/ # Properties form components
│   ├── forms/               # Form system
│   │   ├── registry/        # Factory patterns
│   │   └── sections/        # Reusable form sections
│   ├── context/             # State management
│   └── pages/               # Main pages
└── components/ui/            # Shared UI components
```

## 📝 Contributing

1. **Đọc guidelines**: Luôn review `/guidelines` trước khi code
2. **Follow patterns**: Sử dụng existing architecture patterns  
3. **Document changes**: Cập nhật guidelines khi có thay đổi kiến trúc
4. **Test thoroughly**: Đảm bảo undo/redo hoạt động đúng
5. **Code review**: Tuân thủ checklist trong guidelines

## 📞 Support & Troubleshooting

### 🐛 Bug Report Protocol
Khi gặp vấn đề:
1. **KHÔNG xóa node_modules** 
2. **Mô tả chi tiết** lỗi và steps to reproduce
3. **Copy exact error message** 
4. **Báo cáo cho team lead** để được hỗ trợ

### 📚 Documentation References
- `/guidelines/BUILDER_ARCHITECTURE.md` - Detailed architecture guide
- Known issues & workarounds documented trong guidelines

### 🔧 Environment Issues
- **WSL2/Linux Rollup Error**: Known issue với `@rollup/rollup-linux-x64-gnu`
- **Solution**: Report to dev lead, không tự fix bằng cách xóa dependencies

---

**💡 Remember**: 
- Tài liệu là then chốt cho dự án dài hạn
- Luôn cập nhật `/guidelines` khi có thay đổi quan trọng
- **Báo cáo bugs thay vì tự fix dependencies**