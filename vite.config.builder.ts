import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import dts from "vite-plugin-dts";

export default defineConfig({
  plugins: [
    react(),
    dts({
      insertTypesEntry: true,
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, "src/builder-entry.ts"),
      name: "KhaBuilder",
      formats: ["es", "umd"],
      fileName: (format) => `builder.${format}.js`,
    },
    rollupOptions: {
      external: [
        // React core
        "react",
        "react-dom",
        "react-router-dom",

        // UI Libraries
        "lucide-react",
        "@radix-ui/react-dialog",
        "@radix-ui/react-dropdown-menu",
        "@radix-ui/react-select",
        "@radix-ui/react-tabs",
        "@radix-ui/react-tooltip",
        "@radix-ui/react-slot",
        "@radix-ui/react-separator",
        "@radix-ui/react-checkbox",
        "@radix-ui/react-collapsible",
        "@radix-ui/react-popover",
        "@radix-ui/react-switch",

        // UI Utils (Shadcn dependencies)
        "class-variance-authority",
        "clsx",
        "tailwind-merge",

        // Other dependencies that should be provided by consuming project
        "@dnd-kit/core",
        "@dnd-kit/sortable",
        "@dnd-kit/utilities",
        "cmdk",
        "next-themes",
      ],
      output: {
        globals: {
          react: "React",
          "react-dom": "ReactDOM",
          "react-router-dom": "ReactRouterDOM",
          "lucide-react": "LucideReact",
          "class-variance-authority": "ClassVarianceAuthority",
          clsx: "clsx",
          "tailwind-merge": "tailwindMerge",
        },
      },
    },
    outDir: "dist",
    emptyOutDir: true,
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
    },
  },
});
