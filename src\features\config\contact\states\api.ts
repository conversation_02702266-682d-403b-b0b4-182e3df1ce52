import { BaseResponse, restApi } from "@/api/restApi";
import type { ContactConfig } from "./type";

// Base URLs for config API
const PUBLIC_CONFIG_URL = "/portal/v1/public/config";
const ADMIN_CONFIG_URL = "/portal/v1/admin/config";

// GET /portal/v1/public/config/contacts
export async function fetchContactConfig(): Promise<
  BaseResponse<ContactConfig>
> {
  const res = await restApi.get<BaseResponse<ContactConfig>>(
    `${PUBLIC_CONFIG_URL}/contacts`
  );
  return res.data;
}

// POST /portal/v1/admin/config/contacts
export async function updateContactConfig(
  data: ContactConfig
): Promise<BaseResponse<ContactConfig>> {
  const res = await restApi.post<BaseResponse<ContactConfig>>(
    `${ADMIN_CONFIG_URL}/contacts`,
    data
  );
  return res.data;
}
