/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useMemo, useCallback } from "react";
import { TreeNode } from "../../../../context/types";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PaddingPopover } from "../../inputs/PaddingPopover";
import { MarginPopover } from "../../inputs/MarginPopover";

// Import all the groups we need
import {
  PADDING_GROUPS,
  PADDING_Y_GROUPS,
  PADDING_X_GROUPS,
  PADDING_T_GROUPS,
  PADDING_R_GROUPS,
  PADDING_B_GROUPS,
  PADDING_L_GROUPS,
  PADDING_S_GROUPS,
  PADDING_E_GROUPS,
} from "../../utils/paddingGroups";
import {
  MARGIN_GROUPS,
  MARGIN_Y_GROUPS,
  MARGIN_X_GROUPS,
  MARGIN_T_GROUPS,
  MARGIN_R_GROUPS,
  MARGIN_B_GROUPS,
  MARGIN_L_GROUPS,
  MARGIN_S_GROUPS,
  MARGIN_E_GROUPS,
} from "../../utils/marginGroups";
import { safeMatch } from "../../utils/classNameUtils";

interface SpacingSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

// Helper functions từ PaddingSection
const removePaddingClasses = (className: string): string => {
  return className
    .split(/\s+/)
    .filter(
      (cls) =>
        !cls.startsWith("p-") &&
        !cls.startsWith("px-") &&
        !cls.startsWith("py-") &&
        !cls.startsWith("pt-") &&
        !cls.startsWith("pr-") &&
        !cls.startsWith("pb-") &&
        !cls.startsWith("pl-") &&
        !cls.startsWith("ps-") &&
        !cls.startsWith("pe-")
    )
    .join(" ")
    .trim();
};

// Helper functions từ MarginSection
const removeMarginClasses = (className: string): string => {
  return className
    .split(/\s+/)
    .filter(
      (cls) =>
        !cls.startsWith("m-") &&
        !cls.startsWith("mx-") &&
        !cls.startsWith("my-") &&
        !cls.startsWith("mt-") &&
        !cls.startsWith("mr-") &&
        !cls.startsWith("mb-") &&
        !cls.startsWith("ml-") &&
        !cls.startsWith("ms-") &&
        !cls.startsWith("me-")
    )
    .join(" ")
    .trim();
};

// Parse padding state giống như PaddingSection
const parsePaddingState = (className: string) => {
  const p = safeMatch(className, /\bp-[^\s]+/, "none");
  const px = safeMatch(className, /\bpx-[^\s]+/, "none");
  const py = safeMatch(className, /\bpy-[^\s]+/, "none");
  const pt = safeMatch(className, /\bpt-[^\s]+/, "none");
  const pr = safeMatch(className, /\bpr-[^\s]+/, "none");
  const pb = safeMatch(className, /\bpb-[^\s]+/, "none");
  const pl = safeMatch(className, /\bpl-[^\s]+/, "none");
  const ps = safeMatch(className, /\bps-[^\s]+/, "none");
  const pe = safeMatch(className, /\bpe-[^\s]+/, "none");

  let mode = "default";
  if (p !== "none") mode = "all";
  else if (py !== "none" || px !== "none")
    mode = py !== "none" ? "vertical" : "horizontal";
  else if (pt !== "none" || pr !== "none" || pb !== "none" || pl !== "none")
    mode = "individual";
  else if (ps !== "none") mode = "start";
  else if (pe !== "none") mode = "end";

  return { mode, p, px, py, pt, pr, pb, pl, ps, pe };
};

// Parse margin state giống như MarginSection
const parseMarginState = (className: string) => {
  const m = safeMatch(className, /\bm-[^\s]+/, "none");
  const mx = safeMatch(className, /\bmx-[^\s]+/, "none");
  const my = safeMatch(className, /\bmy-[^\s]+/, "none");
  const mt = safeMatch(className, /\bmt-[^\s]+/, "none");
  const mr = safeMatch(className, /\bmr-[^\s]+/, "none");
  const mb = safeMatch(className, /\bmb-[^\s]+/, "none");
  const ml = safeMatch(className, /\bml-[^\s]+/, "none");
  const ms = safeMatch(className, /\bms-[^\s]+/, "none");
  const me = safeMatch(className, /\bme-[^\s]+/, "none");

  let mode = "default";
  if (m !== "none") mode = "all";
  else if (my !== "none" || mx !== "none")
    mode = my !== "none" ? "vertical" : "horizontal";
  else if (mt !== "none" || mr !== "none" || mb !== "none" || ml !== "none")
    mode = "individual";
  else if (ms !== "none") mode = "start";
  else if (me !== "none") mode = "end";

  return { mode, m, mx, my, mt, mr, mb, ml, ms, me };
};

const SpacingSectionComponent: React.FC<SpacingSectionProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  // Parse current state
  const paddingState = useMemo(
    () => parsePaddingState(currentClassName),
    [currentClassName]
  );
  const marginState = useMemo(
    () => parseMarginState(currentClassName),
    [currentClassName]
  );

  const updateClassName = useCallback(
    (newClassName: string) => {
      onChange({
        style: {
          ...node.style,
          className: newClassName,
        },
      });
    },
    [node.style, onChange]
  );

  // Padding mode change handler
  const handlePaddingModeChange = useCallback(
    (newMode: string) => {
      // Remove all padding classes when changing mode
      let cleanedClassName = removePaddingClasses(currentClassName);

      // Set default value for new mode (except default)
      if (newMode !== "default") {
        const defaultValue = "1"; // Default padding value

        switch (newMode) {
          case "all":
            cleanedClassName = `${cleanedClassName} p-${defaultValue}`.trim();
            break;
          case "vertical":
            cleanedClassName = `${cleanedClassName} py-${defaultValue}`.trim();
            break;
          case "horizontal":
            cleanedClassName = `${cleanedClassName} px-${defaultValue}`.trim();
            break;
          case "start":
            cleanedClassName = `${cleanedClassName} ps-${defaultValue}`.trim();
            break;
          case "end":
            cleanedClassName = `${cleanedClassName} pe-${defaultValue}`.trim();
            break;
          case "individual":
            cleanedClassName =
              `${cleanedClassName} pt-${defaultValue} pr-${defaultValue} pb-${defaultValue} pl-${defaultValue}`.trim();
            break;
        }
      }

      updateClassName(cleanedClassName);
    },
    [currentClassName, updateClassName]
  );

  // Margin mode change handler
  const handleMarginModeChange = useCallback(
    (newMode: string) => {
      // Remove all margin classes when changing mode
      let cleanedClassName = removeMarginClasses(currentClassName);

      // Set default value for new mode (except default)
      if (newMode !== "default") {
        const defaultValue = "1"; // Default margin value

        switch (newMode) {
          case "all":
            cleanedClassName = `${cleanedClassName} m-${defaultValue}`.trim();
            break;
          case "vertical":
            cleanedClassName = `${cleanedClassName} my-${defaultValue}`.trim();
            break;
          case "horizontal":
            cleanedClassName = `${cleanedClassName} mx-${defaultValue}`.trim();
            break;
          case "start":
            cleanedClassName = `${cleanedClassName} ms-${defaultValue}`.trim();
            break;
          case "end":
            cleanedClassName = `${cleanedClassName} me-${defaultValue}`.trim();
            break;
          case "individual":
            cleanedClassName =
              `${cleanedClassName} mt-${defaultValue} mr-${defaultValue} mb-${defaultValue} ml-${defaultValue}`.trim();
            break;
        }
      }

      updateClassName(cleanedClassName);
    },
    [currentClassName, updateClassName]
  );

  // Padding value update
  const updatePadding = useCallback(
    (value: string) => {
      let cleanedClassName = removePaddingClasses(currentClassName);

      if (value && value !== "none") {
        // Value is already full class like "p-1", not just "1"
        cleanedClassName = `${cleanedClassName} ${value}`.trim();
      }

      updateClassName(cleanedClassName);
    },
    [currentClassName, updateClassName]
  );

  // Margin value update
  const updateMargin = useCallback(
    (value: string) => {
      let cleanedClassName = removeMarginClasses(currentClassName);

      if (value && value !== "none") {
        // Value is already full class like "m-1", not just "1"
        cleanedClassName = `${cleanedClassName} ${value}`.trim();
      }

      updateClassName(cleanedClassName);
    },
    [currentClassName, updateClassName]
  );

  return (
    <div className="space-y-6">
      {/* PADDING SECTION */}
      <div className="space-y-3">
        <Label className="text-sm font-medium" role="heading" aria-level={4}>
          Lề trong
        </Label>

        {/* Mode selector và controls cùng hàng */}
        <div className="flex gap-3">
          {/* Mode selector */}
          <div className="flex-1">
            <Select
              value={paddingState.mode}
              onValueChange={handlePaddingModeChange}
              disabled={disabled}
            >
              <SelectTrigger id="padding-mode" className="w-full">
                <SelectValue placeholder="Chọn chế độ" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Mặc định</SelectItem>
                <SelectItem value="all">Toàn bộ</SelectItem>
                <SelectItem value="vertical">Trên dưới</SelectItem>
                <SelectItem value="horizontal">Trái phải</SelectItem>
                <SelectItem value="individual">Độc lập</SelectItem>
                <SelectItem value="start">Bắt đầu</SelectItem>
                <SelectItem value="end">Kết thúc</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Value control cùng hàng - hiển thị theo mode */}
          {paddingState.mode !== "default" &&
            paddingState.mode !== "individual" && (
              <div className="flex-1">
                {paddingState.mode === "all" && (
                  <PaddingPopover
                    value={paddingState.p}
                    onValueChange={updatePadding}
                    placeholder="Chọn giá trị"
                    disabled={disabled}
                    groups={PADDING_GROUPS}
                  />
                )}
                {paddingState.mode === "vertical" && (
                  <PaddingPopover
                    value={paddingState.py}
                    onValueChange={updatePadding}
                    placeholder="Chọn giá trị"
                    disabled={disabled}
                    groups={PADDING_Y_GROUPS}
                  />
                )}
                {paddingState.mode === "horizontal" && (
                  <PaddingPopover
                    value={paddingState.px}
                    onValueChange={updatePadding}
                    placeholder="Chọn giá trị"
                    disabled={disabled}
                    groups={PADDING_X_GROUPS}
                  />
                )}
                {paddingState.mode === "start" && (
                  <PaddingPopover
                    value={paddingState.ps}
                    onValueChange={updatePadding}
                    placeholder="Chọn giá trị"
                    disabled={disabled}
                    groups={PADDING_S_GROUPS}
                  />
                )}
                {paddingState.mode === "end" && (
                  <PaddingPopover
                    value={paddingState.pe}
                    onValueChange={updatePadding}
                    placeholder="Chọn giá trị"
                    disabled={disabled}
                    groups={PADDING_E_GROUPS}
                  />
                )}
              </div>
            )}
        </div>

        {/* Individual controls trong grid riêng */}
        {paddingState.mode === "individual" && (
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label htmlFor="padding-top" className="text-xs text-gray-600">
                Trên
              </Label>
              <PaddingPopover
                value={paddingState.pt}
                onValueChange={updatePadding}
                placeholder="Trên"
                disabled={disabled}
                groups={PADDING_T_GROUPS}
              />
            </div>
            <div>
              <Label htmlFor="padding-right" className="text-xs text-gray-600">
                Phải
              </Label>
              <PaddingPopover
                value={paddingState.pr}
                onValueChange={updatePadding}
                placeholder="Phải"
                disabled={disabled}
                groups={PADDING_R_GROUPS}
              />
            </div>
            <div>
              <Label htmlFor="padding-bottom" className="text-xs text-gray-600">
                Dưới
              </Label>
              <PaddingPopover
                value={paddingState.pb}
                onValueChange={updatePadding}
                placeholder="Dưới"
                disabled={disabled}
                groups={PADDING_B_GROUPS}
              />
            </div>
            <div>
              <Label htmlFor="padding-left" className="text-xs text-gray-600">
                Trái
              </Label>
              <PaddingPopover
                value={paddingState.pl}
                onValueChange={updatePadding}
                placeholder="Trái"
                disabled={disabled}
                groups={PADDING_L_GROUPS}
              />
            </div>
          </div>
        )}
      </div>

      {/* MARGIN SECTION */}
      <div className="space-y-3">
        <Label className="text-sm font-medium" role="heading" aria-level={4}>
          Lề ngoài
        </Label>

        {/* Mode selector và controls cùng hàng */}
        <div className="flex gap-3">
          {/* Mode selector */}
          <div className="flex-1">
            <Select
              value={marginState.mode}
              onValueChange={handleMarginModeChange}
              disabled={disabled}
            >
              <SelectTrigger id="margin-mode" className="w-full">
                <SelectValue placeholder="Chọn chế độ" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Mặc định</SelectItem>
                <SelectItem value="all">Toàn bộ</SelectItem>
                <SelectItem value="vertical">Trên dưới</SelectItem>
                <SelectItem value="horizontal">Trái phải</SelectItem>
                <SelectItem value="individual">Độc lập</SelectItem>
                <SelectItem value="start">Bắt đầu</SelectItem>
                <SelectItem value="end">Kết thúc</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Value control cùng hàng - hiển thị theo mode */}
          {marginState.mode !== "default" &&
            marginState.mode !== "individual" && (
              <div className="flex-1">
                {marginState.mode === "all" && (
                  <MarginPopover
                    value={marginState.m}
                    onValueChange={updateMargin}
                    placeholder="Chọn giá trị"
                    disabled={disabled}
                    groups={MARGIN_GROUPS}
                  />
                )}
                {marginState.mode === "vertical" && (
                  <MarginPopover
                    value={marginState.my}
                    onValueChange={updateMargin}
                    placeholder="Chọn giá trị"
                    disabled={disabled}
                    groups={MARGIN_Y_GROUPS}
                  />
                )}
                {marginState.mode === "horizontal" && (
                  <MarginPopover
                    value={marginState.mx}
                    onValueChange={updateMargin}
                    placeholder="Chọn giá trị"
                    disabled={disabled}
                    groups={MARGIN_X_GROUPS}
                  />
                )}
                {marginState.mode === "start" && (
                  <MarginPopover
                    value={marginState.ms}
                    onValueChange={updateMargin}
                    placeholder="Chọn giá trị"
                    disabled={disabled}
                    groups={MARGIN_S_GROUPS}
                  />
                )}
                {marginState.mode === "end" && (
                  <MarginPopover
                    value={marginState.me}
                    onValueChange={updateMargin}
                    placeholder="Chọn giá trị"
                    disabled={disabled}
                    groups={MARGIN_E_GROUPS}
                  />
                )}
              </div>
            )}
        </div>

        {/* Individual controls trong grid riêng */}
        {marginState.mode === "individual" && (
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label htmlFor="margin-top" className="text-xs text-gray-600">
                Trên
              </Label>
              <MarginPopover
                value={marginState.mt}
                onValueChange={updateMargin}
                placeholder="Trên"
                disabled={disabled}
                groups={MARGIN_T_GROUPS}
              />
            </div>
            <div>
              <Label htmlFor="margin-right" className="text-xs text-gray-600">
                Phải
              </Label>
              <MarginPopover
                value={marginState.mr}
                onValueChange={updateMargin}
                placeholder="Phải"
                disabled={disabled}
                groups={MARGIN_R_GROUPS}
              />
            </div>
            <div>
              <Label htmlFor="margin-bottom" className="text-xs text-gray-600">
                Dưới
              </Label>
              <MarginPopover
                value={marginState.mb}
                onValueChange={updateMargin}
                placeholder="Dưới"
                disabled={disabled}
                groups={MARGIN_B_GROUPS}
              />
            </div>
            <div>
              <Label htmlFor="margin-left" className="text-xs text-gray-600">
                Trái
              </Label>
              <MarginPopover
                value={marginState.ml}
                onValueChange={updateMargin}
                placeholder="Trái"
                disabled={disabled}
                groups={MARGIN_L_GROUPS}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export const SpacingSection = React.memo(SpacingSectionComponent);
