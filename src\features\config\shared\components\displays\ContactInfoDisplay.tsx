import React from "react";
import { MapPin, Phone, Mail } from "lucide-react";
import { cn } from "@/lib/utils";

/**
 * Contact item interface
 */
export interface ContactItem {
  name: string;
  address?: string;
  phone?: string;
  email?: string;
}

/**
 * Props for ContactInfoDisplay component
 */
export interface ContactInfoDisplayProps {
  /** Array of contact items to display */
  contacts: ContactItem[];
  /** Whether to show icons */
  showIcons?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show contact names */
  showNames?: boolean;
}

/**
 * Clean, reusable contact information display component
 * Suitable for both preview and public page usage
 */
export const ContactInfoDisplay: React.FC<ContactInfoDisplayProps> = ({
  contacts,
  showIcons = true,
  className,
  showNames = true,
}) => {
  if (!contacts || contacts.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-6 p-4", className)}>
      {showNames && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Thông tin liên hệ
        </h3>
      )}

      <div className="space-y-6">
        {contacts.map((contact, index) => (
          <div key={`contact-${index}-${contact.name}`} className="space-y-2">
            {/* Contact Name */}
            {showNames && (
              <h4 className="font-semibold text-base text-gray-900">
                {contact.name}
              </h4>
            )}

            {/* Contact Details */}
            <div className="space-y-1">
              {/* Address */}
              {contact.address && (
                <div className="flex items-start gap-2 text-sm text-gray-600">
                  {showIcons && <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0 text-gray-500" />}
                  <span className="break-words">{contact.address}</span>
                </div>
              )}

              {/* Phone */}
              {contact.phone && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  {showIcons && <Phone className="h-4 w-4 flex-shrink-0 text-gray-500" />}
                  <a
                    href={`tel:${contact.phone}`}
                    className="hover:text-blue-600 transition-colors"
                  >
                    {contact.phone}
                  </a>
                </div>
              )}

              {/* Email */}
              {contact.email && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  {showIcons && <Mail className="h-4 w-4 flex-shrink-0 text-gray-500" />}
                  <a
                    href={`mailto:${contact.email}`}
                    className="hover:text-blue-600 transition-colors break-all"
                  >
                    {contact.email}
                  </a>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
