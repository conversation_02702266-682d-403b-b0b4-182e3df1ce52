import type { AppearanceState, RoundedMode, RoundedState } from "./types";
import { ROUNDED_PREFIX_MAP } from "./types";
import {
  memoize,
  appearanceParseCache,
  classNameCleanCache,
} from "../../../../../utils/cache";

/**
 * Check if a string is a valid rounded size className segment
 * Examples: "sm", "md", "lg", "xl", "2xl", "3xl", "full", "[8px]"
 */
function isRoundedSizeClassName(segment: string): boolean {
  // Valid Tailwind rounded size values
  const validSizes = ["none", "sm", "md", "lg", "xl", "2xl", "3xl", "full"];
  if (validSizes.includes(segment)) return true;

  // Check for arbitrary size values [8px], [1rem], etc.
  if (segment.startsWith("[") && segment.endsWith("]")) {
    const sizeUnits = ["px", "rem", "em", "%"];
    return sizeUnits.some((unit) => segment.includes(unit));
  }

  return false;
}

// removeRoundedClasses function moved to utils/classNameUtils.ts to avoid duplicate exports

/**
 * Remove all appearance classes from className
 */
export const removeAppearanceClasses = memoize(
  (className: string): string => {
    return className
      .split(/\s+/)
      .filter(
        (c) =>
          !c.startsWith("opacity-") &&
          !c.startsWith("bg-") &&
          !c.startsWith("rounded")
      )
      .join(" ")
      .trim();
  },
  classNameCleanCache,
  (className) => `appearance:${className}`
);

/**
 * Parse rounded state from className
 * Returns 'custom' mode if rounded classes exist but don't match expected patterns
 */
export const parseRoundedState = memoize(
  (className: string): RoundedState => {
    const classes = className.trim().split(/\s+/);

    // Default state
    const state: RoundedState = {
      enabled: false,
      mode: "none",
      options: [],
    };

    // Check if rounded is enabled
    state.enabled = classes.some((c) => c.startsWith("rounded"));
    if (!state.enabled) return state;

    // Step 1: Detect mode based on className patterns
    if (
      classes.some(
        (c) =>
          c.startsWith("rounded-tl") ||
          c.startsWith("rounded-tr") ||
          c.startsWith("rounded-br") ||
          c.startsWith("rounded-bl")
      )
    ) {
      state.mode = "corners";
    } else if (
      classes.some((c) => c === "rounded-t" || c.startsWith("rounded-t-"))
    ) {
      state.mode = "top";
    } else if (
      classes.some((c) => c === "rounded-r" || c.startsWith("rounded-r-"))
    ) {
      state.mode = "right";
    } else if (
      classes.some((c) => c === "rounded-b" || c.startsWith("rounded-b-"))
    ) {
      state.mode = "bottom";
    } else if (
      classes.some((c) => c === "rounded-l" || c.startsWith("rounded-l-"))
    ) {
      state.mode = "left";
    } else {
      state.mode = "all";
    }

    // Step 2: Extract values based on mode prefixes
    const roundedPrefixes = ROUNDED_PREFIX_MAP[state.mode];

    state.options = roundedPrefixes.map((roundedPrefix) => {
      let size = "";

      // Find all classes that start with this rounded prefix
      const prefixClasses = classes.filter((c) => c.startsWith(roundedPrefix));

      for (const className of prefixClasses) {
        if (className === roundedPrefix) {
          // Just "rounded", "rounded-t", etc. without suffix means base size
          size = "base";
        } else if (className.startsWith(`${roundedPrefix}-`)) {
          // Extract the suffix after the prefix
          const suffix = className.substring(roundedPrefix.length + 1);

          // Check if it's a valid size
          if (isRoundedSizeClassName(suffix)) {
            size = suffix;
          }
        }
      }

      return { size };
    });

    // Step 3: Validate if we have enough information for the detected mode
    if (
      state.enabled &&
      (state.mode as string) !== "custom" &&
      (state.mode as string) !== "none"
    ) {
      let hasEnoughInfo = false;

      switch (state.mode) {
        case "all":
        case "top":
        case "right":
        case "bottom":
        case "left":
          // For single direction modes, we need one complete option
          hasEnoughInfo =
            state.options.length > 0 && Boolean(state.options[0].size);
          break;

        case "corners":
          // For corners mode, we should have 4 options or at least some complete ones
          hasEnoughInfo =
            state.options.length > 0 && state.options.some((opt) => opt.size);
          break;

        default:
          hasEnoughInfo = true;
      }

      // If we don't have enough information, mark as custom mode
      if (!hasEnoughInfo) {
        state.mode = "custom";
      }
    }

    // Check for patterns we don't support
    const hasUnsupportedPatterns = classes.some((c) => {
      // Check for complex arbitrary values we can't parse
      if (c.includes("[") && c.includes("]") && c.startsWith("rounded"))
        return true;
      // Check for logical direction classes (s, e, ss, se, etc.)
      if (/^rounded-[se]-/.test(c) || /^rounded-[se]{2}-/.test(c)) return true;
      return false;
    });

    if (hasUnsupportedPatterns) {
      state.mode = "custom";
    }

    return state;
  },
  appearanceParseCache,
  (className) => `rounded:${className}`
);

/**
 * Parse appearance state from className string
 */
export const parseAppearanceState = memoize(
  (className: string): AppearanceState => {
    // Parse opacity
    const opacityMatch = className.match(/\bopacity-\d+\b/);
    const opacity = opacityMatch ? opacityMatch[0] : "opacity-100";

    // Parse background color
    const bgMatch = className.match(/\bbg-\S+\b/);
    const backgroundColor = bgMatch ? bgMatch[0].replace("bg-", "") : "";

    // Parse rounded state
    const rounded = parseRoundedState(className) as RoundedState;

    return {
      opacity,
      backgroundColor,
      rounded,
    };
  },
  appearanceParseCache,
  (className) => `appearance:${className}`
);

/**
 * Convert RoundedState to Tailwind CSS classes
 */
export function roundedStateToClassName(state: RoundedState): string {
  if (!state.enabled || state.mode === "none") return "";

  // For custom mode, we don't generate any classes
  // The UI should preserve existing rounded classes
  if (state.mode === "custom") {
    return ""; // Return empty, let UI handle preserving existing classes
  }

  const classNames: string[] = [];

  // Get prefix based on mode
  const roundedPrefixes = ROUNDED_PREFIX_MAP[state.mode];

  // Iterate through each option (option i corresponds to prefix i)
  state.options.forEach((opt, idx) => {
    if (opt.size) {
      const prefix = roundedPrefixes[idx] || roundedPrefixes[0]; // avoid undefined

      // For corners mode, there's no "base" - must always have a size
      if (state.mode === "corners") {
        if (opt.size === "base") {
          // Skip - invalid for corners
          return;
        } else if (opt.size === "none") {
          classNames.push(`${prefix}-none`);
        } else {
          classNames.push(`${prefix}-${opt.size}`);
        }
      } else {
        // For other modes, "base" means just the prefix
        if (opt.size === "base") {
          classNames.push(prefix);
        } else if (opt.size === "none") {
          classNames.push(`${prefix}-none`);
        } else {
          classNames.push(`${prefix}-${opt.size}`);
        }
      }
    }
  });

  return classNames.join(" ");
}

/**
 * Convert appearance state back to className
 */
export function appearanceStateToClassName(state: AppearanceState): string {
  const classes: string[] = [];

  // Add opacity
  if (state.opacity && state.opacity !== "opacity-100") {
    classes.push(state.opacity);
  }

  // Add background color
  if (state.backgroundColor) {
    classes.push(`bg-${state.backgroundColor}`);
  }

  // Add rounded classes
  const roundedClasses = roundedStateToClassName(state.rounded);
  if (roundedClasses) {
    classes.push(roundedClasses);
  }

  return classes.join(" ");
}

/**
 * Create default RoundedState for specific mode
 */
function createDefaultRoundedStateForMode(mode: RoundedMode): RoundedState {
  if (mode === "none") {
    return {
      enabled: false,
      mode: "none",
      options: [],
    };
  }

  const optionCount = mode === "corners" ? 4 : 1;

  return {
    enabled: true,
    mode,
    options: Array(optionCount)
      .fill(null)
      .map(() => ({
        size: "sm", // Default to 'sm' instead of 'base'
      })),
  };
}

/**
 * Switch rounded mode and copy values from first option when possible
 */
export function switchRoundedMode(
  oldState: RoundedState,
  newMode: RoundedMode
): RoundedState {
  // Don't allow switching to custom mode - it's only a fallback state
  if (newMode === "custom") {
    return oldState;
  }

  // If switching to none mode, return disabled state
  if (newMode === "none") {
    return createDefaultRoundedStateForMode("none");
  }

  // If switching from none or custom mode, create default state for new mode
  if (oldState.mode === "none" || oldState.mode === "custom") {
    return createDefaultRoundedStateForMode(newMode);
  }

  // For other modes, preserve values from first option when possible
  const newState = createDefaultRoundedStateForMode(newMode);

  // Copy values from first option if available
  const firstOption = oldState.options[0];
  if (firstOption && firstOption.size) {
    newState.options = newState.options.map(() => ({
      size: firstOption.size,
    }));
  }

  return newState;
}
