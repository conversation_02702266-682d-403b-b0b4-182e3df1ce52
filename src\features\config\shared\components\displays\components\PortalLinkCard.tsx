import React, { useState, useEffect } from "react";
import { Image } from "lucide-react";
import { cn } from "@/lib/utils";

export interface PortalLinkProps {
    title: string;
    url: string;
    image?: string;
}

export const PortalLinkCard: React.FC<PortalLinkProps> = ({
    title,
    url,
    image,
}) => {
    const [imageError, setImageError] = useState(false);
    const [imageKey, setImageKey] = useState(0);

    // Reset image error state and force reload when image prop changes
    useEffect(() => {
        setImageError(false);
        setImageKey(prev => prev + 1);
    }, [image]);

    const handleImageError = () => {
        setImageError(true);
    };

    return (
        <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group border border-[#BAE4FD]"
        >
            <div className="relative h-32 bg-gray-100 flex items-center justify-center">
                {image && !imageError ? (
                    <img
                        key={imageKey}
                        src={image}
                        alt={title}
                        className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
                        onError={handleImageError}
                    />
                ) : null}

                <div className={cn(
                    "m-auto inset-0 flex items-center justify-center",
                    image && !imageError ? 'hidden' : ''
                )}>
                    <Image className="m-auto h-max w-8 text-gray-400" />
                </div>
            </div>

            <div className="p-3">
                <h3 className="text-sm font-semibold text-gray-900 line-clamp-2">
                    {title}
                </h3>
            </div>
        </a>
    );
};
