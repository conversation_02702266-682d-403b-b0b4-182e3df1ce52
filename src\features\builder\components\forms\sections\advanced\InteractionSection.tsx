import React, { useMemo, useCallback } from 'react';
import { TreeNode } from '../../../../context/types';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import { safeMatch, combineClassNames } from '../../utils/classNameUtils';

interface InteractionSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

const CURSOR_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'cursor-auto', label: 'Auto' },
  { value: 'cursor-default', label: 'Default' },
  { value: 'cursor-pointer', label: 'Pointer' },
  { value: 'cursor-wait', label: 'Wait' },
  { value: 'cursor-text', label: 'Text' },
  { value: 'cursor-move', label: 'Move' },
  { value: 'cursor-help', label: 'Help' },
  { value: 'cursor-not-allowed', label: 'Not Allowed' },
  { value: 'cursor-none', label: 'None' },
  { value: 'cursor-context-menu', label: 'Context Menu' },
  { value: 'cursor-progress', label: 'Progress' },
  { value: 'cursor-cell', label: 'Cell' },
  { value: 'cursor-crosshair', label: 'Crosshair' },
  { value: 'cursor-vertical-text', label: 'Vertical Text' },
  { value: 'cursor-alias', label: 'Alias' },
  { value: 'cursor-copy', label: 'Copy' },
  { value: 'cursor-no-drop', label: 'No Drop' },
  { value: 'cursor-grab', label: 'Grab' },
  { value: 'cursor-grabbing', label: 'Grabbing' },
  { value: 'cursor-all-scroll', label: 'All Scroll' },
  { value: 'cursor-col-resize', label: 'Col Resize' },
  { value: 'cursor-row-resize', label: 'Row Resize' },
  { value: 'cursor-n-resize', label: 'N Resize' },
  { value: 'cursor-e-resize', label: 'E Resize' },
  { value: 'cursor-s-resize', label: 'S Resize' },
  { value: 'cursor-w-resize', label: 'W Resize' },
  { value: 'cursor-ne-resize', label: 'NE Resize' },
  { value: 'cursor-nw-resize', label: 'NW Resize' },
  { value: 'cursor-se-resize', label: 'SE Resize' },
  { value: 'cursor-sw-resize', label: 'SW Resize' },
  { value: 'cursor-ew-resize', label: 'EW Resize' },
  { value: 'cursor-ns-resize', label: 'NS Resize' },
  { value: 'cursor-nesw-resize', label: 'NESW Resize' },
  { value: 'cursor-nwse-resize', label: 'NWSE Resize' },
  { value: 'cursor-zoom-in', label: 'Zoom In' },
  { value: 'cursor-zoom-out', label: 'Zoom Out' }
];

const POINTER_EVENTS_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'pointer-events-none', label: 'None (Không tương tác)' },
  { value: 'pointer-events-auto', label: 'Auto (Tương tác bình thường)' }
];

const USER_SELECT_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'select-none', label: 'None (Không chọn được)' },
  { value: 'select-text', label: 'Text (Chọn text)' },
  { value: 'select-all', label: 'All (Chọn tất cả)' },
  { value: 'select-auto', label: 'Auto' }
];

const TOUCH_ACTION_OPTIONS = [
  { value: 'default', label: 'Mặc định' },
  { value: 'touch-auto', label: 'Auto' },
  { value: 'touch-none', label: 'None' },
  { value: 'touch-pan-x', label: 'Pan X' },
  { value: 'touch-pan-left', label: 'Pan Left' },
  { value: 'touch-pan-right', label: 'Pan Right' },
  { value: 'touch-pan-y', label: 'Pan Y' },
  { value: 'touch-pan-up', label: 'Pan Up' },
  { value: 'touch-pan-down', label: 'Pan Down' },
  { value: 'touch-pinch-zoom', label: 'Pinch Zoom' },
  { value: 'touch-manipulation', label: 'Manipulation' }
];

const removeInteractionClasses = (className: string): string => {
  return className
    .split(' ')
    .filter(cls => 
      !cls.startsWith('cursor-') &&
      !cls.startsWith('pointer-events-') &&
      !cls.startsWith('select-') &&
      !cls.startsWith('touch-')
    )
    .join(' ')
    .trim();
};

const InteractionSectionComponent: React.FC<InteractionSectionProps> = ({ node, onChange, disabled }) => {
  const currentClassName = node.style?.className || '';
  
  const currentState = useMemo(() => {
    const cursor = safeMatch(currentClassName, /\bcursor-\S+\b/, 'default');
    const pointerEvents = safeMatch(currentClassName, /\bpointer-events-\S+\b/, 'default');
    const userSelect = safeMatch(currentClassName, /\bselect-\S+\b/, 'default');
    const touchAction = safeMatch(currentClassName, /\btouch-\S+\b/, 'default');

    return { cursor, pointerEvents, userSelect, touchAction };
  }, [currentClassName]);

  const cleanClassName = useMemo(() => {
    return removeInteractionClasses(currentClassName);
  }, [currentClassName]);

  const updateClassName = (newClassName: string) => {
    onChange({
      style: {
        ...node.style,
        className: newClassName
      }
    });
  };

  const handleChange = useCallback((type: string, value: string) => {
    const newState = { ...currentState, [type]: value };
    
    const classes = [
      cleanClassName,
      newState.cursor !== 'default' ? newState.cursor : '',
      newState.pointerEvents !== 'default' ? newState.pointerEvents : '',
      newState.userSelect !== 'default' ? newState.userSelect : '',
      newState.touchAction !== 'default' ? newState.touchAction : ''
    ].filter(Boolean);
    
    updateClassName(combineClassNames(...classes));
  }, [cleanClassName, currentState]);

  return (
    <div className="space-y-3">
      {/* Cursor */}
      <Select 
        value={currentState.cursor} 
        onValueChange={(value) => handleChange('cursor', value)}
        disabled={disabled}
      >
        <SelectTrigger>
          <SelectValue placeholder="Con trỏ chuột" />
        </SelectTrigger>
        <SelectContent>
          {CURSOR_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Pointer Events & User Select */}
      <div className="flex gap-2">
        <Select 
          value={currentState.pointerEvents} 
          onValueChange={(value) => handleChange('pointerEvents', value)}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Tương tác chuột" />
          </SelectTrigger>
          <SelectContent>
            {POINTER_EVENTS_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select 
          value={currentState.userSelect} 
          onValueChange={(value) => handleChange('userSelect', value)}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Chọn text" />
          </SelectTrigger>
          <SelectContent>
            {USER_SELECT_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Touch Action */}
      <Select 
        value={currentState.touchAction} 
        onValueChange={(value) => handleChange('touchAction', value)}
        disabled={disabled}
      >
        <SelectTrigger>
          <SelectValue placeholder="Hành động cảm ứng" />
        </SelectTrigger>
        <SelectContent>
          {TOUCH_ACTION_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export const InteractionSection = React.memo(InteractionSectionComponent);
