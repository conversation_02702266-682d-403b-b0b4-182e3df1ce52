import React from "react";
import { Label } from "@/components/ui/label";
import { TreeNode } from "../../../../context/types";
import { GapPopover } from "../../inputs/GapPopover";
import { JustifyAlignGrid } from "../../inputs/JustifyAlignGrid";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface LayoutSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

export const LayoutSection: React.FC<LayoutSectionProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  // Detect current layout mode from className
  const getLayoutMode = (): string => {
    const hasFlex =
      currentClassName.includes("flex") &&
      !currentClassName.includes("inline-flex");
    const hasFlexCol = currentClassName.includes("flex-col");
    const hasFlexRow = currentClassName.includes("flex-row");
    const hasFlexWrap = currentClassName.includes("flex-wrap");

    // Valid combinations require both flex and direction/wrap
    if (hasFlex && hasFlexCol) return "vertical";
    if (hasFlex && hasFlexWrap) return "wrap";
    if (hasFlex && hasFlexRow) return "horizontal";
    if (hasFlex && !hasFlexCol && !hasFlexRow && !hasFlexWrap)
      return "horizontal"; // flex alone = horizontal

    return "default";
  };

  const currentLayoutMode = getLayoutMode();

  // Extract current gap values
  const extractCurrentGap = (
    gapType: "gap" | "gap-x" | "gap-y",
    defaultValue: string = "none"
  ) => {
    let pattern: RegExp;
    switch (gapType) {
      case "gap":
        pattern = /\bgap-(?!x-|y-)[^\s]+/;
        break;
      case "gap-x":
        pattern = /\bgap-x-[^\s]+/;
        break;
      case "gap-y":
        pattern = /\bgap-y-[^\s]+/;
        break;
    }
    const match = currentClassName.match(pattern);
    return match ? match[0] : defaultValue;
  };

  const currentGap = extractCurrentGap("gap", "none");
  const currentGapX = extractCurrentGap("gap-x", "none");
  const currentGapY = extractCurrentGap("gap-y", "none");

  // Handle gap updates with mutual exclusion logic
  const updateGap = (newClass: string) => {
    let updatedClassName = currentClassName;

    // Remove all gap classes first
    updatedClassName = updatedClassName
      .replace(/\bgap-(?!x-|y-)[^\s]+/g, "")
      .replace(/\bgap-x-[^\s]+/g, "")
      .replace(/\bgap-y-[^\s]+/g, "")
      .trim();

    // Add new class if provided and not 'none'
    if (newClass && newClass !== "none") {
      updatedClassName = `${updatedClassName} ${newClass}`.trim();
    }

    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\s+/g, " ").trim();

    onChange({
      style: {
        ...node.style,
        className: updatedClassName,
      },
    });
  };

  // Handle layout mode change
  const handleLayoutModeChange = (mode: string) => {
    let updatedClassName = currentClassName;

    // Remove existing layout and gap classes - specific first, then general
    updatedClassName = updatedClassName
      .replace(/\bflex-col\b/g, "")
      .replace(/\bflex-row\b/g, "")
      .replace(/\bflex-wrap\b/g, "")
      .replace(/\bflex\b/g, "") // Remove flex after removing compounds
      .replace(/\bjustify-(start|end|center|between|around|evenly)\b/g, "")
      .replace(/\bitems-(start|end|center|baseline|stretch)\b/g, "")
      .replace(/\bgap-(?!x-|y-)[^\s]+/g, "")
      .replace(/\bgap-x-[^\s]+/g, "")
      .replace(/\bgap-y-[^\s]+/g, "")
      .trim();

    // Add new layout classes based on mode
    switch (mode) {
      case "horizontal":
        updatedClassName = `${updatedClassName} flex flex-row`.trim();
        break;
      case "vertical":
        updatedClassName = `${updatedClassName} flex flex-col`.trim();
        break;
      case "wrap":
        updatedClassName = `${updatedClassName} flex flex-wrap`.trim();
        break;
      case "default":
      default:
        // All classes already removed above
        break;
    }

    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\s+/g, " ").trim();

    onChange({
      style: {
        ...node.style,
        className: updatedClassName,
      },
    });
  };

  const hasAdvancedLayout = currentLayoutMode !== "default";

  return (
    <div className="space-y-4">
      {/* Layout Mode Selector */}
      <div className="space-y-3">
        <Select
          value={currentLayoutMode}
          onValueChange={handleLayoutModeChange}
          disabled={disabled}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Chọn chế độ bố cục" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="default">Mặc định</SelectItem>
            <SelectItem value="horizontal">Ngang (Flexbox)</SelectItem>
            <SelectItem value="vertical">Dọc (Flexbox)</SelectItem>
            <SelectItem value="wrap">Xuống dòng (Flex wrap)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Advanced Layout Controls - only show for flex modes */}
      {hasAdvancedLayout && (
        <>
          {/* Justify & Align Grid */}
          <div className="space-y-3">
            <Label className="text-xs" role="heading" aria-level={4}>
              Căn chỉnh nội dung
            </Label>
            <JustifyAlignGrid
              className={currentClassName}
              onChange={(newClassName) => {
                onChange({
                  style: {
                    ...node.style,
                    className: newClassName,
                  },
                });
              }}
              disabled={disabled}
            />
          </div>

          {/* Gap Controls */}
          <div className="space-y-3">
            <Label className="text-xs" role="heading" aria-level={4}>
              Khoảng cách
            </Label>

            <div className="space-y-2">
              <div className="space-y-2">
                <Label htmlFor="gap-all" className="text-xs text-gray-600">
                  Chung
                </Label>
                <GapPopover
                  value={currentGap}
                  onValueChange={(value) => updateGap(value)}
                  placeholder="Chọn khoảng cách"
                  disabled={
                    disabled || currentGapX !== "none" || currentGapY !== "none"
                  }
                  type="gap"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gap-x" className="text-xs text-gray-600">
                  Ngang
                </Label>
                <GapPopover
                  value={currentGapX}
                  onValueChange={(value) => updateGap(value)}
                  placeholder="Chọn khoảng cách ngang"
                  disabled={
                    disabled || currentGap !== "none" || currentGapY !== "none"
                  }
                  type="gap-x"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gap-y" className="text-xs text-gray-600">
                  Dọc
                </Label>
                <GapPopover
                  value={currentGapY}
                  onValueChange={(value) => updateGap(value)}
                  placeholder="Chọn khoảng cách dọc"
                  disabled={
                    disabled || currentGap !== "none" || currentGapX !== "none"
                  }
                  type="gap-y"
                />
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
