import React from "react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TreeNode } from "../../../../context/types";
import { MarginPopover } from "../../inputs/MarginPopover";
import {
  MARGIN_GROUPS,
  MARGIN_X_GROUPS,
  MARGIN_Y_GROUPS,
  MARGIN_T_GROUPS,
  MARGIN_R_GROUPS,
  MARGIN_B_GROUPS,
  MARGIN_L_GROUPS,
  MARGIN_S_GROUPS,
  MARGIN_E_GROUPS,
} from "../../utils/marginGroups";

interface MarginSectionProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled?: boolean;
}

type MarginMode =
  | "default"
  | "all"
  | "vertical"
  | "horizontal"
  | "individual"
  | "start"
  | "end";

export const MarginSection: React.FC<MarginSectionProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const currentClassName = node.style?.className || "";

  // Detect current margin mode from className
  const getMarginMode = (): MarginMode => {
    const hasM = /(?:^|\s)m-[^\s]+/.test(currentClassName);
    const hasMx = /(?:^|\s)mx-[^\s]+/.test(currentClassName);
    const hasMy = /(?:^|\s)my-[^\s]+/.test(currentClassName);
    const hasMt = /(?:^|\s)mt-[^\s]+/.test(currentClassName);
    const hasMr = /(?:^|\s)mr-[^\s]+/.test(currentClassName);
    const hasMb = /(?:^|\s)mb-[^\s]+/.test(currentClassName);
    const hasMl = /(?:^|\s)ml-[^\s]+/.test(currentClassName);
    const hasMs = /(?:^|\s)ms-[^\s]+/.test(currentClassName);
    const hasMe = /(?:^|\s)me-[^\s]+/.test(currentClassName);

    // Also check for negative margins
    const hasNegM = /(?:^|\s)-m-[^\s]+/.test(currentClassName);
    const hasNegMx = /(?:^|\s)-mx-[^\s]+/.test(currentClassName);
    const hasNegMy = /(?:^|\s)-my-[^\s]+/.test(currentClassName);
    const hasNegMt = /(?:^|\s)-mt-[^\s]+/.test(currentClassName);
    const hasNegMr = /(?:^|\s)-mr-[^\s]+/.test(currentClassName);
    const hasNegMb = /(?:^|\s)-mb-[^\s]+/.test(currentClassName);
    const hasNegMl = /(?:^|\s)-ml-[^\s]+/.test(currentClassName);
    const hasNegMs = /(?:^|\s)-ms-[^\s]+/.test(currentClassName);
    const hasNegMe = /(?:^|\s)-me-[^\s]+/.test(currentClassName);

    // Check for start mode
    if ((hasMs || hasNegMs) && !(hasMe || hasNegMe)) return "start";

    // Check for end mode
    if ((hasMe || hasNegMe) && !(hasMs || hasNegMs)) return "end";

    // If both ms and me exist, prioritize start
    if ((hasMs || hasNegMs) && (hasMe || hasNegMe)) return "start";

    // Check for individual mode (any combination of mt/mr/mb/ml)
    if (
      hasMt ||
      hasMr ||
      hasMb ||
      hasMl ||
      hasNegMt ||
      hasNegMr ||
      hasNegMb ||
      hasNegMl
    )
      return "individual";

    // Check for axis modes
    if (hasMx || hasMy || hasNegMx || hasNegMy) {
      const hasXAxis = hasMx || hasNegMx;
      const hasYAxis = hasMy || hasNegMy;
      return hasXAxis && hasYAxis
        ? "individual"
        : hasXAxis
        ? "horizontal"
        : "vertical";
    }

    // Check for all mode
    if (hasM || hasNegM) return "all";

    return "default";
  };

  const currentMode = getMarginMode();

  // Extract current margin values (including negative and auto)
  const extractMarginValue = (
    pattern: RegExp,
    defaultValue: string = "none"
  ): string => {
    const match = currentClassName.match(pattern);
    return match ? match[1] : defaultValue;
  };

  const currentM = extractMarginValue(/(?:^|\s)(-?m-[^\s]+)/);
  const currentMx = extractMarginValue(/(?:^|\s)(-?mx-[^\s]+)/);
  const currentMy = extractMarginValue(/(?:^|\s)(-?my-[^\s]+)/);
  const currentMt = extractMarginValue(/(?:^|\s)(-?mt-[^\s]+)/);
  const currentMr = extractMarginValue(/(?:^|\s)(-?mr-[^\s]+)/);
  const currentMb = extractMarginValue(/(?:^|\s)(-?mb-[^\s]+)/);
  const currentMl = extractMarginValue(/(?:^|\s)(-?ml-[^\s]+)/);
  const currentMs = extractMarginValue(/(?:^|\s)(-?ms-[^\s]+)/);
  const currentMe = extractMarginValue(/(?:^|\s)(-?me-[^\s]+)/);

  // Handle mode change - clean all margin classes and switch mode
  const handleModeChange = (mode: MarginMode) => {
    let updatedClassName = currentClassName;

    // Remove all existing margin classes (including negative)
    updatedClassName = updatedClassName
      .replace(/(?:^|\s)-?m-[^\s]+/g, "")
      .replace(/(?:^|\s)-?mx-[^\s]+/g, "")
      .replace(/(?:^|\s)-?my-[^\s]+/g, "")
      .replace(/(?:^|\s)-?mt-[^\s]+/g, "")
      .replace(/(?:^|\s)-?mr-[^\s]+/g, "")
      .replace(/(?:^|\s)-?mb-[^\s]+/g, "")
      .replace(/(?:^|\s)-?ml-[^\s]+/g, "")
      .replace(/(?:^|\s)-?ms-[^\s]+/g, "")
      .replace(/(?:^|\s)-?me-[^\s]+/g, "")
      .trim();

    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\s+/g, " ").trim();

    // Add default margin for the new mode (except 'default' mode)
    if (mode !== "default") {
      switch (mode) {
        case "all":
          updatedClassName = `${updatedClassName} m-4`.trim();
          break;
        case "vertical":
          updatedClassName = `${updatedClassName} my-4`.trim();
          break;
        case "horizontal":
          updatedClassName = `${updatedClassName} mx-4`.trim();
          break;
        case "individual":
          updatedClassName = `${updatedClassName} mt-4 mr-4 mb-4 ml-4`.trim();
          break;
        case "start":
          updatedClassName = `${updatedClassName} ms-4`.trim();
          break;
        case "end":
          updatedClassName = `${updatedClassName} me-4`.trim();
          break;
      }
    }

    onChange({
      style: {
        ...node.style,
        className: updatedClassName,
      },
    });
  };

  // Update specific margin value
  const updateMargin = (newValue: string, marginType: string) => {
    let updatedClassName = currentClassName;

    // Define pattern to remove based on margin type (including negative)
    let removePattern: RegExp;
    switch (marginType) {
      case "m":
        removePattern = /(?:^|\s)-?m-[^\s]+/g;
        break;
      case "mx":
        removePattern = /(?:^|\s)-?mx-[^\s]+/g;
        break;
      case "my":
        removePattern = /(?:^|\s)-?my-[^\s]+/g;
        break;
      case "mt":
        removePattern = /(?:^|\s)-?mt-[^\s]+/g;
        break;
      case "mr":
        removePattern = /(?:^|\s)-?mr-[^\s]+/g;
        break;
      case "mb":
        removePattern = /(?:^|\s)-?mb-[^\s]+/g;
        break;
      case "ml":
        removePattern = /(?:^|\s)-?ml-[^\s]+/g;
        break;
      case "ms":
        removePattern = /(?:^|\s)-?ms-[^\s]+/g;
        break;
      case "me":
        removePattern = /(?:^|\s)-?me-[^\s]+/g;
        break;
      default:
        return;
    }

    // Remove existing class
    updatedClassName = updatedClassName.replace(removePattern, "").trim();

    // Add new class if not 'none'
    if (newValue && newValue !== "none") {
      updatedClassName = `${updatedClassName} ${newValue}`.trim();
    }

    // Clean up extra spaces
    updatedClassName = updatedClassName.replace(/\s+/g, " ").trim();

    onChange({
      style: {
        ...node.style,
        className: updatedClassName,
      },
    });
  };

  return (
    <div className="space-y-4">
      {/* Mode Selector */}
      <div className="space-y-3">
        <Select
          value={currentMode}
          onValueChange={handleModeChange}
          disabled={disabled}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Chọn loại lề ngoài" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="default">Mặc định (không có)</SelectItem>
            <SelectItem value="all">Toàn bộ (4 cạnh)</SelectItem>
            <SelectItem value="vertical">Trên dưới (2 cạnh)</SelectItem>
            <SelectItem value="horizontal">Trái phải (2 cạnh)</SelectItem>
            <SelectItem value="individual">Độc lập (từng cạnh)</SelectItem>
            <SelectItem value="start">Lề bắt đầu</SelectItem>
            <SelectItem value="end">Lề kết thúc</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Margin Controls based on mode */}
      {currentMode === "all" && (
        <div className="space-y-2">
          <Label htmlFor="margin-all" className="text-xs text-gray-600">
            Toàn bộ
          </Label>
          <MarginPopover
            id="margin-all"
            value={currentM}
            onValueChange={(value) => updateMargin(value, "m")}
            placeholder="Chọn lề ngoài"
            disabled={disabled}
            groups={MARGIN_GROUPS}
          />
        </div>
      )}

      {currentMode === "vertical" && (
        <div className="space-y-2">
          <Label htmlFor="margin-vertical" className="text-xs text-gray-600">
            Trên dưới
          </Label>
          <MarginPopover
            id="margin-vertical"
            value={currentMy}
            onValueChange={(value) => updateMargin(value, "my")}
            placeholder="Chọn lề ngoài trên dưới"
            disabled={disabled}
            groups={MARGIN_Y_GROUPS}
          />
        </div>
      )}

      {currentMode === "horizontal" && (
        <div className="space-y-2">
          <Label htmlFor="margin-horizontal" className="text-xs text-gray-600">
            Trái phải
          </Label>
          <MarginPopover
            id="margin-horizontal"
            value={currentMx}
            onValueChange={(value) => updateMargin(value, "mx")}
            placeholder="Chọn lề ngoài trái phải"
            disabled={disabled}
            groups={MARGIN_X_GROUPS}
          />
        </div>
      )}

      {currentMode === "individual" && (
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2">
            <Label htmlFor="margin-top" className="text-xs text-gray-600">
              Trên
            </Label>
            <MarginPopover
              id="margin-top"
              value={currentMt}
              onValueChange={(value) => updateMargin(value, "mt")}
              placeholder="Lề trên"
              disabled={disabled}
              groups={MARGIN_T_GROUPS}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="margin-right" className="text-xs text-gray-600">
              Phải
            </Label>
            <MarginPopover
              id="margin-right"
              value={currentMr}
              onValueChange={(value) => updateMargin(value, "mr")}
              placeholder="Lề phải"
              disabled={disabled}
              groups={MARGIN_R_GROUPS}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="margin-bottom" className="text-xs text-gray-600">
              Dưới
            </Label>
            <MarginPopover
              id="margin-bottom"
              value={currentMb}
              onValueChange={(value) => updateMargin(value, "mb")}
              placeholder="Lề dưới"
              disabled={disabled}
              groups={MARGIN_B_GROUPS}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="margin-left" className="text-xs text-gray-600">
              Trái
            </Label>
            <MarginPopover
              id="margin-left"
              value={currentMl}
              onValueChange={(value) => updateMargin(value, "ml")}
              placeholder="Lề trái"
              disabled={disabled}
              groups={MARGIN_L_GROUPS}
            />
          </div>
        </div>
      )}

      {currentMode === "start" && (
        <div className="space-y-2">
          <Label htmlFor="margin-start" className="text-xs text-gray-600">
            Lề bắt đầu
          </Label>
          <MarginPopover
            id="margin-start"
            value={currentMs}
            onValueChange={(value) => updateMargin(value, "ms")}
            placeholder="Chọn lề bắt đầu"
            disabled={disabled}
            groups={MARGIN_S_GROUPS}
          />
          <p className="text-xs text-gray-500">Trái (LTR) / Phải (RTL)</p>
        </div>
      )}

      {currentMode === "end" && (
        <div className="space-y-2">
          <Label htmlFor="margin-end" className="text-xs text-gray-600">
            Lề kết thúc
          </Label>
          <MarginPopover
            id="margin-end"
            value={currentMe}
            onValueChange={(value) => updateMargin(value, "me")}
            placeholder="Chọn lề kết thúc"
            disabled={disabled}
            groups={MARGIN_E_GROUPS}
          />
          <p className="text-xs text-gray-500">Phải (LTR) / Trái (RTL)</p>
        </div>
      )}
    </div>
  );
};
