import React, { useCallback, useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface ResizeHandleProps {
  onResize: (delta: number) => void;
  className?: string;
  direction?: 'horizontal' | 'vertical';
  style?: React.CSSProperties;
  disabled?: boolean;
}

/**
 * ResizeHandle component for resizing panels
 * 
 * Features:
 * - Smooth mouse drag interactions
 * - Prevents text selection during drag
 * - Configurable direction (horizontal/vertical)
 * - Visual feedback with hover states
 * - Jitter prevention with minimum delta threshold
 */

export const ResizeHandle: React.FC<ResizeHandleProps> = ({
  onResize,
  className,
  direction = 'vertical',
  style,
  disabled = false
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [startPos, setStartPos] = useState(0);
  const handleRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (disabled || e.button !== 0) return; // Only left click and not disabled
    
    e.preventDefault();
    e.stopPropagation(); // Prevent tree drag events
    
    setIsDragging(true);
    setStartPos(direction === 'vertical' ? e.clientX : e.clientY);
    
    // Prevent text selection during drag
    document.body.style.userSelect = 'none';
    document.body.style.cursor = direction === 'vertical' ? 'col-resize' : 'row-resize';
  }, [direction, disabled]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;
    
    e.preventDefault();
    
    const currentPos = direction === 'vertical' ? e.clientX : e.clientY;
    const delta = currentPos - startPos;
    
    // Only resize if delta is significant enough to avoid jitter
    if (Math.abs(delta) >= 1) {
      onResize(delta);
      setStartPos(currentPos);
    }
  }, [isDragging, startPos, direction, onResize]);

  const handleMouseUp = useCallback(() => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    // Restore cursor and text selection
    document.body.style.userSelect = '';
    document.body.style.cursor = '';
  }, [isDragging]);

  // Global mouse events for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div
      ref={handleRef}
      className={cn(
        'select-none transition-colors duration-150',
        direction === 'vertical' 
          ? 'w-1 cursor-col-resize hover:bg-blue-500/20 active:bg-blue-500/40'
          : 'h-1 cursor-row-resize hover:bg-blue-500/20 active:bg-blue-500/40',
        isDragging && 'bg-blue-500/40',
        className
      )}
      onMouseDown={handleMouseDown}
      style={{
        // Expand hit area for better UX
        ...(direction === 'vertical' 
          ? { marginLeft: '-2px', marginRight: '-2px', paddingLeft: '2px', paddingRight: '2px' }
          : { marginTop: '-2px', marginBottom: '-2px', paddingTop: '2px', paddingBottom: '2px' }
        ),
        ...style
      }}
    >
      {/* Visual indicator */}
      <div 
        className={cn(
          'bg-gray-300 transition-colors duration-150',
          direction === 'vertical' ? 'w-px h-full mx-auto' : 'h-px w-full my-auto',
          'hover:bg-blue-500',
          isDragging && 'bg-blue-500'
        )}
      />
    </div>
  );
};