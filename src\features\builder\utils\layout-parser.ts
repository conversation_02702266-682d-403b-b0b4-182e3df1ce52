/**
 * Layout utility functions for parsing and manipulating className
 */

export type LayoutType = "default" | "horizontal" | "vertical" | "wrap";

/**
 * Extract layout type from className
 */
export const getLayoutFromClassName = (className: string = ""): LayoutType => {
  const classes = className.split(/\s+/).filter(Boolean);

  if (classes.includes("flex-row")) return "horizontal";
  if (classes.includes("flex-col")) return "vertical";
  if (classes.includes("flex-wrap")) return "wrap";
  return "default";
};

/**
 * Remove all layout-related classes from className
 */
export const removeLayoutClasses = (className: string = ""): string => {
  const layoutClasses = ["flex", "flex-row", "flex-col", "flex-wrap"];

  return className
    .split(/\s+/)
    .filter((cls) => cls && !layoutClasses.includes(cls))
    .join(" ");
};

/**
 * Add layout classes based on layout type
 */
export const addLayoutClasses = (
  baseClassName: string,
  layoutType: LayoutType
): string => {
  const cleanClassName = removeLayoutClasses(baseClassName);

  const layoutClasses: Record<LayoutType, string[]> = {
    default: [],
    horizontal: ["flex", "flex-row"],
    vertical: ["flex", "flex-col"],
    wrap: ["flex", "flex-wrap"],
  };

  const newClasses = layoutClasses[layoutType];
  const allClasses = [
    ...cleanClassName.split(/\s+/).filter(Boolean),
    ...newClasses,
  ];

  return allClasses.join(" ");
};

/**
 * Update className with new layout type
 */
export const updateLayoutClassName = (
  currentClassName: string = "",
  layoutType: LayoutType
): string => {
  return addLayoutClasses(currentClassName, layoutType);
};
