import { Post, TreeNode, TreeNodeType, ViewMode, EditorMode, BuilderState, Persist<PERSON><PERSON>State, Temporary<PERSON>State, Build<PERSON><PERSON>State } from './types';

// Base action types
export const ACTION_TYPES = {
  // Post actions
  SET_POST: 'SET_POST',
  SET_POST_WITH_PRESERVED_UI: 'SET_POST_WITH_PRESERVED_UI',
  UPDATE_POST: 'UPDATE_POST',
  
  // Node actions
  SELECT_NODE: 'SELECT_NODE',
  TOGGLE_NODE_EXPANSION: 'TOGGLE_NODE_EXPANSION',
  ADD_NODE: 'ADD_NODE',
  UPDATE_NODE: 'UPDATE_NODE',
  DELETE_NODE: 'DELETE_NODE',
  MOVE_NODE: 'MOVE_NODE',
  
  // UI actions
  SET_VIEW_MODE: 'SET_VIEW_MODE',
  SET_EDITOR_MODE: 'SET_EDITOR_MODE',
  SET_DIRTY: 'SET_DIRTY',
  SET_EXPANDED_NODES: 'SET_EXPANDED_NODES',
  SET_LAST_SAVED: 'SET_LAST_SAVED',
  TOGGLE_TREE_PANEL: 'TOGGLE_TREE_PANEL',
  TOGGLE_PROPERTIES_PANEL: 'TOGGLE_PROPERTIES_PANEL',
  
  // Enhanced UI state actions
  SET_UI_STATE: 'SET_UI_STATE',
  SET_PERSISTENT_UI_STATE: 'SET_PERSISTENT_UI_STATE',
  SET_TEMPORARY_UI_STATE: 'SET_TEMPORARY_UI_STATE',
  
  // Copy/Paste actions
  COPY_NODE: 'COPY_NODE',
  PASTE_NODE: 'PASTE_NODE',
  
  // History actions
  UNDO: 'UNDO',
  REDO: 'REDO',
  RESTORE_STATE: 'RESTORE_STATE',
} as const;

// Action type definitions
export type BuilderAction =
  | { type: typeof ACTION_TYPES.SET_POST; payload: Post }
  | { type: typeof ACTION_TYPES.SET_POST_WITH_PRESERVED_UI; payload: Post }
  | { type: typeof ACTION_TYPES.UPDATE_POST; payload: Partial<Post> }
  | { type: typeof ACTION_TYPES.SELECT_NODE; payload: string | null }
  | { type: typeof ACTION_TYPES.TOGGLE_NODE_EXPANSION; payload: string }
  | { type: typeof ACTION_TYPES.ADD_NODE; payload: { 
      parentId: string; 
      nodeType: TreeNodeType; 
      index?: number 
    }}
  | { type: typeof ACTION_TYPES.UPDATE_NODE; payload: { 
      nodeId: string; 
      updates: Partial<TreeNode> 
    }}
  | { type: typeof ACTION_TYPES.DELETE_NODE; payload: string }
  | { type: typeof ACTION_TYPES.MOVE_NODE; payload: { 
      nodeId: string; 
      newParentId: string; 
      index: number 
    }}
  | { type: typeof ACTION_TYPES.SET_VIEW_MODE; payload: ViewMode }
  | { type: typeof ACTION_TYPES.SET_EDITOR_MODE; payload: EditorMode }
  | { type: typeof ACTION_TYPES.SET_DIRTY; payload: boolean }
  | { type: typeof ACTION_TYPES.COPY_NODE; payload: string }
  | { type: typeof ACTION_TYPES.PASTE_NODE }
  | { type: typeof ACTION_TYPES.SET_EXPANDED_NODES; payload: Set<string> }
  | { type: typeof ACTION_TYPES.SET_LAST_SAVED; payload: number }
  | { type: typeof ACTION_TYPES.RESTORE_STATE; payload: BuilderState }
  | { type: typeof ACTION_TYPES.TOGGLE_TREE_PANEL }
  | { type: typeof ACTION_TYPES.TOGGLE_PROPERTIES_PANEL }
  | { type: typeof ACTION_TYPES.SET_UI_STATE; payload: Partial<BuilderUIState> }
  | { type: typeof ACTION_TYPES.SET_PERSISTENT_UI_STATE; payload: Partial<PersistentUIState> }
  | { type: typeof ACTION_TYPES.SET_TEMPORARY_UI_STATE; payload: Partial<TemporaryUIState> };

// Action creators
export const actions = {
  setPost: (post: Post): BuilderAction => ({
    type: ACTION_TYPES.SET_POST,
    payload: post,
  }),
  
  setPostWithPreservedUI: (post: Post): BuilderAction => ({
    type: ACTION_TYPES.SET_POST_WITH_PRESERVED_UI,
    payload: post,
  }),
  
  updatePost: (updates: Partial<Post>): BuilderAction => ({
    type: ACTION_TYPES.UPDATE_POST,
    payload: updates,
  }),
  
  selectNode: (nodeId: string | null): BuilderAction => ({
    type: ACTION_TYPES.SELECT_NODE,
    payload: nodeId,
  }),
  
  toggleNodeExpansion: (nodeId: string): BuilderAction => ({
    type: ACTION_TYPES.TOGGLE_NODE_EXPANSION,
    payload: nodeId,
  }),
  
  addNode: (parentId: string, nodeType: TreeNodeType, index?: number): BuilderAction => ({
    type: ACTION_TYPES.ADD_NODE,
    payload: { parentId, nodeType, index },
  }),
  
  updateNode: (nodeId: string, updates: Partial<TreeNode>): BuilderAction => ({
    type: ACTION_TYPES.UPDATE_NODE,
    payload: { nodeId, updates },
  }),
  
  deleteNode: (nodeId: string): BuilderAction => ({
    type: ACTION_TYPES.DELETE_NODE,
    payload: nodeId,
  }),
  
  moveNode: (nodeId: string, newParentId: string, index: number): BuilderAction => ({
    type: ACTION_TYPES.MOVE_NODE,
    payload: { nodeId, newParentId, index },
  }),
  
  setViewMode: (mode: ViewMode): BuilderAction => ({
    type: ACTION_TYPES.SET_VIEW_MODE,
    payload: mode,
  }),
  
  setEditorMode: (mode: EditorMode): BuilderAction => ({
    type: ACTION_TYPES.SET_EDITOR_MODE,
    payload: mode,
  }),
  
  setDirty: (isDirty: boolean): BuilderAction => ({
    type: ACTION_TYPES.SET_DIRTY,
    payload: isDirty,
  }),
  
  setExpandedNodes: (nodes: Set<string>): BuilderAction => ({
    type: ACTION_TYPES.SET_EXPANDED_NODES,
    payload: nodes,
  }),
  
  setLastSaved: (timestamp: number): BuilderAction => ({
    type: ACTION_TYPES.SET_LAST_SAVED,
    payload: timestamp,
  }),
  
  restoreState: (state: BuilderState): BuilderAction => ({
    type: ACTION_TYPES.RESTORE_STATE,
    payload: state,
  }),
  
  toggleTreePanel: (): BuilderAction => ({
    type: ACTION_TYPES.TOGGLE_TREE_PANEL,
  }),
  
  togglePropertiesPanel: (): BuilderAction => ({
    type: ACTION_TYPES.TOGGLE_PROPERTIES_PANEL,
  }),
  
  copyNode: (nodeId: string): BuilderAction => ({
    type: ACTION_TYPES.COPY_NODE,
    payload: nodeId,
  }),
  
  pasteNode: (): BuilderAction => ({
    type: ACTION_TYPES.PASTE_NODE,
  }),
  
  // Enhanced UI state actions
  setUIState: (updates: Partial<BuilderUIState>): BuilderAction => ({
    type: ACTION_TYPES.SET_UI_STATE,
    payload: updates,
  }),
  
  setPersistentUIState: (updates: Partial<PersistentUIState>): BuilderAction => ({
    type: ACTION_TYPES.SET_PERSISTENT_UI_STATE,
    payload: updates,
  }),
  
  setTemporaryUIState: (updates: Partial<TemporaryUIState>): BuilderAction => ({
    type: ACTION_TYPES.SET_TEMPORARY_UI_STATE,
    payload: updates,
  }),
};

// Check if action should be recorded in history
export const shouldRecordInHistory = (action: BuilderAction): boolean => {
  const nonHistoryActions: string[] = [
    ACTION_TYPES.SET_DIRTY,
    ACTION_TYPES.SET_LAST_SAVED,
    ACTION_TYPES.RESTORE_STATE,
    ACTION_TYPES.SET_POST_WITH_PRESERVED_UI, // Don't record when restoring
    ACTION_TYPES.SET_EDITOR_MODE, // Session-only
    ACTION_TYPES.TOGGLE_NODE_EXPANSION, // Too frequent
    ACTION_TYPES.SET_EXPANDED_NODES, // Too frequent
    ACTION_TYPES.SET_UI_STATE, // Handled separately
  ];
  
  return !nonHistoryActions.includes(action.type);
};

// Get action description for history
export const getActionDescription = (action: BuilderAction): string => {
  switch (action.type) {
    case ACTION_TYPES.ADD_NODE:
      return `Add ${action.payload.nodeType} node`;
    case ACTION_TYPES.UPDATE_NODE:
      return 'Update node';
    case ACTION_TYPES.DELETE_NODE:
      return 'Delete node';
    case ACTION_TYPES.MOVE_NODE:
      return 'Move node';
    case ACTION_TYPES.UPDATE_POST:
      return 'Update post';
    case ACTION_TYPES.SET_TEMPORARY_UI_STATE:
      return 'Change UI settings';
    case ACTION_TYPES.SET_PERSISTENT_UI_STATE:
      return 'Change panel settings';
    case ACTION_TYPES.SET_VIEW_MODE:
      return 'Change view mode';
    default:
      return action.type;
  }
};